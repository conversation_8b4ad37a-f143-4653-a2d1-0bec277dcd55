import { useState, useEffect, useRef } from "react"

const CreateWorkspaceModal = ({ isOpen, onClose, onSubmit }) => {
  const [workspaceName, setWorkspaceName] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const modalRef = useRef(null)

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose])

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setWorkspaceName("")
      setIsSubmitting(false)
    }
  }, [isOpen])

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!workspaceName.trim()) {
      return
    }

    setIsSubmitting(true)

    try {
      const success = await onSubmit(workspaceName.trim())
      if (success) {
        setWorkspaceName("")
        onClose()
      }
    } catch (error) {
      console.error("Error creating workspace:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 font-noto">
      <div ref={modalRef} className="bg-white rounded-[8px] shadow-lg border border-[#D9D9D9]" style={{ width: '634px', height: '221px' }}>
        {/* Modal Header */}
        <div className="h-[61px] flex items-center px-5 border-b border-[#D9D9D9]" style={{ borderTopLeftRadius: '12px', borderTopRightRadius: '12px' }}>
          <h2 className="text-xl font-semibold text-gray-900">Create Workspace</h2>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-5">
          {/* Workspace Name Field */}
          <div className="mb-4">
            <label 
              htmlFor="workspace-name" 
              className="block mb-2 text-sm font-semibold text-[#252525]"
            >
              Workspace Name
            </label>
            <input
              id="workspace-name"
              type="text"
              value={workspaceName}
              onChange={(e) => setWorkspaceName(e.target.value)}
              placeholder="Enter Name"
              className="bg-white border border-[#D9D9D9] rounded-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090] focus:border-[#352090] text-sm w-full px-3"
              style={{ 
                height: '44px'
              }}
              disabled={isSubmitting}
              required
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="bg-white border border-[#D9D9D9] rounded-[8px] text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#352090] disabled:opacity-50 disabled:cursor-not-allowed transition-colors px-8"
              style={{ 
                height: '44px'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!workspaceName.trim() || isSubmitting}
              className="bg-[#352090] rounded-[8px] text-sm font-medium text-white hover:bg-[#2a1a70] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#352090] disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2 px-8"
              style={{ 
                height: '44px'
              }}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </>
              ) : (
                "Save"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CreateWorkspaceModal
