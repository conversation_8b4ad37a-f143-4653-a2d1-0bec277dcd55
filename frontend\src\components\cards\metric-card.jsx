const MetricCard = ({
  title,
  value,
  comparisonValue,
  color,
  onToggle,
  isChecked = true,
  disabled = false,
  dateRange = "Last 28 days",
}) => {
  // Define background colors for checked state
  const bgColors = {
    blue: "#4285F4",
    purple: "#7B61FF", // Updated to match the table header color
    green: "#00A389", // Updated to match the table header color
    orange: "#FF9F43", // Updated to match the table header color
  }

  // Define text colors for better contrast
  const textColors = {
    blue: "text-white",
    purple: "text-white",
    green: "text-white",
    orange: "text-white",
  }

  const handleToggle = () => {
    if (disabled) return
    if (onToggle) {
      onToggle(!isChecked)
    }
  }

  const hasComparison = comparisonValue !== null && comparisonValue !== undefined

  // Parse date range text for comparison view
  const [currentRange, comparisonRange] = hasComparison && dateRange.includes(" vs ")
    ? dateRange.split(" vs ")
    : [dateRange, dateRange]

  return (
    <div
      className={`rounded-[20px] relative overflow-hidden flex flex-col p-4 transition-all duration-200 border ${
        isChecked ? 'border-white' : 'border-[#DCDCDC]'
      }`}
      style={{
        height: hasComparison ? '200px' : '160px',
        width: hasComparison ? '240px' : '200px',
        minWidth: hasComparison ? '240px' : '200px',
        backgroundColor: isChecked ? bgColors[color] : '#FFFFFF'
      }}
    >
      {/* Header with Checkbox and Title */}
      <div className="flex items-center justify-between">
        <label className={`inline-flex items-center ${disabled ? "cursor-not-allowed opacity-60" : "cursor-pointer"}`}>
          <input type="checkbox" className="sr-only" checked={isChecked} onChange={handleToggle} disabled={disabled} />
          <div
            className={`w-4 h-4 rounded border flex items-center justify-center ${
              isChecked 
                ? "bg-white border-white" 
                : "bg-transparent border-[#9A9A9A]"
            }`}
          >
            {isChecked && (
              <svg width="10" height="8" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M1 4L4.5 7.5L11 1"
                  stroke={bgColors[color]}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </div>
          <span className={`ml-2 text-sm ${isChecked ? textColors[color] : 'text-[#252525]'}`}>{title}</span>
        </label>

        {/* Info Icon */}
        <div className="w-5 h-5 flex items-center justify-center">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11 15H13V17H11V15ZM13 13.3551V14H11V12.5C11 11.9477 11.4477 11.5 12 11.5C12.8284 11.5 13.5 10.8284 13.5 10C13.5 9.17157 12.8284 8.5 12 8.5C11.2723 8.5 10.6656 9.01823 10.5288 9.70577L8.56731 9.31346C8.88637 7.70919 10.302 6.5 12 6.5C13.933 6.5 15.5 8.067 15.5 10C15.5 11.5855 14.4457 12.9248 13 13.3551Z"
              fill={isChecked ? "#FFFFFF" : "#9A9A9A"}
            />
          </svg>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-grow flex flex-col justify-center mt-4">
        {!hasComparison ? (
          // Single value layout
          <div className="flex flex-col items-center">
            <div className={`text-3xl font-bold text-center mb-1 ${
              isChecked ? textColors[color] : 'text-[#252525]'
            }`}>{value}</div>
            <div className={`opacity-80 text-xs ${
              isChecked ? textColors[color] : 'text-[#252525]'
            }`}>{dateRange}</div>
          </div>
        ) : (
          // Comparison layout
          <div className="space-y-2">
            {/* Current Period */}
            <div className="flex flex-col">
              <div className="flex items-center justify-between">
                <div>
                  <div className={`text-2xl font-bold mb-0.5 ${
                    isChecked ? textColors[color] : 'text-[#252525]'
                  }`}>{value}</div>
                  <div className={`opacity-80 text-xs ${
                    isChecked ? textColors[color] : 'text-[#252525]'
                  }`}>{currentRange}</div>
                </div>
                <div className={`w-8 h-[2px] ${
                  isChecked ? 'bg-white' : 'bg-[#252525]'
                }`}></div>
              </div>
            </div>

            {/* Comparison Period */}
            <div className="flex flex-col">
              <div className="flex items-center justify-between">
                <div>
                  <div className={`text-2xl font-bold mb-0.5 ${
                    isChecked ? textColors[color] : 'text-[#252525]'
                  }`}>{comparisonValue}</div>
                  <div className={`opacity-80 text-xs ${
                    isChecked ? textColors[color] : 'text-[#252525]'
                  }`}>{comparisonRange}</div>
                </div>
                <div className={`w-8 border-t-2 border-dashed ${
                  isChecked ? 'border-white' : 'border-[#252525]'
                }`}></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MetricCard
