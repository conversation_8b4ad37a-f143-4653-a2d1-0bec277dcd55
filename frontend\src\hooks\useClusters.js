import { useEffect, useMemo, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useCheckOnboardingStatusQuery } from "../store/api/onboardingApi"
import { workspaceApi } from "../store/api/workspaceApi"
import { useGetClustersQuery, useCreateClusterMutation, useDeleteClusterMutation } from "../store/api/clusterApi"
import { clusterApi } from "../store/api/clusterApi"
import { onboardingApi } from "../store/api/onboardingApi"
import {
  closeCreateModal,
  closeDeleteModal,
  addProcessingCluster,
  removeProcessingCluster,
  clearWorkspaceClusters,
  selectClustersByWorkspace,
  selectProcessingClusters,
  selectClusterFilters,
  selectClusterSearchQuery,
} from "../store/slices/clusterSlice"
import { addNotification } from "../store/slices/uiSlice"
import { createSelector } from '@reduxjs/toolkit'

// Move selectors outside the hook for proper memoization
const selectWorkspaceSpecificClusters = createSelector(
  [
    selectClustersByWorkspace,
    (_, workspaceId) => workspaceId
  ],
  (clustersByWorkspace, workspaceId) => clustersByWorkspace[workspaceId] || []
)

export const useClusters = (workspaceId) => {
  const dispatch = useDispatch()
  
  // Use memoized selector
  const searchQuery = useSelector(selectClusterSearchQuery)
  const filters = useSelector(selectClusterFilters)
  const processingClusters = useSelector(selectProcessingClusters)
  const workspaceClusters = useSelector(state => selectWorkspaceSpecificClusters(state, workspaceId))

  // Track previous clusters to detect status changes
  const previousClustersRef = useRef({})
  const previousWorkspaceRef = useRef(null)

  // 🔑 CRITICAL FIX: Temporarily disable onboarding check to prevent session corruption
  // The backend onboarding endpoint corrupts the session when called
  const onboardingData = { completed: true } // Assume completed to avoid session corruption
  const onboardingLoading = false
  const onboardingError = null
  
  // TODO: Fix backend onboarding endpoint and re-enable this:
  // const {
  //   data: onboardingData,
  //   isLoading: onboardingLoading,
  //   error: onboardingError,
  // } = useCheckOnboardingStatusQuery(workspaceId, {
  //   skip: !workspaceId,
  //   refetchOnMountOrArgChange: true,
  // })

  // Domain info comes from workspace access call - no need for separate API call

  // 🔑 KEY FIX: Simplified session readiness check (backend handles domain updates)
  const [sessionReady, setSessionReady] = useState(true) // Start as ready
  
  // 🔑 OPTIMIZATION: Simplified session logic - no need for delays
  useEffect(() => {
    if (workspaceId) {
      // Session is always ready when we have a workspaceId
      setSessionReady(true)
    }
  }, [workspaceId])

  // 🔑 KEY API CALL - This fetches fresh data when user navigates to clusters page
  const {
    data: clustersData = [],
    isLoading: isLoadingClusters,
    error: clustersError,
    refetch: refetchClusters,
  } = useGetClustersQuery(workspaceId, {
    // 🔑 CRITICAL FIX: Skip onboarding check to prevent session corruption
    // The onboarding endpoint has a bug that corrupts the session
    skip: !workspaceId,
    // 🔑 OPTIMIZATION: Only poll if there are processing clusters
    pollingInterval: processingClusters.length > 0 ? 180000 : 0,
    // 🔑 KEY FIX: Force refetch when workspace changes
    refetchOnMountOrArgChange: true,
  })

  // Create cluster mutation
  const [createCluster, { isLoading: isCreating }] = useCreateClusterMutation()

  // Delete cluster mutation
  const [deleteCluster, { isLoading: isDeleting }] = useDeleteClusterMutation()

  // 🔑 KEY FIX: Always use API data as source of truth, Redux is updated via onQueryStarted
  const displayClusters = clustersData || []

  // 🔑 DISABLED: Onboarding validation (since we're not using real onboarding data)
  // useEffect(() => {
  //   if (onboardingData && workspaceId && onboardingData.workspace_id !== workspaceId) {
  //     console.warn("🚨 Onboarding data workspace mismatch detected!")
  //     console.warn("  - Expected workspace:", workspaceId)
  //     console.warn("  - Onboarding returned:", onboardingData.workspace_id)
  //     console.warn("  - This indicates a backend bug in the onboarding endpoint")
  //   }
  // }, [onboardingData, workspaceId])

  console.log("📊 useClusters State:")
  console.log("  - Workspace ID:", workspaceId)
  console.log("  - Session ready:", sessionReady)
  console.log("  - Clusters count:", displayClusters.length)
  console.log("  - Processing clusters:", processingClusters.length)
  console.log("  - Polling interval:", processingClusters.length > 0 ? 180000 : 0)
  console.log("  - Onboarding check: DISABLED (preventing session corruption)")
  console.log("  - API skip condition:", !workspaceId)

  // 🔑 KEY FIX: Clear workspace data and invalidate caches when switching workspaces
  useEffect(() => {
    if (previousWorkspaceRef.current && previousWorkspaceRef.current !== workspaceId) {
      console.log("🔄 Workspace changed from", previousWorkspaceRef.current, "to", workspaceId)
      console.log("🧹 Clearing Redux clusters state for workspace switch")
      
      // Clear clusters for the previous workspace
      dispatch(clearWorkspaceClusters(previousWorkspaceRef.current))
      
      // Clear processing clusters (they're workspace-agnostic, but we want fresh state)
      dispatch(removeProcessingCluster()) // This will clear all processing clusters
      
      // 🔑 OPTIMIZATION: Don't invalidate clusters cache - let RTK Query handle it
      // The useGetClustersQuery will automatically refetch when workspaceId changes
      // due to refetchOnMountOrArgChange: true
      console.log("✅ Skipping manual cache invalidation - RTK Query will handle refetch")
    }
    
    previousWorkspaceRef.current = workspaceId
  }, [workspaceId, dispatch])

  // Domain info comes from workspace access - no need for separate domain change handling

  // Check for status changes and handle notifications
  useEffect(() => {
    if (!displayClusters.length) return

    displayClusters.forEach((cluster) => {
      const previousCluster = previousClustersRef.current[cluster._id]
      const currentStatus = cluster.gsc_status
      const previousStatus = previousCluster?.gsc_status

      // If cluster is processing, add to processing list
      if ((currentStatus === "processing" || currentStatus === "pending") && !processingClusters.includes(cluster._id)) {
        dispatch(addProcessingCluster(cluster._id))

        // Set timeout to stop polling after 10 minutes (600000ms)
        setTimeout(() => {
          dispatch(removeProcessingCluster(cluster._id))
        }, 600000)
      }

      // Check for status changes
      if (previousStatus && previousStatus !== currentStatus) {
        if (currentStatus === "completed" && previousStatus === "processing") {
          // GSC processing completed
          dispatch(removeProcessingCluster(cluster._id))

          // Show success notification
          dispatch(
            addNotification({
              type: "success",
              title: "GSC Data Ready",
              message: `Google Search Console data for "${cluster.clusterName}" is now available!`,
            }),
          )

          console.log(`Cluster ${cluster._id} GSC processing completed`)
        } else if (currentStatus === "error" && previousStatus === "processing") {
          // GSC processing failed
          dispatch(removeProcessingCluster(cluster._id))

          // Show error notification
          dispatch(
            addNotification({
              type: "error",
              title: "GSC Processing Failed",
              message: `Failed to process Google Search Console data for "${cluster.clusterName}".`,
            }),
          )
        }
      }

      // Update previous clusters reference
      previousClustersRef.current[cluster._id] = { ...cluster }
    })

    // Clean up removed clusters from processing list
    const currentClusterIds = displayClusters.map((c) => c._id)
    processingClusters.forEach((clusterId) => {
      if (!currentClusterIds.includes(clusterId)) {
        dispatch(removeProcessingCluster(clusterId))
      }
    })
  }, [displayClusters, processingClusters, dispatch])

  // Filter clusters based on search query and filters
  const filteredClusters = useMemo(() => {
    if (!displayClusters) return []

    let filtered = displayClusters

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter((cluster) => {
        const searchableText = [cluster.clusterName, cluster.deviceFilter, cluster.countryFilter]
          .join(" ")
          .toLowerCase()
        return searchableText.includes(query)
      })
    }

    // Apply device filter
    if (filters.device !== "all") {
      filtered = filtered.filter((cluster) => cluster.deviceFilter === filters.device)
    }

    // Apply country filter
    if (filters.country !== "all") {
      filtered = filtered.filter((cluster) => cluster.countryFilter.includes(filters.country))
    }

    return filtered
  }, [displayClusters, searchQuery, filters])

  // Create cluster handler
  const handleCreateCluster = async (clusterData) => {
    try {
      const result = await createCluster({
        clusterData,
        workspaceId,
      }).unwrap()

      dispatch(
        addNotification({
          type: "success",
          title: "Cluster Created",
          message: "Cluster created successfully! GSC data processing has started in the background.",
        }),
      )

      // Add to processing list if the cluster GSC is processing
      if (result.gsc_status === "processing" || result.gsc_status === "pending") {
        dispatch(addProcessingCluster(result._id))

        // Set timeout to stop polling after 10 minutes
        setTimeout(() => {
          dispatch(removeProcessingCluster(result._id))
        }, 600000)
      }

      dispatch(closeCreateModal())
      return true
    } catch (error) {
      console.error("Error creating cluster:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Creation Failed",
          message: error.data?.detail || "Failed to create cluster. Please try again.",
        }),
      )
      return false
    }
  }

  // Delete cluster handler
  const handleDeleteCluster = async (clusterId) => {
    try {
      // Remove from processing list if it's there
      dispatch(removeProcessingCluster(clusterId))

      await deleteCluster({
        clusterId,
        workspaceId,
      }).unwrap()

      dispatch(
        addNotification({
          type: "success",
          title: "Cluster Deleted",
          message: "Cluster moved to trash successfully!",
        }),
      )

      dispatch(closeDeleteModal())
      return true
    } catch (error) {
      console.error("Error deleting cluster:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Deletion Failed",
          message: error.data?.detail || "Failed to delete cluster. Please try again.",
        }),
      )
      return false
    }
  }

  // Check if cluster is processing
  const isClusterProcessing = (clusterId) => {
    return processingClusters.includes(clusterId)
  }

  // Calculate stats
  const totalClusters = displayClusters.length
  const filteredCount = filteredClusters.length
  const processingCount = processingClusters.length

  return {
    // Data
    clusters: filteredClusters,
    onboardingData,

    // Loading states
    isLoading: onboardingLoading || isLoadingClusters,
    isCreating,
    isDeleting,

    // Error states
    error: onboardingError || clustersError,

    // Actions
    handleCreateCluster,
    handleDeleteCluster,
    refetchClusters,

    // Computed values
    totalClusters,
    filteredCount,
    processingCount,

    // Status helpers
    isClusterProcessing,
  }
}