import { createSlice } from "@reduxjs/toolkit"

// Define navigation pages and their corresponding routes
export const NAVIGATION_PAGES = {
  OVERVIEW: "overview",
  CLUSTERS: "clusters", 
  TRASH: "trash",
  SETTINGS: "settings",
}

// Define route patterns for each page
export const ROUTE_PATTERNS = {
  [NAVIGATION_PAGES.OVERVIEW]: "/workspace/:workspaceId",
  [NAVIGATION_PAGES.CLUSTERS]: "/workspace/:workspaceId/clusters",
  [NAVIGATION_PAGES.TRASH]: "/workspace/:workspaceId/trash", 
  [NAVIGATION_PAGES.SETTINGS]: "/workspace/:workspaceId/settings",
}

// Helper function to determine page from pathname
export const getPageFromPath = (pathname) => {
  if (pathname.includes("/trash")) return NAVIGATION_PAGES.TRASH
  if (pathname.includes("/settings")) return NAVIGATION_PAGES.SETTINGS
  if (pathname.includes("/clusters")) return NAVIGATION_PAGES.CLUSTERS
  if (pathname.match(/^\/workspace\/[^/]+\/?$/)) return NAVIGATION_PAGES.OVERVIEW
  return NAVIGATION_PAGES.OVERVIEW // Default fallback
}

// Helper function to build route for a page
export const buildRoute = (page, workspaceId, additionalPath = "") => {
  if (!workspaceId) return "/"
  
  const baseRoutes = {
    [NAVIGATION_PAGES.OVERVIEW]: `/workspace/${workspaceId}`,
    [NAVIGATION_PAGES.CLUSTERS]: `/workspace/${workspaceId}/clusters`,
    [NAVIGATION_PAGES.TRASH]: `/workspace/${workspaceId}/trash`,
    [NAVIGATION_PAGES.SETTINGS]: `/workspace/${workspaceId}/settings`,
  }
  
  const basePath = baseRoutes[page] || baseRoutes[NAVIGATION_PAGES.OVERVIEW]
  return basePath + additionalPath
}

const initialState = {
  // Current page within the workspace
  currentPage: NAVIGATION_PAGES.OVERVIEW,
  
  // Previous page for back navigation
  previousPage: null,
  
  // Navigation history for the current workspace session
  navigationHistory: [],
  
  // Track if we're in the middle of navigation
  isNavigating: false,
  
  // Track navigation type: 'intra-workspace' | 'inter-workspace' | 'external'
  navigationType: null,
  
  // Store page-specific state that should persist during navigation
  pageStates: {
    [NAVIGATION_PAGES.OVERVIEW]: {},
    [NAVIGATION_PAGES.CLUSTERS]: {
      searchQuery: "",
      selectedCluster: null,
      filters: { device: "all", country: "all" }
    },
    [NAVIGATION_PAGES.TRASH]: {
      activeTab: "clusters",
      searchQuery: ""
    },
    [NAVIGATION_PAGES.SETTINGS]: {}
  },
  
  // Track workspace-specific navigation state
  workspaceNavigationState: {},
}

const navigationSlice = createSlice({
  name: "navigation",
  initialState,
  reducers: {
    // Set current page and update navigation history
    setCurrentPage: (state, action) => {
      const { page, workspaceId, navigationType = "intra-workspace" } = action.payload
      
      // Store previous page
      state.previousPage = state.currentPage
      
      // Update current page
      state.currentPage = page
      state.navigationType = navigationType
      
      // Add to navigation history (limit to last 10 entries)
      state.navigationHistory.push({
        page,
        workspaceId,
        timestamp: Date.now(),
        navigationType
      })
      
      if (state.navigationHistory.length > 10) {
        state.navigationHistory.shift()
      }
      
      // Initialize workspace navigation state if needed
      if (workspaceId && !state.workspaceNavigationState[workspaceId]) {
        state.workspaceNavigationState[workspaceId] = {
          currentPage: page,
          lastVisited: Date.now(),
          pageHistory: [page]
        }
      } else if (workspaceId) {
        state.workspaceNavigationState[workspaceId].currentPage = page
        state.workspaceNavigationState[workspaceId].lastVisited = Date.now()
        state.workspaceNavigationState[workspaceId].pageHistory.push(page)
        
        // Limit page history
        if (state.workspaceNavigationState[workspaceId].pageHistory.length > 5) {
          state.workspaceNavigationState[workspaceId].pageHistory.shift()
        }
      }
    },
    
    // Set navigation state (loading, etc.)
    setNavigationState: (state, action) => {
      const { isNavigating, navigationType } = action.payload
      state.isNavigating = isNavigating
      if (navigationType) state.navigationType = navigationType
    },
    
    // Update page-specific state
    updatePageState: (state, action) => {
      const { page, stateUpdate } = action.payload
      if (state.pageStates[page]) {
        state.pageStates[page] = { ...state.pageStates[page], ...stateUpdate }
      }
    },
    
    // Reset page state
    resetPageState: (state, action) => {
      const { page } = action.payload
      if (state.pageStates[page]) {
        state.pageStates[page] = initialState.pageStates[page]
      }
    },
    
    // Initialize navigation for a workspace
    initializeWorkspaceNavigation: (state, action) => {
      const { workspaceId, currentPath } = action.payload
      const page = getPageFromPath(currentPath)
      
      state.currentPage = page
      state.workspaceNavigationState[workspaceId] = {
        currentPage: page,
        lastVisited: Date.now(),
        pageHistory: [page]
      }
    },
    
    // Clear navigation history
    clearNavigationHistory: (state) => {
      state.navigationHistory = []
      state.previousPage = null
    },
    
    // Reset navigation state (for logout, etc.)
    resetNavigationState: (state) => {
      return { ...initialState }
    },
    
    // Handle workspace switch - preserve some state, reset others
    handleWorkspaceSwitch: (state, action) => {
      const { newWorkspaceId, currentPath } = action.payload
      const page = getPageFromPath(currentPath)
      
      // Reset current navigation but preserve workspace-specific states
      state.currentPage = page
      state.previousPage = null
      state.isNavigating = false
      state.navigationType = "inter-workspace"
      
      // Clear general navigation history but keep workspace-specific history
      state.navigationHistory = []
      
      // Initialize or update workspace navigation state
      if (!state.workspaceNavigationState[newWorkspaceId]) {
        state.workspaceNavigationState[newWorkspaceId] = {
          currentPage: page,
          lastVisited: Date.now(),
          pageHistory: [page]
        }
      } else {
        state.workspaceNavigationState[newWorkspaceId].currentPage = page
        state.workspaceNavigationState[newWorkspaceId].lastVisited = Date.now()
      }
    }
  },
})

export const {
  setCurrentPage,
  setNavigationState,
  updatePageState,
  resetPageState,
  initializeWorkspaceNavigation,
  clearNavigationHistory,
  resetNavigationState,
  handleWorkspaceSwitch
} = navigationSlice.actions

export default navigationSlice.reducer

// Selectors
export const selectCurrentPage = (state) => state.navigation.currentPage
export const selectPreviousPage = (state) => state.navigation.previousPage
export const selectIsNavigating = (state) => state.navigation.isNavigating
export const selectNavigationType = (state) => state.navigation.navigationType
export const selectPageState = (page) => (state) => state.navigation.pageStates[page] || {}
export const selectNavigationHistory = (state) => state.navigation.navigationHistory
export const selectWorkspaceNavigationState = (workspaceId) => (state) => 
  state.navigation.workspaceNavigationState[workspaceId] || null
