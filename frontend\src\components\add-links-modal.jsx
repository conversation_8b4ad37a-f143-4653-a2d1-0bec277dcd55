import { useState, useRef, useEffect } from "react"
import { <PERSON>ert<PERSON>ircle, Loader2 } from "lucide-react"

const AddLinksModal = ({ isOpen, onClose, onSubmit, clusterName, clusterDomain, isSubmitting }) => {
  const [links, setLinks] = useState([{ url: "" }])
  const [errors, setErrors] = useState({})
  const [generalError, setGeneralError] = useState(null)
  const [showAddingProgress, setShowAddingProgress] = useState(false)
  const modalRef = useRef(null)
  const initialInputRef = useRef(null)

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setLinks([{ url: "" }])
      setErrors({})
      setGeneralError(null)
      setShowAddingProgress(false)
      if (initialInputRef.current) {
        setTimeout(() => {
          initialInputRef.current.focus()
        }, 100)
      }
    }
  }, [isOpen])

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose])

  // Handle adding a new link field
  const handleAddLink = () => {
    setLinks([...links, { url: "" }])
    // Clear any previous errors when adding a new field
    setErrors({})
    setGeneralError(null)
  }

  // Handle link URL change
  const handleLinkChange = (i, v) => {
    const copy = [...links]
    copy[i].url = v
    setLinks(copy)

    // Validate domain if URL is not empty
    if (v.trim()) {
      const isDomainValid = validateDomainMatch(v, clusterDomain)
      const newErrors = { ...errors }

      if (!validateUrl(v)) {
        newErrors[i] = "Please enter a valid URL"
      } else if (!isDomainValid) {
        newErrors[i] = `URL must be from domain: ${clusterDomain.replace("sc-domain:", "")}`
      } else {
        delete newErrors[i]
      }

      setErrors(newErrors)
    } else {
      // Clear error if field is empty
      const newErrors = { ...errors }
      delete newErrors[i]
      setErrors(newErrors)
    }
  }

  const validateUrl = (url) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const validateDomainMatch = (url, domain) => {
    try {
      // Remove the "sc-domain:" prefix from domain
      const cleanDomain = domain.replace("sc-domain:", "")

      // Parse the URL
      const urlObj = new URL(url)
      const urlDomain = urlObj.hostname

      // Check if the URL's domain matches or is a subdomain of the specified domain
      return urlDomain === cleanDomain || urlDomain.endsWith(`.${cleanDomain}`)
    } catch {
      return false
    }
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setErrors({})
    setGeneralError(null)
    setShowAddingProgress(true)

    // Validate all non-empty URLs
    const newErrors = {}
    const validLinks = links
      .map((l, index) => {
        const url = l.url.trim()
        if (url === "") return null

        if (!validateUrl(url)) {
          newErrors[index] = "Please enter a valid URL"
          return null
        }

        if (!validateDomainMatch(url, clusterDomain)) {
          newErrors[index] = `URL must be from domain: ${clusterDomain.replace("sc-domain:", "")}`
          return null
        }

        return { url }
      })
      .filter(Boolean)

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      setShowAddingProgress(false)
      return
    }

    if (validLinks.length === 0) {
      setGeneralError("Please enter at least one valid URL")
      setShowAddingProgress(false)
      return
    }

    console.log("AddLinksModal - Submitting links:", validLinks)

    try {
      // Call the onSubmit handler with the correct format
      const success = await onSubmit(validLinks)

      if (success) {
        // Reset form and close modal after successful creation
        setLinks([{ url: "" }])
        setErrors({})
        setGeneralError(null)
        setShowAddingProgress(false)
        onClose()
      } else {
        setShowAddingProgress(false)
      }
    } catch (error) {
      console.error("Link addition failed:", error)
      setShowAddingProgress(false)
      setGeneralError("Failed to add links. Please try again.")
    }
  }

  // Handle cancel
  const handleCancel = () => {
    setLinks([{ url: "" }]) // Reset form
    setErrors({})
    setGeneralError(null)
    setShowAddingProgress(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-[8px] shadow-lg w-full max-w-md mx-4 overflow-hidden">
        {/* Adding Progress Overlay */}
        {showAddingProgress && (
          <div className="absolute inset-0 bg-white bg-opacity-95 rounded-[8px] flex items-center justify-center z-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#352090] mx-auto"></div>
              <div className="mt-6 px-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Processing Your Links</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  Setting up your links and preparing performance tracking.
                  <br />
                  This will just take a moment...
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Updated Header to match design */}
        <div className="p-6">
          <h2 className="text-2xl font-semibold text-gray-900">Add Links to {clusterName}</h2>
          <p className="text-sm text-gray-500 mt-1">Domain: {clusterDomain}</p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <div className="px-6">
            {/* Add Links Section */}
            <div className="mb-4">
              <h3 className="text-sm font-medium text-[#352090]">Add Links</h3>
            </div>

            {generalError && (
              <div className="mb-4 p-3 bg-red-50 rounded-[8px] flex items-start gap-2">
                <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
                <p className="text-red-600 text-sm">{generalError}</p>
              </div>
            )}

            <div className="space-y-4">
              {links.map((link, index) => (
                <div key={index}>
                  <label className="block text-sm font-medium mb-1">Link URL</label>
                  <div className="relative">
                    <input
                      type="url"
                      value={link.url}
                      onChange={(e) => handleLinkChange(index, e.target.value)}
                      placeholder="https://example.com"
                      className={`w-full px-3 py-2 border ${
                        errors[index] ? "border-red-500" : "border-gray-300"
                      } rounded-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                      ref={index === 0 ? initialInputRef : null}
                    />
                    {errors[index] && <p className="text-red-500 text-xs mt-1">{errors[index]}</p>}
                    <div className="flex items-center gap-1 mt-1">
                      <AlertCircle className="h-4 w-4 text-gray-400" />
                      <p className="text-gray-500 text-xs">
                        URLs must be from domain: {clusterDomain.replace("sc-domain:", "")}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Add More Links Button - Right Aligned */}
            <div className="flex justify-end mt-4 mb-6">
              <button
                type="button"
                onClick={handleAddLink}
                className="text-blue-500 text-sm hover:text-blue-700 flex items-center gap-1"
              >
                Add More links
              </button>
            </div>
          </div>

          {/* Action Buttons - Updated styling */}
          <div className="p-4 flex justify-end space-x-3 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting || showAddingProgress}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-[8px] hover:bg-gray-50 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={
                isSubmitting ||
                showAddingProgress ||
                Object.keys(errors).length > 0 ||
                links.every((link) => !link.url.trim())
              }
              className="px-6 py-2 bg-[#352090] text-white rounded-[8px] hover:bg-[#2a1a70] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isSubmitting || showAddingProgress ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Adding Links...
                </>
              ) : (
                "Submit"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddLinksModal
