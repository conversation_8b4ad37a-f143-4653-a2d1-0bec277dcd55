import React from "react"
import <PERSON><PERSON><PERSON><PERSON> from "react-dom/client"
import { Provider } from "react-redux"
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom"
import { store } from "./store/store"
import WorkspaceProvider from "./components/workspace/WorkspaceProvider"
import App from "./App"
import "./App.css"

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <WorkspaceProvider>
          <App />
        </WorkspaceProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>,
)
