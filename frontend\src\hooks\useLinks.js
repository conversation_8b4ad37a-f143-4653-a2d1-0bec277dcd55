import { useMemo, useEffect, useRef } from "react"
import { useSelector, useDispatch } from "react-redux"
import {
  useGetClusterLinksQuery,
  useAddLinkToClusterMutation,
  useRefreshLinkDataMutation,
  useDeleteLinkMutation,
  linkApi,
} from "../store/api/linkApi"
import { addNotification } from "../store/slices/uiSlice"
import {
  closeAddModal,
  closeDeleteModal,
  addProcessingLink,
  removeProcessingLink,
  addRefreshingLink,
  removeRefreshingLink,
} from "../store/slices/linkSlice"

export const useLinks = (clusterId, workspaceId) => {
  const dispatch = useDispatch()
  const { searchQuery, processingLinks, refreshingLinks, activeLinks } = useSelector((state) => state.link)

  // Track previous links to detect status changes
  const previousLinksRef = useRef({})

  // Get links from Redux store for this cluster
  const clusterLinks = activeLinks[clusterId] || []

  // Fetch links with polling when there are processing links
  const {
    data: apiLinks = [],
    isLoading: linksLoading,
    error: linksError,
    refetch: refetchLinks,
  } = useGetClusterLinksQuery(
    { clusterId, workspaceId },
    {
      skip: !clusterId || !workspaceId,
      // Poll every 3 minutes (180000ms) if there are processing links
      pollingInterval: processingLinks.length > 0 ? 180000 : 0,
    },
  )

  // Use Redux store data if available, fallback to API data
  const links = clusterLinks.length > 0 ? clusterLinks : apiLinks

  // Add link mutation (single link - version 26 approach)
  const [addLink, { isLoading: isAdding }] = useAddLinkToClusterMutation()

  // Refresh link mutation
  const [refreshLink, { isLoading: isRefreshing }] = useRefreshLinkDataMutation()

  // Delete link mutation
  const [deleteLink, { isLoading: isDeleting }] = useDeleteLinkMutation()

  // Check for status changes and handle notifications
  useEffect(() => {
    if (!links.length) return

    console.log("useLinks - Checking status changes for links:", links.length)

    links.forEach((link) => {
      const linkId = link.id || link._id
      const previousLink = previousLinksRef.current[linkId]
      const currentStatus = link.status
      const previousStatus = previousLink?.status

      console.log(`Link ${linkId}: ${previousStatus} → ${currentStatus}`)

      // If link is processing, add to processing list
      if ((currentStatus === "in_progress" || currentStatus === "pending") && !processingLinks.includes(linkId)) {
        console.log(`Adding link ${linkId} to processing list`)
        dispatch(addProcessingLink(linkId))

        // Set timeout to stop polling after 10 minutes (600000ms)
        setTimeout(() => {
          console.log(`Removing link ${linkId} from processing list (timeout)`)
          dispatch(removeProcessingLink(linkId))
        }, 600000)
      }

      // Check for status changes
      if (previousStatus && previousStatus !== currentStatus) {
        if (currentStatus === "completed" && (previousStatus === "in_progress" || previousStatus === "pending")) {
          // Link processing completed
          console.log(`Link ${linkId} processing completed!`)
          dispatch(removeProcessingLink(linkId))
          dispatch(removeRefreshingLink(linkId))

          // Show success notification
          dispatch(
            addNotification({
              type: "success",
              title: "Link Data Ready",
              message: `Performance data for "${link.url}" is now available!`,
            }),
          )

          // Invalidate performance cache for this link
          dispatch(linkApi.util.invalidateTags([{ type: "LinkPerformance", id: linkId }]))
        } else if (
          (currentStatus === "error" || currentStatus === "failed") &&
          (previousStatus === "in_progress" || previousStatus === "pending")
        ) {
          // Link processing failed
          console.log(`Link ${linkId} processing failed!`)
          dispatch(removeProcessingLink(linkId))
          dispatch(removeRefreshingLink(linkId))

          // Show error notification
          dispatch(
            addNotification({
              type: "error",
              title: "Link Processing Failed",
              message: `Failed to process data for "${link.url}".`,
            }),
          )
        }
      }

      // Update previous links reference
      previousLinksRef.current[linkId] = { ...link }
    })

    // Clean up removed links from processing list
    const currentLinkIds = links.map((l) => l.id || l._id)
    processingLinks.forEach((linkId) => {
      if (!currentLinkIds.includes(linkId)) {
        console.log(`Cleaning up removed link ${linkId} from processing list`)
        dispatch(removeProcessingLink(linkId))
      }
    })
  }, [links, processingLinks, dispatch])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clean up any remaining processing links
      processingLinks.forEach((linkId) => {
        dispatch(removeProcessingLink(linkId))
      })
      refreshingLinks.forEach((linkId) => {
        dispatch(removeRefreshingLink(linkId))
      })
    }
  }, [])

  // Filter links based on search query
  const filteredLinks = useMemo(() => {
    if (!links) return []

    let filtered = links

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter((link) => link.url.toLowerCase().includes(query))
    }

    return filtered
  }, [links, searchQuery])

  // Add links handler (Version 26 approach - individual link addition)
  const handleAddLinks = async (linkObjects) => {
    try {
      const createdLinks = []

      console.log("useLinks - handleAddLinks received:", linkObjects)

      // Add each link individually (Version 26 working approach)
      for (const { url } of linkObjects) {
        console.log("useLinks - adding individual link:", url)

        const result = await addLink({
          clusterId,
          workspaceId,
          url, // Single URL string
        }).unwrap()

        const normalizedLink = {
          ...result,
          id: result.id ?? result._id,
          status: result.status || "in_progress",
        }

        createdLinks.push(normalizedLink)

        // Add to processing list if the link is processing
        if (normalizedLink.status === "in_progress" || normalizedLink.status === "pending") {
          dispatch(addProcessingLink(normalizedLink.id))

          // Set timeout to stop polling after 10 minutes
          setTimeout(() => {
            dispatch(removeProcessingLink(normalizedLink.id))
          }, 600000)
        }
      }

      dispatch(
        addNotification({
          type: "success",
          title: "Links Added",
          message: `Successfully added ${createdLinks.length} link(s)! Processing has started in the background.`,
        }),
      )

      dispatch(closeAddModal())
      return true
    } catch (error) {
      console.error("Error adding links:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Addition Failed",
          message: error.data?.detail || "Failed to add links. Please try again.",
        }),
      )
      return false
    }
  }

  // Refresh link handler - Updated with proper polling integration
  const handleRefreshLink = async (link) => {
    try {
      const linkId = link.id || link._id
      console.log(`Starting refresh for link ${linkId}`)

      // Add to refreshing list (shows spinning icon)
      dispatch(addRefreshingLink(linkId))

      // Make API call to refresh link data
      await refreshLink({
        clusterId,
        linkId,
        workspaceId,
      }).unwrap()

      console.log(`Refresh API call successful for link ${linkId}`)

      // Add to processing list (starts polling)
      dispatch(addProcessingLink(linkId))

      dispatch(
        addNotification({
          type: "success",
          title: "Refresh Started",
          message: `Data refresh initiated for "${link.url}". Processing in background...`,
        }),
      )

      // Set timeout to stop polling after 10 minutes
      setTimeout(() => {
        console.log(`Removing link ${linkId} from processing list (refresh timeout)`)
        dispatch(removeProcessingLink(linkId))
        dispatch(removeRefreshingLink(linkId))
      }, 600000)

      // Remove from refreshing list after 3 seconds (keep spinning briefly)
      setTimeout(() => {
        dispatch(removeRefreshingLink(linkId))
      }, 3000)

      return true
    } catch (error) {
      console.error("Error refreshing link:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Refresh Failed",
          message: error.data?.detail || "Failed to refresh link data. Please try again.",
        }),
      )

      dispatch(removeRefreshingLink(link.id || link._id))
      return false
    }
  }

  // Delete link handler
  const handleDeleteLink = async (linkId) => {
    try {
      // Remove from processing and refreshing lists if they're there
      dispatch(removeProcessingLink(linkId))
      dispatch(removeRefreshingLink(linkId))

      await deleteLink({
        clusterId,
        linkId,
        workspaceId,
      }).unwrap()

      dispatch(
        addNotification({
          type: "success",
          title: "Link Deleted",
          message: "Link moved to trash successfully!",
        }),
      )

      dispatch(closeDeleteModal())
      return true
    } catch (error) {
      console.error("Error deleting link:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Deletion Failed",
          message: error.data?.detail || "Failed to delete link. Please try again.",
        }),
      )
      return false
    }
  }

  return {
    // Data
    links: filteredLinks,

    // Loading states
    isLoading: linksLoading,
    isAdding,
    isRefreshing,
    isDeleting,

    // Error states
    error: linksError,

    // Actions
    handleAddLinks,
    handleRefreshLink,
    handleDeleteLink,
    refetchLinks,

    // Computed values
    totalLinks: links?.length || 0,
    filteredCount: filteredLinks.length,
    processingCount: processingLinks.length,

    // Status helpers
    isLinkProcessing: (linkId) => processingLinks.includes(linkId),
    isLinkRefreshing: (linkId) => refreshingLinks.includes(linkId),
  }
}
