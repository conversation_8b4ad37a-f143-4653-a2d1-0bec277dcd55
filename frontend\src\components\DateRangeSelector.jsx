import React, { useState, useEffect } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Check, ChevronDown, Calendar } from "lucide-react";

const PRESETS = [
  { label: "28 days", value: "28d" },
  { label: "3 months", value: "3m" },
];

const FILTER_PRESETS = [
  { label: "Last 6 months", value: "6m" },
  { label: "Last 12 months", value: "12m" },
  { label: "Last 16 months", value: "16m" },
];

const COMPARE_PRESETS = [
  { label: "Compare last 28 days to previous period", value: "28d_prev" },
  { label: "Compare last 3 months to previous period", value: "3m_prev" },
  { label: "Compare last 6 months to previous period", value: "6m_prev" },
  { label: "Compare last 28 days year over year", value: "28d_yoy" },
  { label: "Compare last 3 months year over year", value: "3m_yoy" },
];

export default function DateRangeSelector({ onApply, showCompare = true, initialFilter = "3m" }) {
  const [selectedPreset, setSelectedPreset] = useState(initialFilter);
  const [modalOpen, setModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("filter");
  const [filterPreset, setFilterPreset] = useState(initialFilter);
  const [comparePreset, setComparePreset] = useState("28d_prev");
  const [customStart, setCustomStart] = useState(new Date());
  const [customEnd, setCustomEnd] = useState(new Date());
  const [compareStart, setCompareStart] = useState(new Date());
  const [compareEnd, setCompareEnd] = useState(new Date());
  const [customMode, setCustomMode] = useState(false);
  const [compareCustomMode, setCompareCustomMode] = useState(false);

  // Reset to filter tab if compare is hidden
  useEffect(() => {
    if (!showCompare && activeTab === "compare") {
      setActiveTab("filter");
    }
  }, [showCompare, activeTab]);

  // Handle Apply
  const handleApply = () => {
    if (activeTab === "filter") {
      if (customMode) {
        onApply && onApply({
          type: "filter",
          mode: "custom",
          startDate: customStart,
          endDate: customEnd,
        });
      } else {
        onApply && onApply({
          type: "filter",
          mode: "preset",
          preset: filterPreset,
        });
      }
    } else {
      if (compareCustomMode) {
        onApply && onApply({
          type: "compare",
          mode: "custom",
          startDate: customStart,
          endDate: customEnd,
          compareStart,
          compareEnd,
        });
      } else {
        onApply && onApply({
          type: "compare",
          mode: "preset",
          preset: comparePreset,
        });
      }
    }
    setModalOpen(false);
  };

  // Main preset bar click
  const handlePresetClick = (value) => {
    setSelectedPreset(value);
    if (value === "more") {
      setModalOpen(true);
    } else {
      onApply && onApply({
        type: "filter",
        mode: "preset",
        preset: value
      });
    }
  };

  // Get the label for the selected filter
  const getFilterLabel = (preset) => {
    if (preset === "custom") return "Custom";
    const allPresets = [...PRESETS, ...FILTER_PRESETS];
    const found = allPresets.find(p => p.value === preset);
    return found ? found.label : "More";
  };

  return (
    <div className="font-noto">
      {/* Horizontal preset bar */}
      <div className="flex border-2 border-[#000000] rounded-[8px] overflow-hidden w-fit bg-white">
        {PRESETS.map((preset) => (
          <button
            key={preset.value}
            className={`relative px-6 py-2 text-sm font-medium border-r last:border-r-0 border-[#000000] focus:outline-none transition-colors
              ${selectedPreset === preset.value ? "bg-indigo-50 text-black" : "bg-white text-black"}
            `}
            onClick={() => handlePresetClick(preset.value)}
          >
            {selectedPreset === preset.value && (
              <span className="absolute left-2 top-1/2 -translate-y-1/2 text-indigo-600">
                <Check size={16} />
              </span>
            )}
            <span className={selectedPreset === preset.value ? "pl-5" : ""}>{preset.label}</span>
          </button>
        ))}
        <button
          className={`relative px-6 py-2 text-sm font-medium border-r last:border-r-0 border-[#000000] focus:outline-none transition-colors
            ${!PRESETS.some(p => p.value === selectedPreset) ? "bg-indigo-50 text-black" : "bg-white text-black"}
            flex items-center gap-1`}
          onClick={() => handlePresetClick("more")}
        >
          {!PRESETS.some(p => p.value === selectedPreset) && (
            <span className="absolute left-2 top-1/2 -translate-y-1/2 text-indigo-600">
              <Check size={16} />
            </span>
          )}
          <span className={!PRESETS.some(p => p.value === selectedPreset) ? "pl-5" : ""}>
            {!PRESETS.some(p => p.value === selectedPreset) ? getFilterLabel(selectedPreset) : "More"}
          </span>
          <ChevronDown size={16} />
        </button>
      </div>

      {/* Modal */}
      {modalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-20">
          <div className="bg-white rounded lg shadow-lg p-6 w-full max-w-md md:max-w-xl relative">
            {/* Header */}
            <div className="text-lg font-semibold mb-2">Date range</div>
            {/* Tabs - Only show if compare is enabled */}
            {showCompare ? (
              <div className="flex border-b mb-4">
                <button
                  className={`px-4 py-2 font-semibold text-sm focus:outline-none border-b-2 transition-colors ${activeTab === "filter" ? "border-[#352090] text-black" : "border-transparent text-gray-400"}`}
                  onClick={() => setActiveTab("filter")}
                >
                  Filter
                </button>
                <button
                  className={`px-4 py-2 font-semibold text-sm focus:outline-none border-b-2 transition-colors ${activeTab === "compare" ? "border-[#352090] text-black" : "border-transparent text-gray-400"}`}
                  onClick={() => setActiveTab("compare")}
                >
                  Compare
                </button>
              </div>
            ) : (
              // Add some spacing when tabs are hidden
              <div className="mb-4"></div>
            )}

            {/* Filter Tab */}
            {activeTab === "filter" && (
              <div>
                <div className="flex flex-col gap-2 mb-4">
                  {FILTER_PRESETS.map((preset) => (
                    <button
                      key={preset.value}
                      className={`w-full py-2 rounded-[8px] text-sm font-medium border transition-colors ${filterPreset === preset.value && !customMode ? "bg-[#352090] text-white border-[#352090]" : "bg-white text-black border-gray-200"}`}
                      onClick={() => { setFilterPreset(preset.value); setCustomMode(false); }}
                    >
                      {preset.label}
                    </button>
                  ))}
                  <button
                    className={`w-full py-2 rounded-[8px] text-sm font-medium border transition-colors ${customMode ? "bg-[#352090] text-white border-[#352090]" : "bg-white text-black border-gray-200"}`}
                    onClick={() => setCustomMode(true)}
                  >
                    Custom
                  </button>
                </div>
                {/* Custom date pickers */}
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex-1">
                    <label className="block text-xs mb-1">Start date</label>
                    <div className="relative">
                      <DatePicker
                        selected={customStart}
                        onChange={setCustomStart}
                        dateFormat="dd.MM.yyyy"
                        className="w-full border rounded px-6 py-2 text-sm focus:outline-none"
                      />
                      <Calendar className="absolute left-1 top-1/4 text-gray-400 pointer-events-none" size={18} />
                    </div>
                  </div>
                  <span className="text-lg text-gray-400">-</span>
                  <div className="flex-1">
                    <label className="block text-xs mb-1">End Date</label>
                    <div className="relative">
                      <DatePicker
                        selected={customEnd}
                        onChange={setCustomEnd}
                        dateFormat="dd.MM.yyyy"
                        className="w-full border rounded px-6 py-2 text-sm focus:outline-none"
                      />
                      <Calendar className="absolute left-1 top-1/4 text-gray-400 pointer-events-none" size={18} />
                    </div>
                  </div>
                </div>
                {/* Action buttons */}
                <div className="flex justify-end gap-2 mt-4">
                  <button className="px-4 py-2 rounded-[8px] border text-sm font-medium bg-gray-100 text-black" onClick={() => setModalOpen(false)}>Cancel</button>
                  <button className="px-4 py-2 rounded-[8px] text-sm font-medium bg-[#352090] text-white" onClick={handleApply}>Apply</button>
                </div>
              </div>
            )}

            {/* Compare Tab */}
            {activeTab === "compare" && (
              <div className="max-h-[400px] overflow-y-auto pr-2">
                <div className="flex flex-col gap-2 mb-4">
                  {COMPARE_PRESETS.map((preset) => (
                    <button
                      key={preset.value}
                      className={`w-full py-2 rounded [8px] text-sm font-medium border transition-colors ${comparePreset === preset.value && !compareCustomMode ? "bg-[#352090] text-white border-[#352090]" : "bg-white text-black border-gray-200"}`}
                      onClick={() => { setComparePreset(preset.value); setCompareCustomMode(false); }}
                    >
                      {preset.label}
                    </button>
                  ))}
                  <button
                    className={`w-full py-2 rounded [8px] text-sm font-medium border transition-colors ${compareCustomMode ? "bg-[#352090] text-white border-[#352090]" : "bg-white text-black border-gray-200"}`}
                    onClick={() => setCompareCustomMode(true)}
                  >
                    Custom
                  </button>
                </div>
                {/* Custom compare date pickers */}
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex-1">
                    <label className="block text-xs mb-1">Start date</label>
                    <div className="relative">
                      <DatePicker
                        selected={customStart}
                        onChange={setCustomStart}
                        dateFormat="dd.MM.yyyy"
                        className="w-full border rounded px-6 py-2 text-sm focus:outline-none"
                      />
                      <Calendar className="absolute left-1 top-1/4 text-gray-400 pointer-events-none" size={18} />
                    </div>
                  </div>
                  <span className="text-lg text-gray-400">-</span>
                  <div className="flex-1">
                    <label className="block text-xs mb-1">End Date</label>
                    <div className="relative">
                      <DatePicker
                        selected={customEnd}
                        onChange={setCustomEnd}
                        dateFormat="dd.MM.yyyy"
                        className="w-full border rounded px-6 py-2 text-sm focus:outline-none"
                      />
                      <Calendar className="absolute left-1 top-1/4 text-gray-400 pointer-events-none" size={18} />
                    </div>
                  </div>
                </div>
                <div className="text-xs text-black-500 mb-6 mt-6">VS</div>
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex-1">
                    <label className="block text-xs mb-1">Start date</label>
                    <div className="relative">
                      <DatePicker
                        selected={compareStart}
                        onChange={setCompareStart}
                        dateFormat="dd.MM.yyyy"
                        className="w-full border rounded px-6 py-2 text-sm focus:outline-none"
                      />
                      <Calendar className="absolute left-1 top-1/4 text-gray-400 pointer-events-none" size={18} />
                    </div>
                  </div>
                  <span className="text-lg text-gray-400">-</span>
                  <div className="flex-1">
                    <label className="block text-xs mb-1">End Date</label>
                    <div className="relative">
                      <DatePicker
                        selected={compareEnd}
                        onChange={setCompareEnd}
                        dateFormat="dd.MM.yyyy"
                        className="w-full border rounded px-6 py-2 text-sm focus:outline-none"
                      />
                      <Calendar className="absolute left-1 top-1/4 text-gray-400 pointer-events-none" size={18} />
                    </div>
                  </div>
                </div>
                {/* Action buttons */}
                <div className="flex justify-end gap-2 mt-4">
                  <button className="px-4 py-2 rounded [8px] border text-sm font-medium bg-gray-100 text-black" onClick={() => setModalOpen(false)}>Cancel</button>
                  <button className="px-4 py-2 rounded [8px] text-sm font-medium bg-[#352090] text-white" onClick={handleApply}>Apply</button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 