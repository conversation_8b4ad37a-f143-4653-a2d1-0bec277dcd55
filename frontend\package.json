{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@reduxjs/toolkit": "^2.8.2", "@tippyjs/react": "^4.2.6", "axios": "^1.8.4", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "recharts": "^2.15.3", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.1", "vite": "^6.2.0", "vite-plugin-mkcert": "^1.17.8"}}