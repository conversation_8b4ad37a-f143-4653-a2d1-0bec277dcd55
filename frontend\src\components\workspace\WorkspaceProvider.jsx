import { useEffect } from "react"
import { useWorkspace } from "../../hooks/useWorkspace"

// This component initializes workspace data when the app loads
const WorkspaceProvider = ({ children }) => {
  const { workspaces, loading, error, currentWorkspace } = useWorkspace()

  useEffect(() => {
    console.log("Redux Workspace State:", {
      currentWorkspace,
      workspaces: workspaces.map((ws) => ({
        id: ws.id || ws._id,
        name: ws.name,
        owner_id: ws.owner_id,
      })),
      loading,
      error,
    })
  }, [currentWorkspace, workspaces, loading, error])

  return children
}

export default WorkspaceProvider
