import { useState, useRef, useEffect } from "react"

const PaymentModal = ({ isOpen, onClose, onSubmit, plan }) => {
  const [formData, setFormData] = useState({
    cardholderName: "",
    country: "",
    postalCode: "",
    cardDetails: "",
  })
  const modalRef = useRef(null)

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit(formData)
  }

  if (!isOpen) return null

  // Calculate trial end date (24 days from now)
  const trialEndDate = new Date()
  trialEndDate.setDate(trialEndDate.getDate() + 24)
  const formattedDate = trialEndDate.toLocaleDateString("en-US", { month: "long", day: "numeric" })

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-2xl shadow-lg w-full max-w-4xl mx-4 overflow-hidden">
        <form onSubmit={handleSubmit}>
          <div className="p-8">
            <h2 className="text-2xl font-semibold text-[#352090] mb-8">Pay with Card</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Left Column - Payment Form */}
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-2">Billing Period</label>
                  <div className="relative">
                    <input
                      type="text"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-800"
                      value={`Monthly - $50 / user / month`}
                      readOnly
                    />
                  </div>
                </div>

                <h3 className="text-lg font-medium text-gray-800 mb-4">Card Information</h3>

                <div className="mb-5">
                  <label className="block text-sm font-medium mb-2">Cardholder Name</label>
                  <input
                    type="text"
                    name="cardholderName"
                    value={formData.cardholderName}
                    onChange={handleChange}
                    placeholder="Enter Email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    required
                  />
                </div>

                <div className="mb-5">
                  <label className="block text-sm font-medium mb-2">Country</label>
                  <input
                    type="text"
                    name="country"
                    value={formData.country}
                    onChange={handleChange}
                    placeholder="Enter Email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    required
                  />
                </div>

                <div className="mb-5">
                  <label className="block text-sm font-medium mb-2">Postal Code</label>
                  <input
                    type="text"
                    name="postalCode"
                    value={formData.postalCode}
                    onChange={handleChange}
                    placeholder="Enter Email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    required
                  />
                </div>

                <div className="mb-5">
                  <label className="block text-sm font-medium mb-2">Card Details</label>
                  <input
                    type="text"
                    name="cardDetails"
                    value={formData.cardDetails}
                    onChange={handleChange}
                    placeholder="Enter Email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    required
                  />
                </div>
              </div>

              {/* Right Column - Order Summary */}
              <div>
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="border-b border-gray-200 py-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">1 Seat × Pro Plan(at $50 / user/ month)</span>
                      <span className="text-sm font-medium">$50.00</span>
                    </div>
                  </div>

                  <div className="border-b border-gray-200 py-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">PC Miler</span>
                      <span className="text-sm font-medium">$100.00</span>
                    </div>
                  </div>

                  <div className="border-b border-gray-200 py-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Subtotal</span>
                      <span className="text-sm font-medium">$150.00</span>
                    </div>
                  </div>

                  <div className="py-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Total at renewal</span>
                      <span className="text-sm font-medium">$150.00</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-5 bg-[#f8f7ff] rounded-lg flex items-start">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="flex-shrink-0 mr-3"
                  >
                    <path
                      d="M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                      stroke="#352090"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <p className="text-sm text-gray-700">
                    Your card will not be charged until the end of your trial on July 24th.
                  </p>
                </div>

                <div className="mt-8">
                  <button
                    type="submit"
                    className="w-full py-4 bg-[#352090] text-white rounded-lg hover:bg-[#2a1a70] transition-colors duration-300 mb-4 font-medium"
                  >
                    Confirm and Submit
                  </button>
                  <button
                    type="button"
                    onClick={onClose}
                    className="w-full py-4 border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default PaymentModal
