import { configureStore } from "@reduxjs/toolkit"
import { setupListeners } from "@reduxjs/toolkit/query"
import { apiSlice } from "./api/apiSlice"
import authSlice from "./slices/authSlice"
import uiSlice from "./slices/uiSlice"
import workspaceSlice from "./slices/workspaceSlice"
import onboardingSlice from "./slices/onboardingSlice"
import clusterSlice from "./slices/clusterSlice"
import linkSlice from "./slices/linkSlice"
import trashSlice from "./slices/trashSlice"

export const store = configureStore({
  reducer: {
    [apiSlice.reducerPath]: apiSlice.reducer,
    auth: authSlice,
    ui: uiSlice,
    workspace: workspaceSlice,
    onboarding: onboardingSlice,
    cluster: clusterSlice,
    link: linkSlice,
    trash: trashSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }).concat(apiSlice.middleware),
  devTools: process.env.NODE_ENV !== "production",
})

setupListeners(store.dispatch)

export default store
