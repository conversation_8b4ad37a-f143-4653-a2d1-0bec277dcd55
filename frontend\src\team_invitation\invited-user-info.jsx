import { useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useDispatch } from "react-redux"
import { addNotification } from "../store/slices/uiSlice"
import LogoComponent from "../components/logo-component"
import SpiralImage from "../assets/image.png"
import PhoneInput from "react-phone-input-2"
import "react-phone-input-2/lib/style.css"

const TEAM_SERVICE_URL = import.meta.env.VITE_TEAM_SERVICE

const phoneInputStyles = {
  container: { width: "100%" },
  inputStyle: {
    width: "100%",
    height: "48px",
    fontSize: "14px",
    borderRadius: "12px",
    border: "1px solid #D9D9D9",
  },
  buttonStyle: {
    border: "1px solid #D9D9D9",
    borderRadius: "12px 0 0 12px",
    borderRight: "none",
  },
}

const InvitedUserInfo = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()

  const [invitationData, setInvitationData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isExistingUser, setIsExistingUser] = useState(false)

  const [formData, setFormData] = useState({
    fullName: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
  })

  const [formError, setFormError] = useState(null)
  const [success, setSuccess] = useState(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Validate invitation on component mount
  useEffect(() => {
    const validateInvitation = async () => {
      try {
        // 1. Get token from URL
        const params = new URLSearchParams(window.location.search)
        const token = params.get("token")
        if (!token) throw new Error("No invitation token found")

        console.log("🔍 Validating invitation token:", token)

        // 2. Verify with backend
        const verifyRes = await fetch(`${TEAM_SERVICE_URL}/invite/verify/${token}`)
        if (!verifyRes.ok) {
          const err = await verifyRes.json()
          throw new Error(err.detail || "Invalid invitation token")
        }

        const { status, invitation } = await verifyRes.json()
        if (status !== "success") throw new Error("Invitation verification failed")

        console.log("✅ Invitation verified:", invitation)

        // 3. Save data
        setInvitationData({ ...invitation, token })
        const existing = Boolean(invitation.is_existing_user)
        setIsExistingUser(existing)

        // 4. Existing user: auto-accept & redirect to login
        if (existing) {
          console.log("👤 Existing user detected, auto-accepting invitation...")

          const acceptRes = await fetch(`${TEAM_SERVICE_URL}/invite/accept/${token}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({}),
          })

          if (!acceptRes.ok) {
            const err = await acceptRes.json()
            throw new Error(err.detail || "Failed to accept invitation")
          }

          console.log("✅ Invitation auto-accepted for existing user")

          dispatch(
            addNotification({
              type: "success",
              title: "Invitation Accepted",
              message: "You've been added to the workspace. Please log in to continue.",
            }),
          )

          // Redirect to login with message
          navigate("/login", {
            state: {
              message: "You've been added to a workspace—please log in to continue.",
              invitationToken: token,
            },
          })
          return
        }

        console.log("👤 New user detected, showing registration form")
      } catch (err) {
        console.error("❌ Invitation flow error:", err)
        setError(err.message)

        dispatch(
          addNotification({
            type: "error",
            title: "Invitation Error",
            message: err.message,
          }),
        )
      } finally {
        setLoading(false)
      }
    }

    validateInvitation()
  }, [navigate, dispatch])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handlePhoneChange = (value) => {
    setFormData((prev) => ({ ...prev, phoneNumber: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setFormError(null)
    setSuccess(null)

    // Validation checks
    if (!formData.fullName?.trim()) {
      setFormError("Full name is required")
      setIsSubmitting(false)
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setFormError("Passwords do not match")
      setIsSubmitting(false)
      return
    }

    if (!formData.phoneNumber || formData.phoneNumber.length < 8) {
      setFormError("Please enter a valid phone number")
      setIsSubmitting(false)
      return
    }

    if (!invitationData?.token) {
      setFormError("Missing invitation token.")
      setIsSubmitting(false)
      return
    }

    try {
      console.log("🚀 Submitting user registration:", {
        token: invitationData.token,
        email: invitationData.email,
        role: invitationData.role
      })

      const payload = {
        full_name: formData.fullName.trim(),
        phone_number: formData.phoneNumber,
        password: formData.password,
      }

      const response = await fetch(`${TEAM_SERVICE_URL}/invite/accept/${invitationData.token}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
        credentials: 'include'  // Important for session handling
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || "Failed to process invitation. Please try again.")
      }

      const data = await response.json()
      console.log("✅ Registration successful:", data)

      setSuccess("Account created successfully! Redirecting to login...")

      dispatch(
        addNotification({
          type: "success",
          title: "Account Created",
          message: `Your account has been created successfully. You've been added as a ${invitationData.role || 'Member'}. Please log in to continue.`,
        }),
      )

      setFormData({ fullName: "", phoneNumber: "", password: "", confirmPassword: "" })

      setTimeout(() => {
        navigate("/login", {
          state: {
            message: `Welcome! You've been added as a ${invitationData.role || 'Member'}. Please log in to continue.`,
          }
        })
      }, 2000)
    } catch (err) {
      console.error("❌ Error processing invitation:", err)
      setFormError(err.message)

      dispatch(
        addNotification({
          type: "error",
          title: "Registration Failed",
          message: err.message,
        }),
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#352090] mx-auto mb-4" />
          <p className="text-gray-600">Processing invitation...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 max-w-md">
          <div className="text-red-600 mb-2">
            <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">Invitation Error</h3>
          <p className="text-red-700">{error}</p>
          <button
            onClick={() => navigate("/login")}
            className="mt-4 px-4 py-2 bg-[#352090] text-white rounded-md hover:bg-[#2a1a70]"
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  // Existing user case is handled in useEffect with redirect
  if (isExistingUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#352090] mx-auto mb-4" />
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // New user registration form
  return (
    <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
      <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10 overflow-auto">
        <div className="w-full max-w-[426px]">
          <div className="mb-6">
            <LogoComponent />
          </div>

          <h1 className="text-[30px] font-bold mb-2">Personal Info</h1>
          <p className="text-[14px] text-black mb-6">
            Complete your profile to join the workspace as a {invitationData?.role || 'Member'} and start tracking your website's performance.
          </p>

          {formError && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-600">
              {typeof formError === 'string' ? formError : 'Registration failed. Please try again.'}
            </div>
          )}
          {success && (
            <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md text-green-600">{success}</div>
          )}

          <form onSubmit={handleSubmit} className="w-full">
            <div className="mb-5">
              <label htmlFor="fullName" className="block text-[14px] mb-2">
                Full Name
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                required
                disabled={isSubmitting}
              />
            </div>

            <div className="mb-5">
              <label htmlFor="email" className="block text-[14px] mb-2">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={invitationData?.email || ""}
                disabled
                readOnly
                className="w-full h-[48px] px-3 py-2 text-sm bg-gray-100 border border-[#D9D9D9] rounded-[12px]"
              />
            </div>

            <div className="mb-5">
              <label htmlFor="phoneNumber" className="block text-[14px] mb-2">
                Phone Number
              </label>
              <PhoneInput
                country={"us"}
                value={formData.phoneNumber}
                onChange={handlePhoneChange}
                containerStyle={phoneInputStyles.container}
                inputStyle={phoneInputStyles.inputStyle}
                buttonStyle={phoneInputStyles.buttonStyle}
                containerClass="w-full"
                disabled={isSubmitting}
                required
              />
            </div>

            <div className="mb-5">
              <label htmlFor="password" className="block text-[14px] mb-2">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                required
                disabled={isSubmitting}
              />
            </div>

            <div className="mb-6">
              <label htmlFor="confirmPassword" className="block text-[14px] mb-2">
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                required
                disabled={isSubmitting}
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 flex items-center justify-center"
            >
              {isSubmitting ? (
                <>
                  <svg
                    className="animate-spin h-5 w-5 mr-2 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z" />
                  </svg>
                  Processing...
                </>
              ) : (
                "Continue"
              )}
            </button>
          </form>
        </div>
      </div>

      <div className="hidden md:block w-1/2 relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <img
            src={SpiralImage || "/placeholder.svg"}
            alt="Spiral"
            className="w-full h-full py-4 px-4 object-cover rounded-[40px]"
          />
        </div>
      </div>
    </div>
  )
}

export default InvitedUserInfo
