import { useSelector, useDispatch } from "react-redux"
import { useNavigate, useLocation } from "react-router-dom"
import { useCallback, useEffect, useState } from "react"
import { useGetUserWorkspacesQuery, useUpdateLastAccessedWorkspaceMutation } from "../store/api/workspaceApi"
import { clusterApi } from "../store/api/clusterApi"
import { onboardingApi } from "../store/api/onboardingApi"
import { setCurrentWorkspace } from "../store/slices/workspaceSlice"
import { addNotification } from "../store/slices/uiSlice"
import { workspaceApi } from "../store/api/workspaceApi"
import {
  handleWorkspaceSwitch,
  setNavigationState
} from "../store/slices/navigationSlice"

export const useWorkspace = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const location = useLocation()
  const [isSwitching, setIsSwitching] = useState(false)

  const { currentWorkspace, loading, error } = useSelector((state) => state.workspace)
  const { isAuthenticated } = useSelector((state) => state.auth)

  const {
    data: workspaces = [],
    isLoading: workspacesLoading,
    error: workspacesError,
    refetch: refetchWorkspaces,
  } = useGetUserWorkspacesQuery(undefined, {
    skip: !isAuthenticated,
  })

  const [updateLastAccessed] = useUpdateLastAccessedWorkspaceMutation()

  // Extract workspace ID from URL
  const getWorkspaceIdFromUrl = useCallback(() => {
    const match = location.pathname.match(/\/workspace\/([^/]+)/)
    return match ? match[1] : null
  }, [location.pathname])

  // Debug workspace state
  useEffect(() => {
    console.log("🔍 useWorkspace Debug:", {
      currentWorkspace,
      workspacesCount: workspaces.length,
      urlWorkspaceId: getWorkspaceIdFromUrl(),
      pathname: location.pathname,
      isAuthenticated,
      isSwitching
    })
  }, [currentWorkspace, workspaces.length, getWorkspaceIdFromUrl, location.pathname, isAuthenticated, isSwitching])

  // Switch workspace function with enhanced backend support
  const switchWorkspace = useCallback(
    async (workspaceId, redirectPath = null) => {
      // Only prevent switching to same workspace if NOT on workspace selection page
      const isWorkspaceSelectionPage = location.pathname === "/create-workspace"
      if (isSwitching || (!isWorkspaceSelectionPage && workspaceId === currentWorkspace)) {
        console.log("Switch already in progress or same workspace (not on selection page), skipping")
        return
      }

      setIsSwitching(true)

      // Set navigation state for workspace switch
      dispatch(setNavigationState({
        isNavigating: true,
        navigationType: "inter-workspace"
      }))

      try {
        if (!workspaceId || typeof workspaceId !== "string") {
          throw new Error(`Invalid workspace ID: ${workspaceId}`)
        }

        // Check if workspace exists in user's workspaces
        const workspaceExists = workspaces.some((ws) => (ws.id || ws._id) === workspaceId)

        if (!workspaceExists) {
          throw new Error(`Workspace not found: ${workspaceId}`)
        }

        console.log("🚀 Starting workspace switch to:", workspaceId)

        // Step 1: Update last accessed workspace (backend now handles domain info automatically)
        console.log("📝 Updating last accessed workspace...")
        const result = await updateLastAccessed(workspaceId).unwrap()
        
        console.log("✅ Workspace access updated successfully:", result)

        // Update Redux state immediately
        dispatch(setCurrentWorkspace(workspaceId))
        console.log("✅ Redux state updated for workspace:", workspaceId)
        console.log("✅ Current workspace in Redux should now be:", workspaceId)

        // Update navigation state for workspace switch
        dispatch(handleWorkspaceSwitch({
          newWorkspaceId: workspaceId,
          currentPath: location.pathname
        }))

        // If result includes workspace data, the Redux slice will auto-update
        if (result && result.workspace) {
          console.log("✅ Workspace list will be updated with fresh domain info:", result.workspace.domain_info)
        }

        // Step 2: Invalidate onboarding cache for fresh check
        console.log("🎯 CACHE INVALIDATION: Invalidating onboarding cache", {
          workspaceId,
          timestamp: new Date().toISOString()
        })

        // Invalidate the specific workspace's onboarding cache
        // This will force a fresh fetch when the component mounts
        dispatch(
          onboardingApi.util.invalidateTags([
            { type: "Onboarding", id: workspaceId },
          ])
        )

        console.log("✅ CACHE INVALIDATION: Completed", {
          workspaceId,
          timestamp: new Date().toISOString()
        })

        // Step 3: Navigate to workspace onboarding check route
        console.log("🔍 WORKSPACE SWITCH: Navigating to onboarding check route", {
          workspaceId,
          currentPath: location.pathname,
          targetPath: `/workspace/${workspaceId}`,
          timestamp: new Date().toISOString()
        })

        // 🔧 FIX: Use key-based navigation to force component remount
        // This ensures OnboardingCheck component actually mounts for the new workspace
        navigate(`/workspace/${workspaceId}`, {
          replace: true,
          state: {
            forceRemount: Date.now(),
            workspaceSwitch: true
          }
        })

        console.log("✅ WORKSPACE SWITCH: Navigation completed", {
          workspaceId,
          targetPath: `/workspace/${workspaceId}`,
          timestamp: new Date().toISOString()
        })

        // Clear navigation state after successful switch
        dispatch(setNavigationState({
          isNavigating: false,
          navigationType: null
        }))

        // Show success notification with domain info if available
        const domainInfo = result?.domain_info
        const domainMessage = domainInfo ? ` with domain: ${domainInfo.siteUrl}` : ""

        dispatch(
          addNotification({
            type: "success",
            title: "Workspace Switched",
            message: `Switched to workspace successfully${domainMessage}`,
          }),
        )
      } catch (error) {
        console.error("❌ Error switching workspace:", error)

        // Clear navigation state on error
        dispatch(setNavigationState({
          isNavigating: false,
          navigationType: null
        }))

        dispatch(
          addNotification({
            type: "error",
            title: "Switch Failed",
            message: error.message || "Failed to switch workspace",
          }),
        )
      } finally {
        setIsSwitching(false)

        // Ensure navigation state is cleared in finally block
        dispatch(setNavigationState({
          isNavigating: false,
          navigationType: null
        }))
      }
    },
    [workspaces, updateLastAccessed, navigate, dispatch, isSwitching, currentWorkspace, location.pathname],
  )

  // Get workspace by ID
  const getWorkspaceById = useCallback(
    (workspaceId) => {
      return workspaces.find((ws) => (ws.id || ws._id) === workspaceId)
    },
    [workspaces],
  )

  // Get workspace IDs for compatibility
  const getWorkspaceIds = useCallback(() => {
    return workspaces.map((ws) => ws.id || ws._id)
  }, [workspaces])

  // Get current workspace ID (with URL fallback) - Same pattern as workspace-dropdown
  const getCurrentWorkspaceId = useCallback(() => {
    // Primary: Use Redux currentWorkspace if available
    if (currentWorkspace) {
      return currentWorkspace
    }

    // Secondary: Extract from URL and validate against loaded workspaces
    const urlWorkspaceId = getWorkspaceIdFromUrl()
    if (urlWorkspaceId && workspaces.length > 0) {
      const urlWorkspaceExists = workspaces.some((ws) => (ws.id || ws._id) === urlWorkspaceId)

      if (urlWorkspaceExists) {
        // Immediately set workspace in Redux if valid and not switching
        if (!isSwitching) {
          console.log("🔧 getCurrentWorkspaceId: Setting workspace from URL immediately:", urlWorkspaceId)
          dispatch(setCurrentWorkspace(urlWorkspaceId))
        }
        return urlWorkspaceId
      }
    }

    // Tertiary: If workspaces are loaded but no current workspace, use first workspace
    if (workspaces.length > 0 && !isSwitching) {
      const firstWorkspaceId = workspaces[0].id || workspaces[0]._id
      console.log("🔧 getCurrentWorkspaceId: Using first workspace as fallback:", firstWorkspaceId)
      dispatch(setCurrentWorkspace(firstWorkspaceId))
      return firstWorkspaceId
    }

    return null
  }, [currentWorkspace, getWorkspaceIdFromUrl, workspaces, dispatch, isSwitching])

  // Check if navigation is within the same workspace
  const isIntraWorkspaceNavigation = useCallback((targetPath) => {
    const currentWorkspaceId = getCurrentWorkspaceId()
    if (!currentWorkspaceId) return false

    const targetWorkspaceMatch = targetPath.match(/\/workspace\/([^/]+)/)
    const targetWorkspaceId = targetWorkspaceMatch ? targetWorkspaceMatch[1] : null

    return targetWorkspaceId === currentWorkspaceId
  }, [getCurrentWorkspaceId])

  // Optimized navigation function for intra-workspace navigation
  const navigateWithinWorkspace = useCallback((path) => {
    if (isIntraWorkspaceNavigation(path)) {
      console.log("🚀 Intra-workspace navigation:", path)
      navigate(path)
      return true
    }
    return false
  }, [isIntraWorkspaceNavigation, navigate])

  // Initialize current workspace when workspaces are loaded - Optimized pattern
  useEffect(() => {
    // Skip during workspace switching or loading to prevent conflicts
    if (isSwitching || workspacesLoading) {
      return
    }

    // Only initialize if we have workspaces loaded
    if (workspaces.length > 0) {
      const urlWorkspaceId = getWorkspaceIdFromUrl()
      const urlWorkspaceExists = workspaces.some((ws) => (ws.id || ws._id) === urlWorkspaceId)

      // Priority 1: URL has valid workspace but Redux doesn't match - sync immediately
      if (urlWorkspaceId && urlWorkspaceExists && urlWorkspaceId !== currentWorkspace) {
        console.log("🔧 Workspace sync: URL workspace differs from Redux, syncing:", {
          urlWorkspace: urlWorkspaceId,
          currentWorkspace: currentWorkspace,
          action: "updating Redux to match URL"
        })
        dispatch(setCurrentWorkspace(urlWorkspaceId))
        return
      }

      // Priority 2: No current workspace set - use URL workspace if valid
      if (!currentWorkspace && urlWorkspaceId && urlWorkspaceExists) {
        console.log("🔧 Workspace init: Using workspace ID from URL:", urlWorkspaceId)
        dispatch(setCurrentWorkspace(urlWorkspaceId))
        return
      }

      // Priority 3: No current workspace and no valid URL - use first workspace
      if (!currentWorkspace) {
        const firstWorkspaceId = workspaces[0].id || workspaces[0]._id
        console.log("🔧 Workspace init: Setting first workspace as current:", firstWorkspaceId)
        dispatch(setCurrentWorkspace(firstWorkspaceId))
        return
      }
    }
  }, [workspaces, workspacesLoading, currentWorkspace, getWorkspaceIdFromUrl, dispatch, isSwitching])

  return {
    currentWorkspace,
    workspaces,
    workspaceIds: getWorkspaceIds(),
    loading: workspacesLoading || loading || isSwitching,
    error: workspacesError || error,
    switchWorkspace,
    getWorkspaceById,
    refetchWorkspaces,
    updateLastAccessedWorkspace: updateLastAccessed,
    isSwitching,
    // New navigation optimization functions
    getCurrentWorkspaceId,
    isIntraWorkspaceNavigation,
    navigateWithinWorkspace,
  }
}
