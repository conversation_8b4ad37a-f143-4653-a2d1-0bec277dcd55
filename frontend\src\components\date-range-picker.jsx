import { useState, useRef, useEffect } from "react"
import { Calendar } from "lucide-react"

const DateRangePicker = ({ 
  isOpen, 
  onClose, 
  onApply, 
  defaultPreset = null, 
  autoApply = false,
  currentDateRange = null
}) => {
  const [fromDate, setFromDate] = useState("15.05.2024")
  const [toDate, setToDate] = useState("20.05.2024")
  const [activePreset, setActivePreset] = useState(defaultPreset || null)
  const modalRef = useRef(null)
  
  // Set default dates or use current date range when component mounts/updates
  useEffect(() => {
    if (currentDateRange && currentDateRange.fromDate && currentDateRange.toDate) {
      // Use the passed date range
      setFromDate(currentDateRange.fromDate)
      setToDate(currentDateRange.toDate)
      setActivePreset(currentDateRange.preset || null)
    } else if (defaultPreset) {
      // If no current range but default preset is provided, set that range
      handlePresetClick(defaultPreset, false)
    } else {
      // Initialize with today's date
      const today = new Date()
      const todayStr = formatDateToDisplay(today)
      setToDate(todayStr)
    }
  }, [currentDateRange, defaultPreset, isOpen])

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose])

  // Format date as DD.MM.YYYY for display
  const formatDateToDisplay = (date) => {
    const day = String(date.getDate()).padStart(2, "0")
    const month = String(date.getMonth() + 1).padStart(2, "0")
    const year = date.getFullYear()
    return `${day}.${month}.${year}`
  }

  // Handle preset selection
  const handlePresetClick = (preset, shouldAutoApply = autoApply) => {
    setActivePreset(preset)

    const today = new Date()
    const from = new Date()
    const to = new Date(today)

    switch (preset) {
      case "today":
        // Both from and to are today
        break
      case "7days":
        from.setDate(today.getDate() - 7)
        break
      case "15days":
        from.setDate(today.getDate() - 15)
        break
      case "30days":
        from.setDate(today.getDate() - 30)
        break
      default:
        break
    }

    // Format dates for display
    const formattedFrom = formatDateToDisplay(from)
    const formattedTo = formatDateToDisplay(to)
    
    // Update the displayed dates
    setFromDate(formattedFrom)
    setToDate(formattedTo)
    
    // Auto-apply if enabled
    if (shouldAutoApply) {
      // Call the apply function with the preset information
      handleApply(preset)
    }
  }

  // Apply selected date range
  const handleApply = (presetUsed = null) => {
    onApply({ 
      fromDate, 
      toDate, 
      preset: presetUsed || activePreset 
    })
    onClose()
  }
  
  // Handle manual date input change
  const handleDateInputChange = (type, value) => {
    if (type === 'from') {
      setFromDate(value)
      // Clear preset when manually changing dates
      setActivePreset(null)
      
      // Auto-apply if enabled
      if (autoApply) {
        // Small delay to ensure UI updates first
        setTimeout(() => onApply({ fromDate: value, toDate, preset: null }), 100)
      }
    } else {
      setToDate(value)
      // Clear preset when manually changing dates
      setActivePreset(null)
      
      // Auto-apply if enabled
      if (autoApply) {
        // Small delay to ensure UI updates first
        setTimeout(() => onApply({ fromDate, toDate: value, preset: null }), 100)
      }
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white shadow-lg w-full max-w-[360px] p-5">
        {/* Header */}
        <div className="flex justify-between mb-4">
          <div className="text-gray-800 font-medium">From</div>
          <div className="text-gray-800 font-medium">To</div>
        </div>

        {/* Date inputs */}
        <div className="flex items-center mb-4">
          <div className="flex-1 relative">
            <div className="flex items-center border border-[#DCDCDC] p-2 rounded-lg">
              <Calendar className="w-5 h-5 text-gray-400 mr-2" />
              <input
                type="text"
                value={fromDate}
                onChange={(e) => handleDateInputChange('from', e.target.value)}
                className="w-full outline-none text-sm"
              />
            </div>
          </div>

          <div className="mx-2 text-gray-400">-</div>

          <div className="flex-1 relative">
            <div className="flex items-center border border-[#DCDCDC] p-2 rounded-lg">
              <Calendar className="w-5 h-5 text-gray-400 mr-2" />
              <input
                type="text"
                value={toDate}
                onChange={(e) => handleDateInputChange('to', e.target.value)}
                className="w-full outline-none text-sm"
              />
            </div>
          </div>
        </div>

        {/* Preset buttons */}
        <div className="space-y-3">
          <button
            className={`w-full py-3 text-center rounded-lg ${
              activePreset === "today"
                ? "bg-[#352090] text-white"
                : "border border-[#DCDCDC] text-gray-800 hover:bg-gray-50"
            }`}
            onClick={() => handlePresetClick("today")}
          >
            Today
          </button>

          <button
            className={`w-full py-3 text-center rounded-lg ${
              activePreset === "7days"
                ? "bg-[#352090] text-white"
                : "border border-[#DCDCDC] text-gray-800 hover:bg-gray-50"
            }`}
            onClick={() => handlePresetClick("7days")}
          >
            Last 7 days
          </button>

          <button
            className={`w-full py-3 text-center rounded-lg ${
              activePreset === "15days"
                ? "bg-[#352090] text-white"
                : "border border-[#DCDCDC] text-gray-800 hover:bg-gray-50"
            }`}
            onClick={() => handlePresetClick("15days")}
          >
            Last 15 days
          </button>

          <button
            className={`w-full py-3 text-center rounded-lg ${
              activePreset === "30days"
                ? "bg-[#352090] text-white"
                : "border border-[#DCDCDC] text-gray-800 hover:bg-gray-50"
            }`}
            onClick={() => handlePresetClick("30days")}
          >
            Last 30 days
          </button>
        </div>
        
        {/* Only show apply button if auto-apply is disabled */}
        {!autoApply && (
          <div className="mt-4">
            <button
              className="w-full py-3 bg-[#352090] text-white rounded-lg"
              onClick={() => handleApply()}
            >
              Apply
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default DateRangePicker
