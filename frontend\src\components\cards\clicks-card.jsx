import MetricCard from "./metric-card"

const ClicksCard = ({ clicks, comparisonClicks, onToggle, isChecked, title = "Total Clicks", disabled, dateRange }) => {
  return (
    <MetricCard
      title={title}
      value={clicks}
      comparisonValue={comparisonClicks}
      color="blue"
      onToggle={onToggle}
      isChecked={isChecked}
      disabled={disabled}
      dateRange={dateRange}
    />
  )
}

export default ClicksCard
