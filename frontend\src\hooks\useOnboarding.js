import { useCallback } from "react"
import { useNavigate } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useCheckOnboardingStatusQuery } from "../store/api/onboardingApi"
import { addNotification } from "../store/slices/uiSlice"
import { updateWorkspaceState } from "../store/slices/onboardingSlice"

export const useOnboarding = (workspaceId) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { workspaceStates } = useSelector((state) => state.onboarding)

  const {
    data: onboardingData,
    isLoading,
    error,
    refetch,
  } = useCheckOnboardingStatusQuery(workspaceId, {
    skip: !workspaceId, // Only fetch when workspaceId is provided
  })

  // Navigate based on backend response
  const navigateToStep = useCallback(
    (onboardingResponse = onboardingData) => {
      if (!onboardingResponse || !workspaceId) return

      const { completed, redirect_to, current_step, domain_info } = onboardingResponse

      console.log("Onboarding navigation:", {
        completed,
        redirect_to,
        current_step,
        workspaceId,
        domain_info,
      })

      // Update workspace state in Redux
      dispatch(updateWorkspaceState({
        workspaceId,
        onboardingState: {
          isCompleted: completed,
          currentStep: current_step,
          domainInfo: domain_info,
        }
      }))

      if (redirect_to) {
        navigate(redirect_to)
      } else {
        // Fallback navigation based on current_step
        const fallbackPaths = {
          google_auth: `/google_auth/${workspaceId}`,
          site_selection: `/workspace/${workspaceId}/site_selection`,
          clusters: `/workspace/${workspaceId}/clusters`,
        }

        const fallbackPath = fallbackPaths[current_step] || `/workspace/${workspaceId}/clusters`
        navigate(fallbackPath)
      }
    },
    [navigate, onboardingData, workspaceId, dispatch],
  )

  // Check onboarding and navigate - simplified version
  const checkAndNavigate = useCallback(
    async () => {
      if (!workspaceId) {
        console.warn("No workspace ID provided to checkAndNavigate")
        return
      }

      try {
        const response = await refetch()
        if (response.data) {
          console.log("Onboarding data fetched:", response.data)
          // The OnboardingCheck component will handle navigation
          // This is mainly for manual refetch scenarios
        }
      } catch (error) {
        console.error("Error checking onboarding:", error)
        dispatch(
          addNotification({
            type: "error",
            title: "Navigation Error",
            message: "Failed to check onboarding status",
          }),
        )
      }
    },
    [refetch, workspaceId, dispatch],
  )

  // Get current workspace state
  const currentWorkspaceState = workspaceId ? workspaceStates[workspaceId] : null

  return {
    onboardingData,
    isLoading,
    error,
    navigateToStep,
    checkAndNavigate,
    refetch,
    workspaceState: currentWorkspaceState,
  }
}
