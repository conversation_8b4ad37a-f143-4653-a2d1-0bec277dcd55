import { memo } from "react"
import { useGetLinkPerformanceQuery } from "../store/api/linkApi"
import { Loader } from "lucide-react"

const MetricDisplay = ({ label, value, color }) => (
  <div className="flex flex-col items-center">
    <span className="text-xs text-gray-500">{label}</span>
    <span className="text-base font-semibold" style={{ color }}>
      {value.toLocaleString()}
    </span>
  </div>
)

const LinkPerformanceSparkline = memo(({ 
  linkId, 
  clusterId, 
  metricType = "clicks",
  startDate: providedStartDate,
  endDate: providedEndDate,
  isLoading: externalLoading = false
}) => {
  console.log("LinkPerformanceSparkline - linkId:", linkId, "clusterId:", clusterId, "metricType:", metricType)

  // Use provided dates - they are now required
  const startDate = providedStartDate;
  const endDate = providedEndDate;

  // Fetch performance data with caching
  const {
    data: performanceResponse,
    isLoading: queryLoading,
    error,
  } = useGetLinkPerformanceQuery(
    {
      linkId,
      clusterId,
      startDate,
      endDate,
      perPage: 30,
    },
    {
      skip: !linkId || !clusterId || !startDate || !endDate,
      // Refetch every 5 minutes
      pollingInterval: 300000,
    },
  )

  console.log("LinkPerformanceSparkline - Query state:", {
    queryLoading,
    externalLoading,
    error,
    performanceResponse,
    skip: !linkId || !clusterId || !startDate || !endDate,
  })

  const performanceData = performanceResponse?.data || []

  // Show loading state if either external loading or query loading is true
  if (externalLoading || queryLoading) {
    return (
      <div className="flex flex-col items-center justify-center text-amber-600">
        <Loader className="w-5 h-5 animate-spin" />
      </div>
    )
  }

  if (error || !performanceData || performanceData.length === 0) {
    return (
      <div className="flex items-center justify-center text-xs text-gray-400">
        No data
      </div>
    )
  }

  // Calculate total for the selected metric
  const total = performanceData.reduce((sum, d) => sum + (d[metricType] || 0), 0)

  // Define metric-specific properties
  const metricProps = {
    clicks: {
      label: "Clicks",
      color: "#352090",
    },
    impressions: {
      label: "Impressions",
      color: "#AD66FF",
    },
  }[metricType]

  return (
    <div className="flex items-center justify-center">
      <MetricDisplay
        label={metricProps.label}
        value={total}
        color={metricProps.color}
      />
    </div>
  )
})

LinkPerformanceSparkline.displayName = "LinkPerformanceSparkline"

export default LinkPerformanceSparkline
