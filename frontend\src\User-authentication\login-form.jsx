import { useState, useEffect } from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { useDispatch } from "react-redux"
import LogoComponent from "../components/logo-component"
import SpiralImage from "../assets/image.png"
import GlowLoading from "/Glow loading.gif"
import { Switch } from "../components/switch-component"
import { useLoginMutation } from "../store/api/authApi"
import { addNotification } from "../store/slices/uiSlice"
import { setCurrentWorkspace } from "../store/slices/workspaceSlice"
import { updateWorkspaceState } from "../store/slices/onboardingSlice"

const LoginForm = () => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [fieldErrors, setFieldErrors] = useState({
    email: "",
    password: ""
  })

  const dispatch = useDispatch()
  const navigate = useNavigate()

  const [login, { isLoading }] = useLoginMutation()

  // Clear errors when component mounts
  useEffect(() => {
    setFieldErrors({ email: "", password: "" })
  }, [])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setFieldErrors({ email: "", password: "" })

    try {

      const result = await login({
        email,
        password,
        remember_me: rememberMe,
      }).unwrap()

      // Handle workspace and onboarding state
      if (result.workspaces && result.workspaces.length > 0) {
        // Update workspace states in Redux
        result.workspaces.forEach(workspace => {
          dispatch(updateWorkspaceState({
            workspaceId: workspace.workspace_id,
            onboardingState: workspace.onboarding_state
          }))
        })

        // New navigation logic based on workspace count
        if (result.workspaces.length === 1) {
          // Single workspace - set current and navigate to onboarding check
          const workspace = result.workspaces[0]
          const workspaceId = workspace.workspace_id

          dispatch(setCurrentWorkspace(workspaceId))

          dispatch(addNotification({
            type: "success",
            message: "Welcome back! Redirecting to your workspace...",
          }))

          // Navigate directly to the onboarding check route with workspace context
          navigate(`/workspace/${workspaceId}`)
        } else {
          // Multiple workspaces - let user choose
          dispatch(addNotification({
            type: "info",
            message: "Please select a workspace to continue.",
          }))
          navigate("/create-workspace")
        }
      } else {
        // No workspaces yet
        dispatch(addNotification({
          type: "success",
          message: "Welcome! Let's set up your first workspace.",
        }))
        navigate("/create-workspace")
      }

    } catch (error) {

      let errorMessage = "Login failed. Please try again."

      if (error.status === 401 && error.data?.detail?.includes("Email not verified")) {
        setFieldErrors(prev => ({ ...prev, email: error.data.detail }))
      } else if (error.data?.detail) {
        // Check if error is related to specific fields
        if (error.data.detail.toLowerCase().includes("email")) {
          setFieldErrors(prev => ({ ...prev, email: error.data.detail }))
        } else if (error.data.detail.toLowerCase().includes("password")) {
          setFieldErrors(prev => ({ ...prev, password: error.data.detail }))
        } else {
          // If error is general, show in both fields
          setFieldErrors({
            email: error.data.detail,
            password: error.data.detail
          })
        }
      } else if (error.message) {
        setFieldErrors({
          email: error.message,
          password: error.message
        })
      }

      dispatch(
        addNotification({
          type: "error",
          message: errorMessage,
        }),
      )
    }
  }

  return (
  <div className="flex flex-col md:flex-row min-h-screen w-full font-['Noto Sans']">
    {/* Left Side: Logo + Form */}
    <div className="relative w-full md:w-full lg:w-1/2 flex items-start md:items-center justify-center p-4 md:p-6 lg:p-8">
      <div className="w-full max-w-[426px] space-y-3 md:space-y-4 py-2">
        {/* Logo */}
        <div className="flex-shrink-0">
          <LogoComponent />
        </div>

        {/* Title */}
        <h2 className="text-[36px] md:text-[48px] lg:text-[60px] leading-[1.2] font-brantford font-[300] text-gray-400 tracking-normal">
          Google Search
          <br />
          <span className="inline-flex gap-1">
            Console <span className="text-[#352090] font-[600]">Tracker</span>
          </span>
        </h2>

        {/* Heading */}
        <h1 className="text-[24px] md:text-[30px] leading-none font-bold text-black">
          Login
        </h1>

        {/* Paragraph */}
        <p className="text-[14px] leading-relaxed text-black">
          Access your personalized SEO insights and manage your website's
          performance with ease.
        </p>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4">
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-[14px] leading-none text-black mb-1.5">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              className={`w-full h-11 px-3 text-[14px] leading-none bg-white border ${
                fieldErrors.email ? "border-red-500" : "border-[#D9D9D9]"
              } rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
              required
              disabled={isLoading}
            />
            {fieldErrors.email && (
              <div className="flex items-center gap-1.5 mt-1.5">
                <svg
                  className="w-4 h-4 text-red-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                <p className="text-sm text-red-600">{fieldErrors.email}</p>
              </div>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-[14px] leading-none text-black mb-1.5">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              className={`w-full h-11 px-3 text-[14px] leading-none bg-white border ${
                fieldErrors.password ? "border-red-500" : "border-[#D9D9D9]"
              } rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
              required
              disabled={isLoading}
            />
            {fieldErrors.password && (
              <div className="flex items-center gap-1.5 mt-1.5">
                <svg
                  className="w-4 h-4 text-red-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                <p className="text-sm text-red-600">{fieldErrors.password}</p>
              </div>
            )}
          </div>

          {/* Switch + Forgot Password */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Switch
                checked={rememberMe}
                onCheckedChange={setRememberMe}
                name="remember_me"
                disabled={isLoading}
              />
              <label htmlFor="remember_me" className="ml-2 text-[14px] leading-none text-black">
                Remember me
              </label>
            </div>
            <Link to="/forgot-password" className="text-[14px] leading-none text-[#266DF0] hover:underline">
              Forgot Password
            </Link>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full h-12 bg-[#352090] text-white text-[16px] leading-none font-bold rounded-[12px] hover:bg-[#2a1a70] transition duration-300 disabled:opacity-50 mt-2 flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <img
                  src={GlowLoading}
                  alt="Loading..."
                  className="w-6 h-6 mr-2"
                />
                Logging in...
              </>
            ) : (
              "Login"
            )}
          </button>
        </form>

        {/* Sign Up Link */}
        <div className="text-center">
          <p className="text-[14px] leading-none text-black">
            Don't have an account?
            <Link to="/signup" className="ml-1 font-bold text-[#266DF0] hover:underline">
              Sign Up
            </Link>
          </p>
        </div>
      </div>
    </div>

    {/* Right Side: Spiral Image */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <div className="absolute inset-0 m-5">
          <img
            src={SpiralImage || "/placeholder.svg"}
            alt="Decorative spiral"
            className="w-full h-full object-cover rounded-[40px]"
          />
        </div>
      </div>
  </div>
  )
}

export default LoginForm
