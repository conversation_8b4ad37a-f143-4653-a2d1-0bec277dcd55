import { useState, useEffect } from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { useDispatch } from "react-redux"
import LogoComponent from "../components/logo-component"
import SpiralImage from "../assets/image.png"
import { Switch } from "../components/switch-component"
import { useLoginMutation } from "../store/api/authApi"
import { addNotification } from "../store/slices/uiSlice"
import { setCurrentWorkspace } from "../store/slices/workspaceSlice"
import { updateWorkspaceState } from "../store/slices/onboardingSlice"

const LoginForm = () => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [localError, setLocalError] = useState("")

  const dispatch = useDispatch()
  const navigate = useNavigate()

  const [login, { isLoading }] = useLoginMutation()

  // Clear local error when component mounts
  useEffect(() => {
    setLocalError("")
  }, [])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLocalError("")

    try {
      console.log("Starting login process...")

      const result = await login({
        email,
        password,
        remember_me: rememberMe,
      }).unwrap()

      console.log("Login successful:", result)

      // Handle workspace and onboarding state
      if (result.workspaces && result.workspaces.length > 0) {
        // Update workspace states in Redux
        result.workspaces.forEach(workspace => {
          dispatch(updateWorkspaceState({
            workspaceId: workspace.workspace_id,
            onboardingState: workspace.onboarding_state
          }))
        })

        // New navigation logic based on workspace count
        if (result.workspaces.length === 1) {
          // Single workspace - set current and navigate to onboarding check
          const workspace = result.workspaces[0]
          const workspaceId = workspace.workspace_id

          console.log("Setting current workspace:", workspaceId)
          dispatch(setCurrentWorkspace(workspaceId))

          dispatch(addNotification({
            type: "success",
            message: "Welcome back! Redirecting to your workspace...",
          }))

          // Navigate directly to the onboarding check route with workspace context
          navigate(`/workspace/${workspaceId}`)
        } else {
          // Multiple workspaces - let user choose
          dispatch(addNotification({
            type: "info",
            message: "Please select a workspace to continue.",
          }))
          navigate("/create-workspace")
        }
      } else {
        // No workspaces yet
        dispatch(addNotification({
          type: "success",
          message: "Welcome! Let's set up your first workspace.",
        }))
        navigate("/create-workspace")
      }

    } catch (error) {
      console.error("Login Error:", error)

      let errorMessage = "Login failed. Please try again."

      if (error.status === 401 && error.data?.detail?.includes("Email not verified")) {
        errorMessage = error.data.detail
      } else if (error.data?.detail) {
        errorMessage = error.data.detail
      } else if (error.message) {
        errorMessage = error.message
      }

      setLocalError(errorMessage)
      dispatch(
        addNotification({
          type: "error",
          message: errorMessage,
        }),
      )
    }
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
      {/* Left Side: Logo + Form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10 overflow-auto">
        <div className="w-full max-w-[426px]">
          {/* Logo */}
          <div className="mb-6">
            <LogoComponent />
          </div>

          {/* Title */}
          <h2 className="text-[60px] leading-[70px] font-brantford font-[300] text-gray-400 mb-4 w-[502px] h-[140px]">
            Google Search
            <br />
            <span className="flex gap-1">
              Console <span className="text-[#352090] font-[600]">Tracker</span>
            </span>
          </h2>

          {/* Heading */}
          <h1 className="text-[30px] leading-[100%] font-bold text-black mb-2 w-[502px] h-[41px] font-noto">Login</h1>

          {/* Paragraph */}
          <p className="text-[14px] leading-[100%] text-black mb-6">
            Access your personalized SEO insights and manage your website's performance with ease.
          </p>

          {/* Display error if any */}
          {localError && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-600">{localError}</div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="w-full space-y-5">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-[14px] leading-[100%] text-black mb-1">
                Email
              </label>
              <input
                type="text"
                id="email"
                name="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                required
                disabled={isLoading}
              />
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-[14px] leading-[100%] text-black mb-1">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                required
                disabled={isLoading}
              />
            </div>

            {/* Switch + Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="mr-2 flex items-center justify-center">
                  <Switch
                    checked={rememberMe}
                    onCheckedChange={(checked) => setRememberMe(checked)}
                    name="remember_me"
                    disabled={isLoading}
                  />
                </div>
                <label htmlFor="remember_me" className="text-[16px] leading-[100%] text-black">
                  Remember me
                </label>
              </div>
              <Link to="/forgot-password" className="text-[16px] leading-[100%] text-[#266DF0] hover:underline">
                Forgot Password
              </Link>
            </div>

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin h-4 w-4 mr-2 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z" />
                    </svg>
                    Logging in...
                  </>
                ) : (
                  "Login"
                )}
              </button>
            </div>
          </form>

          {/* Sign Up Link */}
          <div className="mt-4 text-center">
            <p className="text-[16px] leading-[100%] text-black">
              Don&apos;t have an account?
              <Link to="/signup" className="ml-1 font-bold text-[#352090] hover:underline">
                Sign Up
              </Link>
            </p>
          </div>
        </div>
      </div>

      {/* Right Side: Spiral Image */}
      <div className="hidden md:block md:w-1/2 relative">
        <div className="absolute inset-0 m-5">
          <img
            src={SpiralImage || "/placeholder.svg"}
            alt="Decorative spiral"
            className="w-full h-full object-cover rounded-[40px]"
          />
        </div>
      </div>
    </div>
  )
}

export default LoginForm
