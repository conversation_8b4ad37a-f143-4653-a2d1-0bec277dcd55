import { apiSlice } from "./apiSlice"

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE || "http://localhost:8000"
const CLUSTERGAZER_SERVICE_URL = import.meta.env.VITE_CLUSTERGAZER_SERVICE || "http://localhost:8001"

export const onboardingApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Check onboarding status (workspace required)
    checkOnboardingStatus: builder.query({
      query: (workspaceId) => {
        // 🔧 FIX: Validate workspace ID to prevent invalid API calls
        if (!workspaceId || workspaceId === 'undefined') {
          throw new Error('Invalid workspace ID provided to checkOnboardingStatus')
        }
        return {
          url: `/auth/check-onboarding?workspace_id=${workspaceId}`,
          baseUrl: USER_SERVICE_URL,
        }
      },
      providesTags: (result, error, workspaceId) => [{ type: "Onboarding", id: workspaceId }],
      // 🔧 FIX: Add keepUnusedDataFor to prevent unnecessary refetches
      keepUnusedDataFor: 30, // Keep cached data for 30 seconds
    }),

    // Google Auth - Get authorization URL
    getGoogleAuthUrl: builder.query({
      query: (workspaceId) => ({
        url: `/google_auth/authorize/${workspaceId}`,
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: (result, error, workspaceId) => [{ type: "Onboarding", id: workspaceId }],
    }),

    // Get GSC sites for workspace
    getGSCSites: builder.query({
      query: (workspaceId) => ({
        url: `/domain/sites?workspace_id=${workspaceId}`,
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      providesTags: (result, error, workspaceId) => [
        { type: "GSCData", id: workspaceId },
        { type: "Onboarding", id: workspaceId }
      ],
    }),

    // Select site for workspace
    selectSite: builder.mutation({
      query: ({ siteUrl, workspaceId }) => ({
        url: "/domain/site-selection",
        method: "POST",
        body: {
          site_url: siteUrl,
          workspace_id: workspaceId,
        },
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      invalidatesTags: (result, error, { workspaceId }) => [
        { type: "Onboarding", id: workspaceId },
        { type: "GSCData", id: workspaceId }
      ],
    }),

    // Check OAuth tokens for workspace
    checkOAuthTokens: builder.query({
      query: (workspaceId) => ({
        url: `/google_auth/check-oauth-tokens?workspace_id=${workspaceId}`,
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: (result, error, workspaceId) => [{ type: "Onboarding", id: workspaceId }],
    }),
  }),
})

export const {
  useCheckOnboardingStatusQuery,
  useGetGoogleAuthUrlQuery,
  useGetGSCSitesQuery,
  useSelectSiteMutation,
  useCheckOAuthTokensQuery,
} = onboardingApi
