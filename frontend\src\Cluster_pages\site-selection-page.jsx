import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useWorkspace } from "../hooks/useWorkspace";
import { useGetGSCSitesQuery, useSelectSiteMutation } from "../store/api/onboardingApi";
import { addNotification } from "../store/slices/uiSlice";
import { workspaceApi } from "../store/api/workspaceApi";
import { onboardingApi } from "../store/api/onboardingApi";
import { clusterApi } from "../store/api/clusterApi";
import Navbar from "../components/navbar";

const SiteSelectionPage = () => {
  const [selectedDomain, setSelectedDomain] = useState(""); 
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { workspaceId } = useParams();
  const { currentWorkspace } = useWorkspace();

  // Use workspaceId from params or current workspace
  const activeWorkspaceId = workspaceId || currentWorkspace;

  // Fetch GSC sites
  const { data: sitesData, isLoading, error, refetch } = useGetGSCSitesQuery(activeWorkspaceId, {
    skip: !activeWorkspaceId,
  });

  // Select site mutation
  const [selectSite] = useSelectSiteMutation();

  // Set default selected domain when sites are loaded
  useEffect(() => {
    if (sitesData?.sites && sitesData.sites.length === 1 && !selectedDomain) {
      // If only one site is available and not already selected, set it as the default
      setSelectedDomain(sitesData.sites[0].siteUrl);
    }
  }, [sitesData, selectedDomain]);

  const handleDomainSelect = (domainUrl) => {
    setSelectedDomain(domainUrl); // Enable Continue button when a domain is selected
  };

  const handleContinue = async () => {
    if (!selectedDomain) {
      dispatch(
        addNotification({
          type: "warning",
          title: "Selection Required",
          message: "Please select a domain to continue",
        })
      );
      return;
    }

    if (!activeWorkspaceId) {
      dispatch(
        addNotification({
          type: "error",
          title: "Workspace Error",
          message: "Workspace ID is missing",
        })
      );
      return;
    }

    if (isSubmitting) return; // Prevent multiple submissions

    try {
      setIsSubmitting(true);

      // Step 1: Select site
      console.log("🔄 Selecting site:", selectedDomain);
      const result = await selectSite({
        siteUrl: selectedDomain,
        workspaceId: activeWorkspaceId,
      }).unwrap();

      // Step 2: Force cache invalidation
      console.log("🔄 Invalidating caches...");
      dispatch(workspaceApi.util.invalidateTags(["Workspace"]));
      dispatch(onboardingApi.util.invalidateTags(["Onboarding"]));
      dispatch(clusterApi.util.invalidateTags(["Clusters"]));

      // Step 3: Wait for cache invalidation
      console.log("⏳ Waiting for cache updates...");
      await new Promise((resolve) => setTimeout(resolve, 1000));

      dispatch(
        addNotification({
          type: "success",
          title: "Site Selected",
          message: "Site selected successfully!",
        })
      );

      // Step 4: Navigate to clusters page
      console.log("✅ Navigating to clusters page");
      navigate(`/workspace/${activeWorkspaceId}/clusters`, {
        state: { selectedDomain },
        replace: true,
      });
    } catch (error) {
      console.error("Error selecting site:", error);
      dispatch(
        addNotification({
          type: "error",
          title: "Selection Failed",
          message: error.data?.message || "Failed to select site. Please try again.",
        })
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <main className="flex-1 container mx-auto px-4 py-8 max-w-5xl">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090]"></div>
            <p className="mt-4 text-gray-600">Loading sites...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    let errorMessage = "Failed to fetch sites. Please try again later.";
    if (error.data?.detail) {
      errorMessage = error.data.detail; 
    } else if (error.error?.message) {
      errorMessage = error.error.message; 
    }

    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <main className="flex-1 container mx-auto px-4 py-8 max-w-5xl">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="p-4 bg-red-50 rounded-lg">
              <p className="text-red-600 text-center">
                {errorMessage}
                <button onClick={refetch} className="block mt-4 text-[#352090] hover:text-[#2a1a70] font-medium">
                  Try Again
                </button>
              </p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-8 max-w-5xl mt-20">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Choose a Site</h1>
          <h2 className="text-lg font-bold text-[#352090]">Select a Domain Property</h2>
        </div>

        <div className="space-y-4 mb-8">
          {sitesData.sites.map((domain, index) => {
            // Disable domain if permissionLevel is 'siteUnverifiedUser'
            const isDisabled = domain.permissionLevel === "siteUnverifiedUser";
            const isSelectable = domain.isSelectable && !isDisabled;

            return (
              <div
                key={index}
                className={`flex items-center justify-between p-4 rounded-[12px] cursor-pointer transition-colors duration-200 
                ${isSelectable ? "bg-[#F8F7FC] hover:bg-[#F0EEF9]" : "bg-gray-200 cursor-not-allowed"}`}
                onClick={() => isSelectable && handleDomainSelect(domain.siteUrl)}
                style={{ pointerEvents: isSelectable ? "auto" : "none" }} 
              >
                <div className="flex items-center">
                  <div
                    className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mr-4 ${
                      selectedDomain === domain.siteUrl ? "border-[#352090] bg-white" : "border-gray-300 bg-white"
                    }`}
                  >
                    {selectedDomain === domain.siteUrl && <div className="w-3 h-3 rounded-full bg-[#352090]"></div>}
                  </div>
                  <div>
                    <p className="font-medium text-gray-800">{domain.siteUrl}</p>
                    <p className="text-sm text-gray-500">Permission: {domain.permissionLevel || "N/A"}</p>
                  </div>
                </div>

                {domain.isAssociated && <span className="px-4 py-1 bg-green-500 text-white text-sm rounded-full">Associated</span>}
                {isDisabled && <span className="px-4 py-1 bg-gray-400 text-white text-sm rounded-full">Disabled</span>}
              </div>
            );
          })}
        </div>

        <div className="flex justify-end">
          <button
            onClick={handleContinue}
            disabled={isSubmitting || !selectedDomain || sitesData.sites.every((domain) => !domain.isSelectable)}
            className="px-6 py-3 bg-[#352090] text-white font-noto rounded-[8px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                Processing...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </main>
    </div>
  );
};

export default SiteSelectionPage;
