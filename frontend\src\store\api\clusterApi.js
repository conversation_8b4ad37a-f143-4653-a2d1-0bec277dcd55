import { apiSlice } from "./apiSlice"
import { 
  setWorkspaceClusters, 
  addWorkspaceCluster, 
  removeWorkspace<PERSON>luster 
} from "../slices/clusterSlice"
import { clusterMovedToTrash } from "../slices/trashSlice"
import {getComparisonDates } from "../../utils/dateUtils"

const CLUSTERGAZER_SERVICE_URL = import.meta.env.VITE_CLUSTERGAZER_SERVICE || "http://localhost:8001"

export const clusterApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get clusters for workspace - THIS IS THE KEY API CALL
    getClusters: builder.query({
      query: (workspaceId) => ({
        url: "/clusters/", // GET http://localhost:8001/clusters/
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      providesTags: (result, error, workspaceId) => [
        { type: "Cluster", id: "LIST" },
        { type: "Cluster", id: workspaceId },
        ...(result || []).map(({ _id }) => ({ type: "Cluster", id: _id })),
      ],
      transformResponse: (response) => {
        console.log("🔄 Fresh API Response from /clusters/:", response)

        // Filter only active, non-deleted clusters (deleted: false)
        const activeClusters = response.filter((cluster) => cluster.is_active === true && cluster.deleted === false)

        console.log("✅ Filtered Active Clusters (deleted: false):", activeClusters)

        // Sort clusters by _id in descending order (newest first)
        return activeClusters.sort((a, b) => {
          if (a._id > b._id) return -1
          if (a._id < b._id) return 1
          return 0
        })
      },
      keepUnusedDataFor: 300, // Keep cached for 5 minutes
      refetchOnMountOrArgChange: true, // Only refetch when workspaceId actually changes
      refetchOnFocus: false, // Don't refetch on window focus
      refetchOnReconnect: true, // Refetch on network reconnect
      // 🔑 KEY FIX: Update Redux with workspace-specific data
      async onQueryStarted(workspaceId, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          console.log("🔄 Updating Redux with fresh clusters data for workspace:", workspaceId, data)
          dispatch(setWorkspaceClusters({ workspaceId, clusters: data })) // ✅ Workspace-specific Redux update
        } catch (error) {
          console.error("❌ Error fetching clusters:", error)
        }
      },
    }),

    // Create cluster
    createCluster: builder.mutation({
      query: ({ clusterData, workspaceId }) => ({
        url: "/clusters/",
        method: "POST",
        body: { ...clusterData, workspace_id: workspaceId },
        headers: {
          "Content-Type": "application/json",
        },
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      invalidatesTags: (result, error, { workspaceId }) => [
        { type: "Cluster", id: "LIST" },
        { type: "Cluster", id: workspaceId },
      ],
      async onQueryStarted({ workspaceId, clusterData }, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          // Only add if it's active and not deleted
          if (data.is_active && !data.deleted) {
            dispatch(addWorkspaceCluster({ workspaceId, cluster: data })) // ✅ Workspace-specific add
          }
        } catch (error) {
          console.error("Error creating cluster:", error)
        }
      },
    }),

    // Get cluster performance data with caching
    getClusterPerformance: builder.query({
      query: ({ clusterId, startDate, endDate }) => {
        // compute comparison window
        const { compare_start_date, compare_end_date } = getComparisonDates(startDate, endDate)
        console.log("getClusterPerformance params:", {
          clusterId,
          startDate,
          endDate,
          compare_start_date,
          compare_end_date,
        })

        return {
          url: `/clusters/${clusterId}/metrics/total`,
          params: {
            start_date: startDate,
            end_date: endDate,
            compare_start_date,
            compare_end_date,
            filter_type: "compare",
          },
          baseUrl: CLUSTERGAZER_SERVICE_URL,
        }
      },
      providesTags: (result, error, { clusterId }) => [
        { type: "Performance", id: clusterId },
      ],
      keepUnusedDataFor: 300, // cache for 5 minutes
      transformResponse: (response) => {
        console.log("Cluster‑metrics response:", response)
        return response
      },
    }),

    // Delete cluster (soft delete)
    deleteCluster: builder.mutation({
      query: ({ clusterId, workspaceId }) => ({
        url: `/clusters/${clusterId}`,
        method: "DELETE",
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      invalidatesTags: (result, error, { workspaceId }) => [
        { type: "Cluster", id: "LIST" },
        { type: "Cluster", id: workspaceId },
        { type: "DeletedCluster", id: "LIST" },
      ],
      async onQueryStarted({ clusterId, workspaceId }, { dispatch, queryFulfilled, getState }) {
        try {
          await queryFulfilled

          // Get the cluster data before removing it
          const state = getState()
          const workspaceClusters = state.cluster.clustersByWorkspace[workspaceId] || []
          const clusterToDelete = workspaceClusters.find((c) => c._id === clusterId)

          // Remove from workspace clusters immediately
          dispatch(removeWorkspaceCluster({ workspaceId, clusterId })) // ✅ Workspace-specific remove

          // Add to trash with proper formatting
          if (clusterToDelete) {
            const deletedAtDate = new Date()
            const permanentDeletionDate = new Date(deletedAtDate)
            permanentDeletionDate.setDate(permanentDeletionDate.getDate() + 30)

            const deletedCluster = {
              id: clusterToDelete._id,
              name: clusterToDelete.clusterName,
              deviceFilter: clusterToDelete.deviceFilter,
              countryFilter: clusterToDelete.countryFilter,
              dateDeleted: deletedAtDate.toLocaleDateString(),
              permanentDeletionDate: permanentDeletionDate.toLocaleDateString(),
              daysLeft: 30,
            }
            dispatch(clusterMovedToTrash(deletedCluster))
          }
        } catch (error) {
          console.error("Error deleting cluster:", error)
        }
      },
    }),
  }),
})

export const {
  useGetClustersQuery,
  useCreateClusterMutation,
  useDeleteClusterMutation,
  useGetClusterPerformanceQuery,
} = clusterApi
