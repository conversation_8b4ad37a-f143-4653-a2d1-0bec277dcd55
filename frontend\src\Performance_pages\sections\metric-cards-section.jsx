import ClicksCard from "../../components/cards/clicks-card"
import ImpressionsCard from "../../components/cards/impressions-card"
import CTRCard from "../../components/cards/ctr-card"
import PositionCard from "../../components/cards/position-card"

const MetricCardsSection = ({ totalMetrics, activeMetrics, dateRangeText, onMetricToggle }) => {
  const selectedMetricsCount = Object.values(activeMetrics).filter(Boolean).length

  return (
    <div className="flex flex-wrap gap-4 mb-8">
      <div className={`${totalMetrics.comparison ? "w-[240px] h-[200px]" : "w-[200px] h-[160px]"}`}>
        <ClicksCard
          clicks={totalMetrics.clicks}
          comparisonClicks={totalMetrics.comparison?.clicks}
          onToggle={(isChecked) => onMetricToggle("clicks", isChecked)}
          isChecked={activeMetrics.clicks}
          title="Total Clicks"
          disabled={activeMetrics.clicks && selectedMetricsCount === 1}
          dateRange={dateRangeText}
        />
      </div>
      <div className={`${totalMetrics.comparison ? "w-[240px] h-[200px]" : "w-[200px] h-[160px]"}`}>
        <ImpressionsCard
          impressions={totalMetrics.impressions}
          comparisonImpressions={totalMetrics.comparison?.impressions}
          onToggle={(isChecked) => onMetricToggle("impressions", isChecked)}
          isChecked={activeMetrics.impressions}
          title="Total Impressions"
          disabled={activeMetrics.impressions && selectedMetricsCount === 1}
          dateRange={dateRangeText}
        />
      </div>
      <div className={`${totalMetrics.comparison ? "w-[240px] h-[200px]" : "w-[200px] h-[160px]"}`}>
        <CTRCard
          ctr={totalMetrics.ctr}
          comparisonCtr={totalMetrics.comparison?.ctr}
          onToggle={(isChecked) => onMetricToggle("ctr", isChecked)}
          isChecked={activeMetrics.ctr}
          title="Average CTR"
          disabled={activeMetrics.ctr && selectedMetricsCount === 1}
          dateRange={dateRangeText}
        />
      </div>
      <div className={`${totalMetrics.comparison ? "w-[240px] h-[200px]" : "w-[200px] h-[160px]"}`}>
        <PositionCard
          position={totalMetrics.position}
          comparisonPosition={totalMetrics.comparison?.position}
          onToggle={(isChecked) => onMetricToggle("position", isChecked)}
          isChecked={activeMetrics.position}
          title="Average Position"
          disabled={activeMetrics.position && selectedMetricsCount === 1}
          dateRange={dateRangeText}
        />
      </div>
    </div>
  )
}

export default MetricCardsSection
