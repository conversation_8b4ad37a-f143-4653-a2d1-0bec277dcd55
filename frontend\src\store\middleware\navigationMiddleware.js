import { 
  setCurrentPage, 
  handleWorkspaceSwitch,
  resetNavigationState,
  NAVIGATION_PAGES,
  getPageFromPath 
} from "../slices/navigationSlice"
import { switchWorkspace } from "../slices/workspaceSlice"
import { logout } from "../slices/authSlice"

/**
 * Navigation middleware to coordinate navigation state with workspace management
 * This middleware intercepts relevant actions and updates navigation state accordingly
 */
export const navigationMiddleware = (store) => (next) => (action) => {
  const result = next(action)
  const state = store.getState()
  
  // Handle workspace switch actions
  if (action.type === switchWorkspace.fulfilled.type) {
    const { workspaceId } = action.payload
    const currentPath = window.location.pathname
    
    // Update navigation state for workspace switch
    store.dispatch(handleWorkspaceSwitch({ 
      newWorkspaceId: workspaceId, 
      currentPath 
    }))
    
    console.log("🔄 Navigation middleware: Workspace switched, navigation state updated")
  }
  
  // Handle logout actions
  if (action.type === logout.type) {
    // Reset navigation state on logout
    store.dispatch(resetNavigationState())
    console.log("🔄 Navigation middleware: User logged out, navigation state reset")
  }
  
  // Handle navigation actions to update workspace state if needed
  if (action.type === setCurrentPage.type) {
    const { page, workspaceId, navigationType } = action.payload
    const currentWorkspace = state.workspace.currentWorkspace
    
    // Log navigation for debugging
    console.log("🔄 Navigation middleware: Page changed", {
      page,
      workspaceId,
      navigationType,
      currentWorkspace: currentWorkspace?.id
    })
    
    // If navigating within workspace, ensure workspace state is consistent
    if (navigationType === "intra-workspace" && currentWorkspace?.id === workspaceId) {
      // Navigation state is already updated by the action
      // This is just for logging and potential future enhancements
      console.log("✅ Navigation middleware: Intra-workspace navigation completed")
    }
  }
  
  return result
}

/**
 * Enhanced navigation middleware with URL synchronization
 * This version also handles browser navigation events
 */
export const enhancedNavigationMiddleware = (store) => (next) => (action) => {
  const result = next(action)
  
  // Handle browser back/forward navigation
  if (typeof window !== "undefined") {
    const handlePopState = (event) => {
      const currentPath = window.location.pathname
      const page = getPageFromPath(currentPath)
      const state = store.getState()
      const currentWorkspace = state.workspace.currentWorkspace
      
      if (currentWorkspace && currentPath.includes(`/workspace/${currentWorkspace.id}`)) {
        // Update navigation state for browser navigation
        store.dispatch(setCurrentPage({
          page,
          workspaceId: currentWorkspace.id,
          navigationType: "browser"
        }))
        
        console.log("🔄 Navigation middleware: Browser navigation detected", {
          page,
          path: currentPath,
          workspaceId: currentWorkspace.id
        })
      }
    }
    
    // Only add listener once
    if (!window.__navigationListenerAdded) {
      window.addEventListener("popstate", handlePopState)
      window.__navigationListenerAdded = true
    }
  }
  
  return result
}

/**
 * Navigation performance middleware
 * Tracks navigation performance and logs metrics
 */
export const navigationPerformanceMiddleware = (store) => (next) => (action) => {
  // Track navigation start time
  if (action.type === setCurrentPage.type) {
    const startTime = performance.now()
    action.meta = { ...action.meta, startTime }
  }
  
  const result = next(action)
  
  // Log navigation completion time
  if (action.type === setCurrentPage.type && action.meta?.startTime) {
    const endTime = performance.now()
    const duration = endTime - action.meta.startTime
    
    console.log("⚡ Navigation Performance:", {
      page: action.payload.page,
      navigationType: action.payload.navigationType,
      duration: `${duration.toFixed(2)}ms`
    })
    
    // Log slow navigations
    if (duration > 100) {
      console.warn("🐌 Slow navigation detected:", {
        page: action.payload.page,
        duration: `${duration.toFixed(2)}ms`
      })
    }
  }
  
  return result
}

export default navigationMiddleware
