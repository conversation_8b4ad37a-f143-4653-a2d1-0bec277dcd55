import { useEffect, useState } from "react"
import {
  Search,
  FileText,
  LineChartIcon as ChartLine,
  Eye,
  Trash2,
  AlertCircle,
  Clock,
  CheckCircle,
  Loader,
} from "lucide-react"
import { useNavigate, useParams } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useWorkspace } from "../hooks/useWorkspace"
import { useClusters } from "../hooks/useClusters"
import {
  setSearchQuery,
  openCreateModal,
  closeCreateModal,
  openDeleteModal,
  closeDeleteModal,
} from "../store/slices/clusterSlice"
import Navbar from "../components/navbar"
import Sidebar from "../components/sidebar"
import CreateClusterModal from "../components/create-cluster-modal"
import DeleteConfirmationModal from "../components/delete-confirmation-modal"
import PerformanceSparkline from "../components/PerformanceSparkline"
import { workspaceApi } from "../store/api/workspaceApi"

// Add country mapping at the top
const COUNTRY_CODES = {
  usa: "United States",
  ind: "India",
  gbr: "United Kingdom",
  can: "Canada",
  aus: "Australia",
  // Add more mappings as needed
}

const getCountryName = (code) => {
  return COUNTRY_CODES[code.toLowerCase()] || code.toUpperCase()
}

const ClustersPage = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { workspaceId: urlWorkspaceId } = useParams()
  const { currentWorkspace, isSwitching, getWorkspaceById } = useWorkspace()

  // 🔑 KEY FIX: Always use currentWorkspace from Redux, but validate URL sync
  const activeWorkspaceId = currentWorkspace

  // 🔑 NEW: Get domain info directly from workspace data
  const currentWorkspaceObj = getWorkspaceById(activeWorkspaceId)
  const domainInfo = currentWorkspaceObj?.domain_info?.siteUrl || "Loading..."

  // 🔑 NEW: Track data loading
  const [isInitialLoad, setIsInitialLoad] = useState(true)

  useEffect(() => {
    if (isInitialLoad && currentWorkspaceObj?.domain_info) {
      setIsInitialLoad(false)
    }
  }, [currentWorkspaceObj, isInitialLoad])

  // Note: Workspace data refetching is handled by useWorkspace hook
  // No need for manual refetch here as it causes duplicate API calls

  // 🔧 FIX: Prevent URL sync during workspace switching to allow onboarding check
  useEffect(() => {
    // Don't interfere with workspace switching - let onboarding check handle navigation
    if (activeWorkspaceId && urlWorkspaceId && urlWorkspaceId !== activeWorkspaceId && !isSwitching) {
      console.log("🔄 URL out of sync with Redux state, but checking if onboarding needed first...")

      // Instead of directly navigating to clusters, navigate to onboarding check route
      // This ensures proper onboarding validation
      navigate(`/workspace/${activeWorkspaceId}`, { replace: true })
    }
  }, [activeWorkspaceId, urlWorkspaceId, navigate, isSwitching])

  // Redux state
  const { searchQuery, ui } = useSelector((state) => state.cluster)
  const { isCreateModalOpen, isDeleteModalOpen, clusterToDelete } = ui

  // Custom hook for clusters data and operations
  const {
    clusters,
    onboardingData,
    isLoading: clustersLoading,
    isCreating,
    isDeleting,
    error,
    handleCreateCluster,
    handleDeleteCluster,
    totalClusters,
    filteredCount,
    processingCount,
    isClusterProcessing,
    refetchClusters,
  } = useClusters(activeWorkspaceId)

  // 🔑 NEW: Enhanced loading state
  const isLoading = clustersLoading || isSwitching || isInitialLoad || !currentWorkspaceObj?.domain_info

  console.log("ClustersPage - URL workspace:", urlWorkspaceId)
  console.log("ClustersPage - Current workspace:", currentWorkspace)
  console.log("ClustersPage - Active workspace:", activeWorkspaceId)
  console.log("ClustersPage - Is switching workspace:", isSwitching)
  console.log("ClustersPage - Current workspace obj:", currentWorkspaceObj)
  console.log("ClustersPage - Domain info:", domainInfo)

  // 🔑 KEY FIX: Redirect if no workspace is set
  useEffect(() => {
    if (!activeWorkspaceId && !isSwitching) {
      console.log("No active workspace, redirecting to login")
      navigate("/login")
      return
    }
  }, [activeWorkspaceId, navigate, isSwitching])

  // Let onboarding-check component handle onboarding redirects
  // Removed redundant onboarding redirect logic to prevent duplicate navigation

  const handleSearchChange = (e) => {
    dispatch(setSearchQuery(e.target.value))
  }

  const handleAddCluster = () => {
    dispatch(openCreateModal())
  }

  const handleCloseModal = () => {
    dispatch(closeCreateModal())
  }

  const handleSubmitCluster = async (clusterData) => {
    const success = await handleCreateCluster(clusterData)
    return success
  }

  const handleDeleteClick = (cluster) => {
    dispatch(openDeleteModal(cluster))
  }

  const handleDeleteConfirm = async () => {
    if (!clusterToDelete) return
    const success = await handleDeleteCluster(clusterToDelete._id)
    if (success) {
      dispatch(closeDeleteModal())
    }
  }

  // Navigate to cluster links page
  const handleNavigateToLinks = (cluster) => {
    navigate(`/workspace/${activeWorkspaceId}/clusters/${cluster._id}/links`, {
      state: { clusterName: cluster.clusterName },
    })
  }

  const renderStatusIcon = (cluster) => {
    const isProcessing = isClusterProcessing(cluster._id)

    if (isProcessing || cluster.gsc_status === "processing" || cluster.gsc_status === "pending") {
      return (
        <div className="text-amber-600" title="Processing GSC data...">
          <Loader className="w-5 h-5 animate-spin" />
        </div>
      )
    }

    if (cluster.gsc_status === "completed") {
      return (
        <div className="text-green-600" title="GSC data ready">
          <CheckCircle className="w-5 h-5" />
        </div>
      )
    }

    if (cluster.gsc_status === "error") {
      return (
        <div className="text-red-600" title="GSC processing error">
          <AlertCircle className="w-5 h-5" />
        </div>
      )
    }

    return (
      <div className="text-gray-500" title="Pending GSC processing">
        <Clock className="w-5 h-5" />
      </div>
    )
  }

  const renderPerformance = (cluster) => {
    const isProcessing = isClusterProcessing(cluster._id)

    if (isProcessing || cluster.gsc_status === "processing" || cluster.gsc_status === "pending") {
      return (
        <div className="flex flex-col items-center justify-center h-full min-h-[60px] text-amber-600">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-600 mb-1"></div>
          <span className="text-xs">Processing GSC Data...</span>
        </div>
      )
    }

    if (cluster.gsc_status === "error") {
      return (
        <div className="flex flex-col items-center justify-center h-full min-h-[60px] text-red-500">
          <AlertCircle className="w-6 h-6 mb-1" />
          <span className="text-xs">GSC Error</span>
        </div>
      )
    }

    if (cluster.gsc_status === "completed") {
      return (
        <div className="flex items-center justify-center h-full min-h-[60px] relative">
          <PerformanceSparkline clusterId={cluster._id} />
        </div>
      )
    }

    // Default case for pending
    return (
      <div className="flex flex-col items-center justify-center h-full min-h-[60px] text-gray-500">
        <Clock className="w-6 h-6 mb-1" />
        <span className="text-xs">Waiting for GSC...</span>
      </div>
    )
  }

  // Show loading while checking onboarding or fetching clusters
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090]"></div>
            <p className="mt-4 text-gray-600">
              {isSwitching 
                ? "Switching workspace..." 
                : !currentWorkspaceObj?.domain_info
                ? "Loading workspace data..."
                : "Loading clusters..."}
            </p>
            {(isSwitching || !currentWorkspaceObj?.domain_info) && (
              <p className="mt-2 text-sm text-gray-500">
                This may take a few seconds to update...
              </p>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Show error if there's an issue
  if (error) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="p-4 bg-red-50 rounded-lg max-w-md">
              <div className="flex items-center gap-2 text-red-600 mb-2">
                <AlertCircle className="w-5 h-5" />
                <h3 className="font-medium">Error</h3>
              </div>
              <p className="text-red-600 mb-4">{error.data?.detail || error.message || "Failed to load clusters"}</p>
              <button
                onClick={() => window.location.reload()}
                className="text-[#352090] hover:text-[#2a1a70] font-medium"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />

      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar activePage="overview" />
        </div>

        <main className="flex-1 p-6 overflow-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h1 className="text-xl font-semibold text-gray-800 mb-2">
                {domainInfo}
              </h1>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span>
                  ({filteredCount} of {totalClusters} clusters)
                </span>
                {processingCount > 0 && (
                  <span className="flex items-center gap-1 text-amber-600">
                    <Clock className="w-4 h-4 animate-pulse" />
                    {processingCount} processing
                  </span>
                )}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3 mt-4 md:mt-0">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search clusters..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-[8px] w-full sm:w-[250px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>

              <button
                onClick={handleAddCluster}
                disabled={isCreating}
                className="bg-[#352090] text-white px-4 py-2 rounded-[8px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isCreating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Creating...
                  </>
                ) : (
                  "Add Cluster"
                )}
              </button>
            </div>
          </div>

          <div className="bg-white rounded-md border border-gray-200 overflow-x-auto">
            {/* Updated header - removed Status column */}
            <div className="bg-[#DCDCDC] min-w-full grid grid-cols-5 border-b border-gray-200">
              <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-[#DCDCDC]">Cluster Name</div>
              <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Device Filter</div>
              <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Country Filter</div>
              <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Performance</div>
              <div className="p-4 font-['Noto Sans'] text-[#352090]">Actions</div>
            </div>

            {clusters.length === 0 && (
              <div className="flex flex-col items-center justify-center py-16">
                <div className="w-12 h-12 rounded-full bg-[#F8F7FC] flex items-center justify-center mb-4">
                  <FileText className="w-6 h-6 text-[#AD66FF]" />
                </div>
                <h3 className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                  {searchQuery ? "No matching clusters found" : "Add Clusters"}
                </h3>
                <p className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                  {searchQuery ? "Try adjusting your search query" : "No clusters created yet"}
                </p>
                {!searchQuery && (
                  <button
                    onClick={handleAddCluster}
                    className="mt-4 bg-[#352090] text-white px-4 py-2 rounded-[8px] hover:bg-[#2a1a70] transition-colors"
                  >
                    Create Your First Cluster
                  </button>
                )}
              </div>
            )}

            {clusters.map((cluster) => (
              <div
                key={cluster._id}
                className="min-w-full grid grid-cols-5 border-b border-gray-200 relative hover:bg-gray-50 transition-colors"
                style={{ position: "relative", zIndex: 1 }}
              >
                {/* Clickable Cluster Name */}
                <div className="p-4 text-gray-800 border-r border-gray-200">
                  <button
                    onClick={() => handleNavigateToLinks(cluster)}
                    className="text-left w-full hover:text-[#352090] transition-colors cursor-pointer"
                    title="Click to view cluster links"
                  >
                    <span className="truncate block font-medium hover:underline" title={cluster.clusterName}>
                      {cluster.clusterName}
                    </span>
                  </button>
                </div>
                <div className="p-4 text-gray-800 border-r border-gray-200">{cluster.deviceFilter}</div>
                <div className="p-4 text-gray-800 border-r border-gray-200">
                  <div className="flex flex-wrap gap-1">
                    {cluster.countryFilter === "ALL" ? (
                      <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">
                        All Countries
                      </span>
                    ) : (
                      cluster.countryFilter
                        .split(",")
                        .slice(0, 3)
                        .map((countryCode, index) => (
                          <span key={index} className="inline-block px-2 py-1 bg-gray-100 rounded-md text-sm">
                            {getCountryName(countryCode.trim())}
                          </span>
                        ))
                    )}
                    {cluster.countryFilter !== "ALL" && cluster.countryFilter.split(",").length > 3 && (
                      <span className="inline-block px-2 py-1 bg-gray-100 rounded-md text-sm">
                        +{cluster.countryFilter.split(",").length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                {/* Performance column with proper overflow handling */}
                <div className="text-gray-800 border-r border-gray-200 flex items-center justify-center min-h-[80px] relative overflow-visible">
                  <div className="relative z-10">{renderPerformance(cluster)}</div>
                </div>
                {/* Updated actions column with status icon */}
                <div className="p-4 flex items-center space-x-4">
                  {/* Status Icon */}
                  <div className="flex items-center justify-center">{renderStatusIcon(cluster)}</div>

                  <button
                    className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
                    title="View Performance"
                    onClick={() => navigate(`/cluster_performance/${cluster._id}`)}
                  >
                    <ChartLine className="w-5 h-5" />
                  </button>
                  <button
                    className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
                    title="View Links"
                    onClick={() => handleNavigateToLinks(cluster)}
                  >
                    <Eye className="w-5 h-5" />
                  </button>
                  <button
                    className="text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="Delete"
                    onClick={() => handleDeleteClick(cluster)}
                    disabled={isDeleting}
                  >
                    {isDeleting && clusterToDelete?._id === cluster._id ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
                    ) : (
                      <Trash2 className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </main>
      </div>

      <CreateClusterModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmitCluster}
        domainInfo={currentWorkspaceObj?.domain_info}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => dispatch(closeDeleteModal())}
        onConfirm={handleDeleteConfirm}
        title="Delete Cluster"
        message="This cluster will be moved to trash. You have 30 days to recover your data."
        isDeleting={isDeleting}
      />
    </div>
  )
}

export default ClustersPage
