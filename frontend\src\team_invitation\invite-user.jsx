import { useState, useRef, useEffect } from "react"
import { Trash2 } from "lucide-react"
import { useDispatch } from "react-redux"
import { useWorkspace } from "../hooks/useWorkspace"
import {
  useGetInvitedUsersQuery,
  useSendTeamInvitationMutation,
  useRemoveUserFromWorkspaceMutation,
} from "../store/api/teamApi"
import { useGetUserProfileQuery } from "../store/api/authApi"
import { addNotification } from "../store/slices/uiSlice"
import DeleteConfirmationModal from "../components/delete-confirmation-modal"

// Spinner used for loading states in the table and on initial fetch
const Spinner = () => (
  <div className="flex items-center justify-center py-12">
    <svg
      className="animate-spin h-8 w-8 text-[#352090]"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
    </svg>
  </div>
)

const InviteUserModal = ({ isOpen, onClose, workspaceId: propWorkspaceId }) => {
  const dispatch = useDispatch()
  const { currentWorkspace } = useWorkspace()

  // Use workspace from props or current workspace
  const activeWorkspaceId = propWorkspaceId || currentWorkspace

  const [email, setEmail] = useState("")
  const [error, setError] = useState("")
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState(null)

  const modalRef = useRef(null)
  const inputRef = useRef(null)

  // Redux queries and mutations
  const { data: userProfile } = useGetUserProfileQuery()
  const {
    data: invitedUsersData,
    isLoading: isLoadingUsers,
    refetch: refetchInvitedUsers,
  } = useGetInvitedUsersQuery(activeWorkspaceId, {
    skip: !isOpen || !activeWorkspaceId,
  })

  const [sendTeamInvitation, { isLoading: isInviting }] = useSendTeamInvitationMutation()
  const [removeUserFromWorkspace, { isLoading: isDeleting }] = useRemoveUserFromWorkspaceMutation()

  // Extract data
  const invitedUsers = invitedUsersData?.active_members || []
  const currentUserEmail = userProfile?.email || ""

  // Reset states when main modal closes
  useEffect(() => {
    if (!isOpen) {
      setError("")
      setDeleteModalOpen(false)
      setUserToDelete(null)
      setEmail("")
    }
  }, [isOpen])

  // Focus the input on open
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  // Close on outside click - Modified to prevent closing when delete modal is open
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target) && !deleteModalOpen) {
        onClose()
      }
    }
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose, deleteModalOpen])

  const handleInvite = async () => {
    if (!email.trim()) return
    if (!activeWorkspaceId) {
      setError("No workspace selected")
      return
    }

    setError("")

    const emails = email.split(/\s|,+/).filter(Boolean)
    const invalid = emails.find((e) => !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e))
    if (invalid) {
      setError(`"${invalid}" doesn't look like a valid email.`)
      return
    }

    try {
      console.log("🚀 Sending invitations:", { workspaceId: activeWorkspaceId, emails })

      const result = await sendTeamInvitation({
        workspaceId: activeWorkspaceId,
        email: emails.length === 1 ? emails[0] : emails,
        role: "Admin",
      }).unwrap()

      console.log("✅ Invitation result:", result)

      if (result.status === "success" && result.results) {
        const successCount = result.results.filter((r) => r.status === "success").length
        const errorCount = result.results.filter((r) => r.status === "error").length

        if (successCount > 0) {
          dispatch(
            addNotification({
              type: "success",
              title: "Invitations Sent!",
              message: `Successfully sent ${successCount} invitation(s)`,
            }),
          )
        }

        if (errorCount > 0) {
          const errorMessages = result.results
            .filter((r) => r.status === "error")
            .map((r) => `${r.email}: ${r.message}`)
            .join(", ")

          dispatch(
            addNotification({
              type: "warning",
              title: "Some Invitations Failed",
              message: errorMessages,
              persistent: true,
            }),
          )
        }

        // Clear email and refetch users
        setEmail("")
        refetchInvitedUsers()
      }
    } catch (error) {
      console.error("❌ Failed to send invitations:", error)
      setError(error.data?.detail || error.message || "We ran into an issue inviting those emails. Please try again.")

      dispatch(
        addNotification({
          type: "error",
          title: "Invitation Failed",
          message: error.data?.detail || error.message || "Failed to send invitations. Please try again.",
        }),
      )
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault()
      handleInvite()
    }
  }

  const handleDeleteClick = (e, user) => {
    console.log("🗑️ Delete click - Current user:", currentUserEmail, "User to delete:", user.email)
    // Prevent deletion if it's the current user
    if (user.email === currentUserEmail) return

    e.stopPropagation()
    setUserToDelete(user)
    setDeleteModalOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!userToDelete || !activeWorkspaceId) return

    setError("")
    try {
      console.log("🚀 Removing user:", { workspaceId: activeWorkspaceId, email: userToDelete.email })

      await removeUserFromWorkspace({
        workspaceId: activeWorkspaceId,
        email: userToDelete.email,
      }).unwrap()

      console.log("✅ User removed successfully")

      dispatch(
        addNotification({
          type: "success",
          title: "User Removed",
          message: `${userToDelete.email} has been removed from the workspace`,
        }),
      )

      // Refetch users and close modal
      refetchInvitedUsers()
      setDeleteModalOpen(false)
      setUserToDelete(null)
    } catch (error) {
      console.error("❌ Delete error:", error)
      setError(
        error.data?.detail || error.message || "Couldn't remove that user right now. Please try again in a moment.",
      )

      dispatch(
        addNotification({
          type: "error",
          title: "Remove Failed",
          message: error.data?.detail || error.message || "Failed to remove user. Please try again.",
        }),
      )
    }
  }

  const handleDeleteCancel = (e) => {
    e?.stopPropagation()
    setDeleteModalOpen(false)
    setUserToDelete(null)
    setError("")
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-[8px] shadow-lg w-full max-w-3xl mx-4 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Invite Team Member</h2>
          {activeWorkspaceId && (
            <p className="text-sm text-gray-500 mt-1">
              Workspace:{" "}
              {activeWorkspaceId.length > 12
                ? `${activeWorkspaceId.substring(0, 8)}...${activeWorkspaceId.substring(activeWorkspaceId.length - 4)}`
                : activeWorkspaceId}
            </p>
          )}
        </div>

        {/* Invite form */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex gap-3">
            <input
              ref={inputRef}
              type="text"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Add emails (comma or space separated)"
              className="flex-grow px-4 py-2 border border-gray-300 rounded-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
              disabled={isInviting || isDeleting}
            />
            <button
              onClick={handleInvite}
              disabled={isInviting || isDeleting || !activeWorkspaceId}
              className="px-6 py-2 bg-[#352090] text-white rounded-[8px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 whitespace-nowrap flex items-center justify-center min-w-[100px]"
            >
              {isInviting ? (
                <>
                  <svg
                    className="animate-spin h-4 w-4 mr-2 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z" />
                  </svg>
                  Inviting...
                </>
              ) : (
                "Invite"
              )}
            </button>
          </div>
        </div>

        {/* Table section */}
        <div className="p-6">
          <h3 className="text-md font-medium text-[#352090] mb-4">Invited Team Members</h3>

          {error && <div className="mb-4 p-3 bg-red-50 rounded-[8px] text-red-600 text-sm">{error}</div>}

          {!activeWorkspaceId ? (
            <div className="text-center py-8 text-gray-500">No workspace selected</div>
          ) : isLoadingUsers ? (
            <Spinner />
          ) : (
            <div className="overflow-x-auto rounded-[8px] border border-gray-200">
              <table className="min-w-full border-collapse">
                <thead>
                  <tr className="bg-[#DCDCDC]">
                    {["Name", "Email", "Status", "Action"].map((h) => (
                      <th
                        key={h}
                        className="py-3 px-4 text-left text-sm font-medium text-[#352090] border-b border-gray-200"
                      >
                        {h}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {invitedUsers.length > 0 ? (
                    invitedUsers.map((user) => (
                      <tr key={user.email} className="border-b border-gray-200 hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm text-gray-800">{user.full_name}</td>
                        <td className="py-3 px-4 text-sm text-gray-800">{user.email}</td>
                        <td className="py-3 px-4 text-sm text-gray-800 capitalize">{user.status}</td>
                        <td className="py-3 px-4 text-sm text-gray-800">
                          <button
                            onClick={(e) => handleDeleteClick(e, user)}
                            disabled={isDeleting || user.email === currentUserEmail}
                            className={`text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-100 ${
                              user.email === currentUserEmail ? "opacity-50 cursor-not-allowed" : ""
                            }`}
                            title={user.email === currentUserEmail ? "Cannot remove yourself" : "Remove user"}
                          >
                            {isDeleting && userToDelete?.email === user.email ? (
                              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
                            ) : (
                              <Trash2 className="w-5 h-5" />
                            )}
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="4" className="py-8 text-center text-sm text-gray-500">
                        No team members invited yet.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 flex justify-end gap-3 border-t border-gray-200 rounded-b-[8px]">
          <button
            onClick={onClose}
            disabled={isInviting || isDeleting}
            className="px-6 py-2 border border-gray-300 rounded-[8px] hover:bg-gray-50 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={onClose}
            disabled={isInviting || isDeleting}
            className="px-6 py-2 bg-[#352090] text-white rounded-[8px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50"
          >
            Done
          </button>
        </div>
      </div>

      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Remove User"
        message="This user will be permanently removed from the workspace and will no longer have access to this workspace."
        isDeleting={isDeleting}
      />
    </div>
  )
}

export default InviteUserModal
