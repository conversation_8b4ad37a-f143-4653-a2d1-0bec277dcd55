@import url("https://fonts.googleapis.com/css2?family=Noto+Sans:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body,
html {
  font-family: "Noto Sans";
  height: 100%;
  width: 100%;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(53, 32, 144, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(53, 32, 144, 0.5);
}

/* Responsive viewport height fix for mobile browsers */
@media screen and (max-width: 768px) {
  .min-h-screen {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }
}

/* Ensure form fits on smaller screens */
@media screen and (max-height: 700px) {
  .mb-6 {
    margin-bottom: 1rem !important;
  }

  .mb-5 {
    margin-bottom: 0.75rem !important;
  }

  .mb-4 {
    margin-bottom: 0.5rem !important;
  }

  .mb-2 {
    margin-bottom: 0.25rem !important;
  }

  .custom-h-48 {
    height: 40px !important;
  }

  .custom-h-52 {
    height: 44px !important;
  }
}

/* Adjust for very small screens */
@media screen and (max-height: 600px) {
  .mb-6 {
    margin-bottom: 0.75rem !important;
  }

  .mb-5 {
    margin-bottom: 0.5rem !important;
  }

  .mb-4 {
    margin-bottom: 0.375rem !important;
  }

  .mb-2 {
    margin-bottom: 0.125rem !important;
  }

  .custom-h-48 {
    height: 36px !important;
  }

  .custom-h-52 {
    height: 40px !important;
  }

  .custom-text-30 {
    font-size: 24px !important;
  }

  .custom-text-16 {
    font-size: 14px !important;
  }

  .custom-text-14 {
    font-size: 12px !important;
  }
}

/* Tailwind config extension */
@layer utilities {
  .custom-rounded-xl {
    border-radius: 12px;
  }
}

:root {
  --cursor-x: 0px;
  --cursor-y: 0px;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #E5E7EB transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #E5E7EB;
  border-radius: 20px;
  border: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #D1D5DB;
}
