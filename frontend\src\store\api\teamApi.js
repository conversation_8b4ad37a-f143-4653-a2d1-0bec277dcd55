import { apiSlice } from "./apiSlice"

export const teamApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    sendTeamInvitation: builder.mutation({
      query: ({ workspaceId, email, role }) => ({
        url: `http://localhost:8002/invite/${workspaceId}`,
        method: "POST",
        body: { email, role },
      }),
      invalidatesTags: ["Team"],
    }),

    getInvitedUsers: builder.query({
      query: (workspaceId) => ({
        url: `http://localhost:8002/invite/workspaces/${workspaceId}/invited_members`,
        method: "GET",
      }),
      providesTags: ["Team"],
    }),

    removeUserFromWorkspace: builder.mutation({
      query: ({ workspaceId, email }) => ({
        url: `http://localhost:8002/invite/workspaces/${workspaceId}/remove_user`,
        method: "DELETE",
        params: { email },
      }),
      invalidatesTags: ["Team"],
    }),

    getWorkspaceMembersBatch: builder.query({
      query: (data) => ({
        url: `http://localhost:8002/invite/workspaces/batch/members`,
        method: 'POST',
        body: data.workspace_ids
      }),
    }),
  }),
})

export const {
  useSendTeamInvitationMutation,
  useGetInvitedUsersQuery,
  useRemoveUserFromWorkspaceMutation,
  useGetWorkspaceMembersBatchQuery,
} = teamApi
