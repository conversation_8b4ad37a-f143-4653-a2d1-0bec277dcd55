import { apiSlice } from "./apiSlice"
const VITE_TEAM_SERVICE_URL = import.meta.env.VITE_TEAM_SERVICE || "http://localhost:8002"

export const teamApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    sendTeamInvitation: builder.mutation({
      query: ({ workspaceId, email, role }) => ({
        url: `${VITE_TEAM_SERVICE_URL}/invite/${workspaceId}`,
        method: "POST",
        body: { email, role },
      }),
      invalidatesTags: ["Team"],
    }),

    getInvitedUsers: builder.query({
      query: (workspaceId) => ({
        url: `${VITE_TEAM_SERVICE_URL}/invite/workspaces/${workspaceId}/invited_members`,
        method: "GET",
      }),
      providesTags: ["Team"],
    }),

    removeUserFromWorkspace: builder.mutation({
      query: ({ workspaceId, email }) => ({
        url: `${VITE_TEAM_SERVICE_URL}/invite/workspaces/${workspaceId}/remove_user`,
        method: "DELETE",
        params: { email },
      }),
      invalidatesTags: ["Team"],
    }),

    getWorkspaceMembersBatch: builder.query({
      query: (data) => ({
        url: `${VITE_TEAM_SERVICE_URL}/invite/workspaces/batch/members`,
        method: 'POST',
        body: data.workspace_ids
      }),
    }),
  }),
})

export const {
  useSendTeamInvitationMutation,
  useGetInvitedUsersQuery,
  useRemoveUserFromWorkspaceMutation,
  useGetWorkspaceMembersBatchQuery,
} = teamApi
