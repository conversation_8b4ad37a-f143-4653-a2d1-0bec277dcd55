import axios from 'axios';

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE;
const TEAM_SERVICE_URL = import.meta.env.VITE_TEAM_SERVICE;
const CLUSTERGAZER_SERVICE_URL = import.meta.env.VITE_CLUSTERGAZER_SERVICE;

// Configure axios with credentials for the User-service api
const Userapi = axios.create({
  baseURL: USER_SERVICE_URL,
  withCredentials: true // Important: this ensures cookies are sent with requests
});


// Configure axios with credentials API for cluster/link services
const clusterApi = axios.create({
  baseURL: CLUSTERGAZER_SERVICE_URL,
  withCredentials: true
});


// Configure axios with credentials API for Team Services
const TeamApi = axios.create({
  baseURL: TEAM_SERVICE_URL,
  withCredentials: true
});



// SIDE-BAR-API

export const fetchAdminName = async () => {
  try {
    const response = await Userapi.get('/auth/user-profile');
    return response.data.full_name || response.data.email;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

export const logout = async () => {
  try {
    await Userapi.post('/auth/logout');
    localStorage.removeItem("userData");
    return true;
  } catch (error) {
    console.error('Error logging out:', error);
    throw error;
  }
};



export const refreshLinkData = async (clusterId, linkId) => {
  try {
    const response = await clusterApi.post(`/clusters/${clusterId}/links/${linkId}/refresh`);
    return response.data;
  } catch (error) {
    console.error('Error refreshing link data:', error);
    throw error;
  }
};

export const fetchLinkTotalMetrics = async (
  clusterId, 
  linkId, 
  startDate = null, 
  endDate = null, 
  compareStartDate = null, 
  compareEndDate = null,
  filterType = null,
  filterMode = null
) => {
  try {
    // Validate required parameters
    if (!clusterId || !linkId) {
      throw new Error('clusterId and linkId are required');
    }

    // Build query parameters - same structure as chart API
    const params = {};
    if (filterType) params.filter_type = filterType;
    if (filterMode) params.filter_mode = filterMode;
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    if (compareStartDate) params.compare_start_date = compareStartDate;
    if (compareEndDate) params.compare_end_date = compareEndDate;

    // Add request timing for debugging
    const startTime = performance.now();
    console.log('Fetching link total metrics with params:', params);
    
    const response = await clusterApi.get(
      `/clusters/${clusterId}/links/${linkId}/metrics/total`,
      { params }
    );
    const endTime = performance.now();
    
    console.debug(`Metrics fetch took ${(endTime - startTime).toFixed(2)}ms`);
    
    return response.data;
  } catch (error) {
    console.error('Error fetching link total metrics:', {
      error,
      clusterId,
      linkId,
      startDate,
      endDate,
      compareStartDate,
      compareEndDate,
      filterType,
      filterMode
    });
    
    // Transform specific error cases
    if (error.response?.status === 404) {
      throw new Error('Link not found');
    }
    if (error.response?.status === 400) {
      throw new Error('Invalid date parameters');
    }
    
    throw error;
  }
};

export const softDeleteLink = async (clusterId, linkId) => {
  try {
    await clusterApi.delete(`/clusters/${clusterId}/links/${linkId}`);
    return true;
  } catch (error) {
    console.error('Error soft-deleting link:', error);
    throw error;
  }
};

export const fetchDeletedLinksByWorkspace = async (workspaceId) => {
  try {
    const response = await clusterApi.get(`/clusters/workspace/${workspaceId}/deleted-links`);
    return response.data;
  } catch (error) {
    console.error('Error fetching deleted links for workspace:', error);
    throw error;
  }
};

export const permanentlyDeleteLink = async (clusterId, linkId) => {
  try {
    console.log(`Making API call to permanently delete link: clusterId=${clusterId}, linkId=${linkId}`);
    
    // Check for invalid parameters
    if (!clusterId || !linkId || clusterId === 'undefined' || linkId === 'undefined') {
      console.error('Invalid parameters for permanentlyDeleteLink:', { clusterId, linkId });
      throw new Error('Invalid clusterId or linkId for permanent deletion');
    }

    // Ensure we're passing string values (MongoDB expects string IDs)
    const clusterIdStr = String(clusterId);
    const linkIdStr = String(linkId);
    
    await clusterApi.delete(`/clusters/${clusterIdStr}/links/${linkIdStr}/permanent`);
    console.log('Link permanently deleted successfully');
    return true;
  } catch (error) {
    console.error('Error permanently deleting link:', error);
    
    // Log more details about the error
    if (error.response) {
      console.error('Error response:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    
    throw error;
  }
};

export const recoverLink = async (clusterId, linkId) => {
  try {
    console.log(`Making API call to recover link: clusterId=${clusterId}, linkId=${linkId}`);
    
    // Check for invalid parameters
    if (!clusterId || !linkId || clusterId === 'undefined' || linkId === 'undefined') {
      console.error('Invalid parameters for recoverLink:', { clusterId, linkId });
      throw new Error('Invalid clusterId or linkId for link recovery');
    }

    // Ensure we're passing string values (MongoDB expects string IDs)
    const clusterIdStr = String(clusterId);
    const linkIdStr = String(linkId);
    
    const response = await clusterApi.post(`/clusters/${clusterIdStr}/links/${linkIdStr}/recover`);
    console.log('Link recovery successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error recovering link:', error);
    
    // Log more details about the error
    if (error.response) {
      console.error('Error response:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    
    throw error;
  }
};

// New functions for cluster operations

export const fetchDeletedClustersByWorkspace = async (workspaceId) => {
  try {
    const response = await clusterApi.get(`/clusters/workspace/${workspaceId}/deleted-clusters`);
    return response.data;
  } catch (error) {
    console.error('Error fetching deleted clusters for workspace:', error);
    throw error;
  }
};

export const permanentlyDeleteCluster = async (clusterId) => {
  try {
    console.log(`Making API call to permanently delete cluster: clusterId=${clusterId}`);
    
    // Check for invalid parameters
    if (!clusterId || clusterId === 'undefined') {
      console.error('Invalid parameter for permanentlyDeleteCluster:', { clusterId });
      throw new Error('Invalid clusterId for permanent deletion');
    }

    // Ensure we're passing string values (MongoDB expects string IDs)
    const clusterIdStr = String(clusterId);
    
    await clusterApi.delete(`/clusters/${clusterIdStr}/permanent`);
    console.log('Cluster permanently deleted successfully');
    return true;
  } catch (error) {
    console.error('Error permanently deleting cluster:', error);
    
    // Log more details about the error
    if (error.response) {
      console.error('Error response:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    
    throw error;
  }
};

export const recoverCluster = async (clusterId) => {
  try {
    console.log(`Making API call to recover cluster: clusterId=${clusterId}`);
    
    // Check for invalid parameters
    if (!clusterId || clusterId === 'undefined') {
      console.error('Invalid parameter for recoverCluster:', { clusterId });
      throw new Error('Invalid clusterId for cluster recovery');
    }

    // Ensure we're passing string values (MongoDB expects string IDs)
    const clusterIdStr = String(clusterId);
    
    const response = await clusterApi.post(`/clusters/${clusterIdStr}/recover`);
    console.log('Cluster recovery successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error recovering cluster:', error);
    
    // Log more details about the error
    if (error.response) {
      console.error('Error response:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    
    throw error;
  }
};





// Function to fetch both accepted and pending invited users for a specific workspace
export const getInvitedUsers = async (workspaceId) => {
  try {
    const response = await TeamApi.get(`invite/workspaces/${workspaceId}/invited_members`);
    if (response.data.status === "success") {
      return response.data; // Return the data containing pending and accepted users
    }
    throw new Error("Failed to fetch invited users");
  } catch (error) {
    console.error("Error fetching invited users:", error);
    throw error; // Throw the error to be handled in the component
  }
};

export const removeUserFromWorkspace = async (workspaceId, email) => {
  // Input validation
  if (!workspaceId || !email) {
    console.error("Missing required parameters");
    return {
      success: false,
      message: "Both workspaceId and email are required"
    };
  }

  try {
    const response = await TeamApi.delete(
      `invite/workspaces/${workspaceId}/remove_user`,
      {
        params: { email },
        validateStatus: (status) => status < 500 // Consider status < 500 as not error
      }
    );

    // Handle different status codes
    if (response.status === 200) {
      console.log("User removed successfully:", response.data);
      return {
        success: true,
        message: "User removed successfully",
        data: response.data
      };
    } else if (response.status === 404) {
      return {
        success: false,
        message: response.data?.detail || "Workspace or user not found"
      };
    } else if (response.status === 403) {
      return {
        success: false,
        message: response.data?.detail || "Unauthorized to perform this action"
      };
    } else {
      return {
        success: false,
        message: response.data?.detail || "Failed to remove user"
      };
    }

  } catch (error) {
    // Handle different error types
    let errorMessage = "Failed to remove user from workspace";
    
    if (error.response) {
      // The request was made and the server responded with a status code
      errorMessage = error.response.data?.detail || error.response.statusText;
    } else if (error.request) {
      // The request was made but no response was received
      errorMessage = "No response received from server";
    } else {
      // Something happened in setting up the request
      errorMessage = error.message;
    }

    console.error("Error removing user:", errorMessage);
    return {
      success: false,
      message: errorMessage
    };
  }
};


// Function to send invitation to team members
export const inviteTeamMember = async (workspaceId, emails) => {
  try {
    // Set default role to "Admin"
    const role = "Admin";  // Default role

    // Prepare the invitation data
    const invitationData = {
      email: emails,  // This can be a single email or an array of emails
      role: role,
    };

    // Make the POST request to invite the team members
    const response = await TeamApi.post(`invite/${workspaceId}`, invitationData);

    if (response.data.status === 'success') {
      // Ensure results are available
      if (response.data.results) {
        console.log('Invitations sent successfully:', response.data.results);
        return response.data.results;  // Return the results (success or error for each email)
      } else {
        throw new Error("No results returned from the server.");
      }
    } else {
      throw new Error('Failed to send invitations');
    }
  } catch (error) {
    // Log the error and provide useful debugging information
    console.error('Error inviting team member:', error);
    // Check for network or timeout errors
    if (error.response) {
      console.error('Response error:', error.response);
    } else if (error.request) {
      console.error('Request error:', error.request);
    } else {
      console.error('Unexpected error:', error.message);
    }
    throw error;  // Propagate error to be handled in the UI
  }
};

