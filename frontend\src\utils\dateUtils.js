// src/utils/dateUtils.js
import { parseISO, formatISO, differenceInCalendarDays, subDays } from "date-fns"

/**
 * Given an ISO start/end, return a same‑length preceding period.
 */
export function getComparisonDates(startDate, endDate) {
  const start = parseISO(startDate)
  const end   = parseISO(endDate)

  // +1 so 2025‑07‑13 to 2025‑07‑13 is 1 day, not 0
  const daysInRange = differenceInCalendarDays(end, start) + 1

  const compareEnd   = subDays(start, 1)
  const compareStart = subDays(compareEnd, daysInRange - 1)

  return {
    compare_start_date: formatISO(compareStart, { representation: "date" }),
    compare_end_date:   formatISO(compareEnd,   { representation: "date" }),
  }
}
