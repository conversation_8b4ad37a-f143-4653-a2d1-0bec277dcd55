import MetricCard from "./metric-card"

const PositionCard = ({
  position,
  comparisonPosition,
  onToggle,
  isChecked,
  title = "Average Position",
  disabled,
  dateRange,
}) => {
  return (
    <MetricCard
      title={title}
      value={position}
      comparisonValue={comparisonPosition}
      color="orange"
      onToggle={onToggle}
      isChecked={isChecked}
      disabled={disabled}
      dateRange={dateRange}
    />
  )
}

export default PositionCard
