import { useState, useRef, useEffect } from "react"
import { ChevronDown, Mail, Plus } from "lucide-react"
import { useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"
import { useWorkspace } from "../hooks/useWorkspace"
import { useLogoutMutation } from "../store/api/authApi"
import { addNotification } from "../store/slices/uiSlice"

const WorkspaceDropdown = ({ adminName, adminEmail }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [setupMessage, setSetupMessage] = useState("")
  const [targetId, setTargetId] = useState(null)
  const dropdownRef = useRef(null)
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const {
    currentWorkspace,
    workspaces,
    loading,
    error,
    switchWorkspace,
    getWorkspaceById,
    isSwitching,
  } = useWorkspace()

  const [logoutMutation] = useLogoutMutation()

  // Close dropdown when clicking outside
  useEffect(() => {
    const onClick = (e) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        setIsOpen(false)
      }
    }
    document.addEventListener("mousedown", onClick)
    return () => document.removeEventListener("mousedown", onClick)
  }, [])

  const handleWorkspaceSwitch = async (workspaceId) => {
    if (isProcessing) return
    setTargetId(workspaceId)
    setIsProcessing(true)
    setSetupMessage("Preparing your workspace...")

    try {
      // small UX delay
      await new Promise(r => setTimeout(r, 2000))
      await switchWorkspace(workspaceId)
      setIsOpen(false)
    } catch (err) {
      dispatch(
        addNotification({
          type: "error",
          title: "Switch Failed",
          message: err.message || "Failed to switch workspace.",
        })
      )
    } finally {
      setIsProcessing(false)
      setTargetId(null)
      setSetupMessage("")
    }
  }

  const handleAddMore = () => {
    navigate("/create-workspace")
    setIsOpen(false)
  }

  const getWorkspaceDomainInfo = (ws) =>
    ws.domain_info?.siteUrl ||
    (ws.has_domain === false ? "No domain set" : "Loading domain...")

  const isLoading = loading || isSwitching || isProcessing

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 hover:bg-gray-50 rounded-lg p-2 transition-colors"
        disabled={isLoading}
      >
        <span className="hidden sm:inline-block text-sm font-medium">{adminName}</span>
        <ChevronDown
          className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? "rotate-180" : ""} ${
            isLoading ? "animate-spin" : ""
          }`}
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div
          className="
            absolute right-0 top-full mt-2 
            w-[249px] h-[244px] 
            bg-white rounded-lg 
            shadow-lg border border-[#EBEBEB] 
            z-50 flex flex-col
          "
        >
          {/* Admin Info */}
          <div className="px-4 pt-4 flex flex-col items-center">
            <span className="font-['Noto Sans'] font-semibold text-[18px] leading-[20px] text-black">
              {adminName}
            </span>
            <div className="mt-1 flex items-center text-[14px] leading-[20px] text-gray-500">
              <Mail className="w-4 h-4 mr-2" />
              <span className="truncate">{adminEmail}</span>
            </div>
          </div>

          {/* Divider */}
          <div className="mt-8 border-t border-[#EBEBEB] mx-4" />

          {/* Legend-style Switch workspace */}
          <div className="relative my-0.5 px-4 h-0">
            <span
              className="
                absolute 
                left-1/2 
                -top-2 
                transform -translate-x-1/2
                bg-white px-2 
                font-['Noto Sans'] 
                font-medium 
                text-[12px] 
                leading-[12px] 
                text-[#A4A4A4]
                text-center 
                tracking-normal
              "
              style={{ lineHeight: "100%" }}
            >
              Switch workspace
            </span>
          </div>

          {/* Workspace List */}
          <div className="flex-1 overflow-y-auto px-3 space-y-2 mt-2.5">
            {workspaces && workspaces.length > 0 ? (
              workspaces.map((ws) => {
                const id = ws.id || ws._id
                const isActive = id === currentWorkspace
                const domain = getWorkspaceDomainInfo(ws)
                const disabled = isProcessing || isActive

                return (
                  <button
                    key={id}
                    onClick={() => handleWorkspaceSwitch(id)}
                    disabled={disabled}
                    className={`
                      w-full py-2 rounded-[8px] 
                      font-['Noto Sans'] text-[14px] leading-[20px] font-medium 
                      transition-colors flex items-center justify-center
                      ${isActive
                        ? "bg-[#352090] text-white"
                        : "bg-[#F4F4F4] text-gray-700 hover:bg-gray-100"}
                    `}
                  >
                    {targetId === id && isProcessing ? (
                      <img
                        src="/Glow loading.gif"
                        alt="Switching…"
                        className="w-6 h-6"
                      />
                    ) : (
                      domain.replace(/^sc-domain:/, "")
                    )}
                  </button>
                )
              })
            ) : (
              <div className="text-sm text-gray-500 text-center">No workspaces available</div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-[#EBEBEB] mx-4" />
          <div className="px-4 py-3">
            <button
              onClick={handleAddMore}
              disabled={isProcessing}
              className="
                w-full 
                font-['Noto Sans'] font-medium text-[14px] leading-[20px] 
                text-[#352090] 
                hover:underline
              "
            >
              + Add More
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default WorkspaceDropdown
