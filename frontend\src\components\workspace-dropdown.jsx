import { useState, useRef, useEffect } from "react"
import { ChevronDown, Mail, Plus } from "lucide-react"
import { useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"
import { useWorkspace } from "../hooks/useWorkspace"
import { useLogoutMutation } from "../store/api/authApi"
import { logout } from "../store/slices/authSlice"
import { addNotification } from "../store/slices/uiSlice"
import { clusterApi } from "../store/api/clusterApi"
import { authApi } from "../store/api/authApi"

const WorkspaceDropdown = ({ adminName, adminEmail, adminInitials }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [setupMessage, setSetupMessage] = useState("")
  const dropdownRef = useRef(null)
  const dispatch = useDispatch()
  const navigate = useNavigate()

  // Use Redux-based workspace hook (this already includes workspace data)
  const { currentWorkspace, workspaces, loading, error, switchWorkspace, getWorkspaceById, isSwitching } =
    useWorkspace()

  // 🔧 FIX: Removed useOnboarding hook to eliminate duplicate API calls
  // OnboardingCheck component handles all onboarding validation

  // Logout mutation
  const [logoutMutation] = useLogoutMutation()

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // 🔑 NEW: Refetch workspaces when switching to get fresh domain info
  useEffect(() => {
    if (isSwitching) {
      console.log("🔄 Workspace switching detected, will refetch workspaces")
    }
  }, [isSwitching])

  const handleWorkspaceSwitch = async (workspaceId) => {
    // Prevent multiple clicks and switching to same workspace
    if (isSwitching || isProcessing) {
      console.log("Switch already in progress")
      return
    }

    try {
      setIsProcessing(true)
      setSetupMessage("Preparing your workspace...")
      console.log("🔄 Switching workspace from dropdown:", workspaceId)

      // Add initial delay for UX
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Switch workspace
      await switchWorkspace(workspaceId)
      console.log("✅ Workspace switched successfully")

      // The switchWorkspace function already handles navigation to the onboarding check route
      // No additional navigation needed here
      console.log("✅ Workspace switch completed")
      
      setIsOpen(false)
    } catch (error) {
      console.error("❌ Failed to switch workspace:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Switch Failed",
          message: error.message || "Failed to switch workspace. Please try again.",
        }),
      )
    } finally {
      setIsProcessing(false)
      setSetupMessage("")
    }
  }

  const handleLogout = async () => {
    try {
      console.log("🔄 Starting logout process from dropdown...")
      
      // Call the logout API
      await logoutMutation().unwrap()
      console.log("✅ Logout API call successful")
      
      // Clear Redux state
      dispatch(logout())
      console.log("✅ Redux state cleared")
      
      // Clear all RTK Query cache to prevent user-profile call
      dispatch(clusterApi.util.resetApiState())
      dispatch(authApi.util.resetApiState())
      console.log("✅ API cache cleared")
      
      // Clear localStorage
      localStorage.clear()
      console.log("✅ LocalStorage cleared")
      
      // Show success notification
      dispatch(
        addNotification({
          type: "success",
          title: "Logged Out",
          message: "You have been logged out successfully",
        }),
      )
      
      // Close dropdown
      setIsOpen(false)
      
      // Navigate to login
      window.location.href = "/login"
      console.log("✅ Navigated to login page")
      
    } catch (error) {
      console.error("❌ Logout failed:", error)
      
      // Even if API call fails, clear local state and redirect
      dispatch(logout())
      dispatch(clusterApi.util.resetApiState())
      dispatch(authApi.util.resetApiState())
      localStorage.clear()
      
      dispatch(
        addNotification({
          type: "error",
          title: "Logout Failed",
          message: "Failed to logout from server, but local session cleared.",
        }),
      )
      
      setIsOpen(false)
      window.location.href = "/login"
    }
  }

  const handleAddMore = () => {
    navigate("/create-workspace")
    setIsOpen(false)
  }

  // Get display name for workspace (use name if available, otherwise truncate ID)
  const getWorkspaceDisplayName = (workspace) => {
    const workspaceId = workspace.id || workspace._id

    // If workspace has a name and it's not empty, use it
    if (workspace.name && workspace.name.trim()) {
      return workspace.name
    }

    // Otherwise, show truncated ID
    return workspaceId.length > 12
      ? `${workspaceId.substring(0, 8)}...${workspaceId.substring(workspaceId.length - 4)}`
      : workspaceId
  }

  // Get domain display info for workspace
  const getWorkspaceDomainInfo = (workspace) => {
    if (workspace.domain_info && workspace.domain_info.siteUrl) {
      return workspace.domain_info.siteUrl
    }
    
    if (workspace.has_domain === false) {
      return "No domain set"
    }
    
    return "Loading domain..."
  }

  // Validate workspace ID format (MongoDB ObjectId is 24 characters hex)
  const isValidObjectId = (id) => {
    return typeof id === "string" && /^[0-9a-fA-F]{24}$/.test(id)
  }

  // Get current workspace object
  const currentWorkspaceObj = getWorkspaceById(currentWorkspace)

  // Debug logging
  console.log("WorkspaceDropdown render:", {
    currentWorkspace,
    currentWorkspaceObj,
    workspaces: workspaces?.length || 0,
    loading,
    isSwitching,
    error,
  })

  const isLoading = loading || isSwitching || isProcessing

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 hover:bg-gray-50 rounded-lg p-2 transition-colors"
        disabled={isLoading}
      >
        <div className="w-8 h-8 rounded-full bg-[#352090] flex items-center justify-center text-white text-sm font-medium">
          {adminInitials}
        </div>
        <span className="hidden sm:inline-block text-sm font-medium">{adminName}</span>
        <ChevronDown
          className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? "rotate-180" : ""} ${
            isLoading ? "animate-spin" : ""
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* User Info Section */}
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-[#352090] flex items-center justify-center text-white text-sm font-medium">
                {adminInitials}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{adminName}</p>
                <div className="flex items-center space-x-1 mt-1">
                  <Mail className="w-3 h-3 text-gray-400" />
                  <p className="text-xs text-gray-500 truncate">{adminEmail}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Switch Workspace Section */}
          <div className="p-4 border-b border-gray-100">
            <p className="text-xs text-gray-500 uppercase tracking-wide font-medium mb-3">Switch workspace</p>

            {/* Loading State */}
            {isLoading && (
              <div className="px-3 py-2 text-sm text-gray-500 flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#352090] mr-2"></div>
                {setupMessage || (isSwitching ? "Switching workspace..." : "Loading workspaces...")}
              </div>
            )}

            {/* Error State */}
            {error && !loading && !isSwitching && <div className="px-3 py-2 text-sm text-red-500">Error: {error}</div>}

            {/* Workspaces List */}
            {!loading && !isSwitching && !error && (
              <div className="space-y-1 max-h-[240px] overflow-y-auto custom-scrollbar">
                {workspaces && workspaces.length > 0 ? (
                  workspaces.map((workspace) => {
                    const workspaceId = workspace.id || workspace._id
                    const isActive = workspaceId === currentWorkspace
                    const isValid = isValidObjectId(workspaceId)
                    const displayName = getWorkspaceDisplayName(workspace)
                    const domainInfo = getWorkspaceDomainInfo(workspace)

                    return (
                      <button
                        key={workspaceId}
                        onClick={() => handleWorkspaceSwitch(workspaceId)}
                        disabled={loading || isSwitching || !isValid || isActive}
                        className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                          isActive
                            ? "bg-[#352090] text-white"
                            : "text-gray-700 hover:bg-gray-100 disabled:text-gray-400 disabled:hover:bg-transparent"
                        }`}
                      >
                        <div className="flex flex-col">
                          <span className="font-medium">{displayName}</span>
                          <span className={`text-xs mt-1 ${
                            isActive ? "text-gray-200" : "text-gray-500"
                          }`}>
                            {domainInfo}
                          </span>
                        </div>
                      </button>
                    )
                  })
                ) : (
                  <div className="px-3 py-2 text-sm text-gray-500">No workspaces available</div>
                )}
              </div>
            )}

            {/* Add More Button */}
            <button
              onClick={handleAddMore}
              className="w-full mt-3 flex items-center justify-center space-x-2 px-3 py-2 text-sm text-[#352090] hover:bg-gray-50 rounded-md transition-colors"
              disabled={loading || isSwitching}
            >
              <Plus className="w-4 h-4" />
              <span>Add More</span>
            </button>
          </div>

          {/* Actions Section */}
          <div className="p-4">
            <button
              onClick={handleLogout}
              className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors"
              disabled={loading || isSwitching}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
              </svg>
              <span>Logout</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default WorkspaceDropdown
