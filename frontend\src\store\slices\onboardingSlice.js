import { createSlice } from "@reduxjs/toolkit"

const getOnboardingState = () => {
  try {
    const stored = localStorage.getItem("onboarding_state")
    return stored ? JSON.parse(stored) : null
  } catch {
    return null
  }
}

const initialState = {
  onboardingState: getOnboardingState(),
  currentStep: "google_auth", // Only workspace-specific steps: google_auth, site_selection
  isCompleted: false,
  workspaceId: localStorage.getItem("currentWorkspaceId") || null,
  workspaceStates: {}, // Map of workspace IDs to their onboarding states
}

const onboardingSlice = createSlice({
  name: "onboarding",
  initialState,
  reducers: {
    setOnboardingState: (state, action) => {
      state.onboardingState = action.payload
      if (action.payload) {
        localStorage.setItem("onboarding_state", JSON.stringify(action.payload))
      }
    },
    setCurrentStep: (state, action) => {
      state.currentStep = action.payload
    },
    setWorkspaceId: (state, action) => {
      state.workspaceId = action.payload
      if (action.payload) {
        localStorage.setItem("currentWorkspaceId", action.payload)
      }
    },
    updateWorkspaceState: (state, action) => {
      const { workspaceId, onboardingState } = action.payload
      state.workspaceStates[workspaceId] = onboardingState
    },
    completeOnboarding: (state, action) => {
      const { workspaceId } = action.payload
      if (workspaceId && state.workspaceStates[workspaceId]) {
        state.workspaceStates[workspaceId].isCompleted = true
        state.workspaceStates[workspaceId].currentStep = "completed"
      }
      // Also update global state if it's the current workspace
      if (workspaceId === state.workspaceId) {
        state.isCompleted = true
        state.currentStep = "completed"
      }
    },
    resetOnboarding: (state, action) => {
      const { workspaceId } = action.payload
      if (workspaceId) {
        // Reset specific workspace
        delete state.workspaceStates[workspaceId]
        if (workspaceId === state.workspaceId) {
          state.currentStep = "google_auth"
          state.isCompleted = false
        }
      } else {
        // Reset all
        state.onboardingState = null
        state.currentStep = "google_auth"
        state.isCompleted = false
        state.workspaceId = null
        state.workspaceStates = {}
        localStorage.removeItem("onboarding_state")
        localStorage.removeItem("currentWorkspaceId")
      }
    },
  },
})

export const { 
  setOnboardingState, 
  setCurrentStep, 
  setWorkspaceId, 
  updateWorkspaceState,
  completeOnboarding, 
  resetOnboarding 
} = onboardingSlice.actions

export default onboardingSlice.reducer
