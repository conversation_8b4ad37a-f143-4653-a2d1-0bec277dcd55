import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import LogoComponent from "../components/logo-component"
import SpiralImage from "../assets/image.png"
import { useForgotPasswordMutation } from "../store/api/authApi"
import { clearError, clearForgotPasswordSuccess } from "../store/slices/authSlice"
import { addNotification } from "../store/slices/uiSlice"

const ForgotPassword = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const [email, setEmail] = useState("")

  const { loading, error, forgotPasswordSuccess } = useSelector((state) => state.auth)
  const [forgotPassword] = useForgotPasswordMutation()

  // Clear error and success state when component mounts
  useEffect(() => {
    dispatch(clearError())
    dispatch(clearForgotPasswordSuccess())
  }, [dispatch])

  const handleSubmit = async (e) => {
    e.preventDefault()
    dispatch(clearError())

    try {
      // Pass just the email string, not an object
      await forgotPassword(email).unwrap()

      dispatch(
        addNotification({
          type: "success",
          message: "Password reset link sent to your email!",
        }),
      )
    } catch (error) {
      console.error("Forgot password error:", error)

      // Handle validation errors from the API
      let errorMessage = "Failed to send reset email. Please try again."

      if (error.data?.detail) {
        if (Array.isArray(error.data.detail)) {
          // Handle validation errors array
          const validationErrors = error.data.detail.map((err) => err.msg).join(", ")
          errorMessage = `Validation error: ${validationErrors}`
        } else if (typeof error.data.detail === "string") {
          // Handle string error message
          errorMessage = error.data.detail
        }
      }

      dispatch(
        addNotification({
          type: "error",
          message: errorMessage,
        }),
      )
    }
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
      {/* Form Section */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10 overflow-auto">
        <div className="w-full max-w-[426px]">
          {/* Logo */}
          <div className="mb-6">
            <LogoComponent />
          </div>

          {!forgotPasswordSuccess ? (
            <>
              <h1 className="text-[30px] font-bold mb-2">Forgot Password</h1>
              <p className="text-[14px] text-gray-600 mb-6">
                Enter your email address and we'll send you a link to reset your password.
              </p>

              <form onSubmit={handleSubmit}>
                <div className="mb-5">
                  <label htmlFor="email" className="block text-[14px] mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    required
                    placeholder="Enter your email"
                    disabled={loading}
                  />
                </div>

                {error && (
                  <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-600">{error}</div>
                )}

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <svg
                        className="animate-spin h-4 w-4 mr-2 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z" />
                      </svg>
                      Sending...
                    </>
                  ) : (
                    "Send Reset Link"
                  )}
                </button>
              </form>
            </>
          ) : (
            <div className="text-center">
              <div className="mb-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <h1 className="text-[30px] font-bold mb-2">Check Your Email</h1>
              <p className="text-[14px] text-gray-600 mb-6">
                We've sent a password reset link to <strong>{email}</strong>. Please check your inbox and follow the
                instructions.
              </p>
              <button
                onClick={() => navigate("/login")}
                className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300"
              >
                Back to Login
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Image Section */}
      <div className="hidden md:block md:w-1/2 relative">
        <div className="absolute inset-0 m-5">
          <img
            src={SpiralImage || "/placeholder.svg"}
            alt="Decorative spiral"
            className="w-full h-full object-cover rounded-[40px]"
          />
        </div>
      </div>
    </div>
  )
}

export default ForgotPassword
