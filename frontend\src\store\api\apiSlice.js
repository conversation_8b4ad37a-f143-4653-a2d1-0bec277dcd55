import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react"

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE || "http://localhost:8000"

const baseQuery = fetchBaseQuery({
  baseUrl: USER_SERVICE_URL,
  credentials: "include", // Always include cookies for session-based auth
  prepareHeaders: (headers) => {
    headers.set("content-type", "application/json")
    return headers
  },
})

// Enhanced base query that can handle different service URLs
const baseQueryWithReauth = async (args, api, extraOptions) => {
  // If the request has a custom baseUrl, create a new fetchBaseQuery for it
  if (typeof args === "object" && args.baseUrl) {
    const { baseUrl, ...restArgs } = args

    const customBaseQuery = fetchBaseQuery({
      baseUrl: baseUrl,
      credentials: "include", // Always include cookies
      prepareHeaders: (headers) => {
        headers.set("content-type", "application/json")
        return headers
      },
    })

    const result = await customBaseQuery(restArgs, api, extraOptions)

    if (result.error && result.error.status === 401) {
      // Handle session expiry - redirect to login
      api.dispatch({ type: "auth/logout" })
    }

    return result
  }

  // Use default base query
  const result = await baseQuery(args, api, extraOptions)

  if (result.error && result.error.status === 401) {
    // Handle session expiry - redirect to login
    api.dispatch({ type: "auth/logout" })
  }

  return result
}

export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["User", "Workspace", "Cluster", "Performance", "Team"],
  endpoints: (builder) => ({}),
})
