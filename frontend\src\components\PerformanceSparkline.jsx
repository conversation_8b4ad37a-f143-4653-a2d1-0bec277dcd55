// src/components/PerformanceSparkline.jsx
import { memo, useMemo } from "react"
import { useGetClusterPerformanceQuery } from "../store/api/clusterApi"
import { Loader } from "lucide-react"
import { getComparisonDates } from "../utils/dateUtils"
import Tippy from "@tippyjs/react"
import "tippy.js/dist/tippy.css"

const MetricDisplay = ({ label, value, color }) => (
  <div className="flex flex-col items-center">
    <span className="text-xs text-gray-500">{label}</span>
    <span className="text-base font-semibold" style={{ color }}>
      {value}
    </span>
  </div>
)

const PerformanceSparkline = memo(({
  clusterId,
  metricType = "clicks",
  // Optionally override default 30-day range:
  startDate: providedStartDate,
  endDate: providedEndDate,
  isLoading: externalLoading = false,
}) => {
  // 1) Date range: last 30 days, unless overridden
  const { startDate, endDate } = useMemo(() => {
    if (providedStartDate && providedEndDate) {
      return { startDate: providedStartDate, endDate: providedEndDate }
    }

    const now = new Date()
    // subtract 3 days for the "end" boundary
    const end = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
    // subtract 30 more days for the "start" boundary
    const start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)

    return {
      startDate: start.toISOString().split("T")[0],  // e.g. "2025-06-14"
      endDate:   end.toISOString().split("T")[0],    // e.g. "2025-07-14"
    }
  }, [providedStartDate, providedEndDate])

  const skipQuery = !clusterId || !startDate || !endDate

  // 2) Fetch totals + comparison
  const {
    data: totals,
    isLoading: queryLoading,
    error,
  } = useGetClusterPerformanceQuery(
    { clusterId, startDate, endDate },
    { skip: skipQuery, pollingInterval: 300_000 }
  )

  // 3) Loading state
  if (externalLoading || queryLoading) {
    return (
      <div className="flex items-center justify-center text-amber-600">
        <Loader className="w-5 h-5 animate-spin" />
      </div>
    )
  }

  // 4) No data
  if (error || !totals || typeof totals[metricType] === "undefined") {
    return (
      <div className="flex items-center justify-center text-xs text-gray-400">
        No data
      </div>
    )
  }

  // 5) Display & comparison values
  const displayValue = totals.formatted?.[metricType] ?? String(totals[metricType])
  const { compare_start_date: compareStart, compare_end_date: compareEnd } =
    getComparisonDates(startDate, endDate)

  const comp = totals.comparison
  const compValue = comp?.formatted?.[metricType] ?? (comp ? String(comp[metricType]) : null)
  const isUp = comp && totals[metricType] > comp[metricType]
  const arrowIcon = isUp ? "/performance_up.svg" : "/performance_down.svg"
  const arrowAlt  = isUp ? "Up" : "Down"

  // 6) Metric props
  const metricProps = {
    clicks:      { color: "#4285F4", label: "Clicks" },
    impressions: { color: "#5E34B2", label: "Impressions" },
  }[metricType] || { color: "#000", label: metricType }

  // 7) Render
  return (
    <div className="flex items-start justify-start gap-1">
      <MetricDisplay value={displayValue} color={metricProps.color} />

      {comp && (
        <Tippy
          content={
            <div className="text-xs">
              <div><strong>Current:</strong> {startDate} → {endDate}</div>
              <div>{metricProps.label}: {displayValue}</div>
              <div className="mt-1"><strong>Compare:</strong> {compareStart} → {compareEnd}</div>
              <div>{metricProps.label}: {compValue}</div>
            </div>
          }
          arrow={true}
          placement="top"
          delay={[200, 100]}
        >
          <img
            src={arrowIcon}
            alt={arrowAlt}
            className="w-4 h-6 cursor-pointer"
          />
        </Tippy>
      )}
    </div>
  )
})

PerformanceSparkline.displayName = "PerformanceSparkline"

export default PerformanceSparkline
