import { <PERSON><PERSON><PERSON>, <PERSON>, Toolt<PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { useMemo, memo } from "react"
import { useGetClusterPerformanceQuery } from "../store/api/clusterApi"

const PerformanceSparkline = memo(({ clusterId }) => {
  // Calculate date range (last 30 days)
  const { startDate, endDate } = useMemo(() => {
    const now = new Date()
    const start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    return {
      startDate: start.toISOString().split("T")[0],
      endDate: now.toISOString().split("T")[0],
    }
  }, [])

  // Fetch performance data with caching
  const {
    data: performanceResponse,
    isLoading,
    error,
  } = useGetClusterPerformanceQuery(
    {
      clusterId,
      startDate,
      endDate,
      perPage: 30,
    },
    {
      skip: !clusterId,
      // Refetch every 5 minutes
      pollingInterval: 300000,
    },
  )

  const performanceData = performanceResponse?.data || []

  if (isLoading) {
    return <div className="animate-pulse h-16 w-40 bg-gray-200 rounded border" />
  }

  if (error || !performanceData || performanceData.length === 0) {
    return (
      <div className="h-16 w-40 flex items-center justify-center text-xs text-gray-400 bg-gray-50 rounded border">
        No data available
      </div>
    )
  }

  // Handle single data point case
  if (performanceData.length === 1) {
    const data = performanceData[0]
    return (
      <div className="h-16 w-40 flex items-center justify-center bg-gray-50 rounded border">
        <div className="text-center">
          <div className="flex justify-center gap-1 mb-1">
            <div className="w-2 h-2 rounded-full bg-[#352090]" title={`Clicks: ${data.clicks}`}></div>
            <div className="w-2 h-2 rounded-full bg-[#AD66FF]" title={`Impressions: ${data.impressions}`}></div>
          </div>
          <div className="text-xs text-gray-600">{data.date}</div>
        </div>
      </div>
    )
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null
    const data = payload[0].payload

    return (
      <div
        className="bg-white p-3 rounded-lg shadow-xl border border-gray-100 z-[9999] pointer-events-none"
        style={{
          position: "fixed",
          transform: "translate(-50%, -100%)",
          marginTop: "-10px",
          maxWidth: "200px",
          whiteSpace: "nowrap",
        }}
      >
        <div className="flex flex-col gap-1">
          <p className="text-xs font-semibold text-gray-600">{data.date}</p>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-[#352090]" />
            <p className="text-sm font-medium text-gray-800">Clicks: {data.clicks.toLocaleString()}</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-[#AD66FF]" />
            <p className="text-sm font-medium text-gray-800">Impressions: {data.impressions.toLocaleString()}</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-[#00A389]" />
            <p className="text-sm font-medium text-gray-800">CTR: {(data.ctr * 100).toFixed(1)}%</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-[#FF9F43]" />
            <p className="text-sm font-medium text-gray-800">Position: {data.position.toFixed(1)}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-16 w-40 relative bg-gray-50 rounded border overflow-visible">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={performanceData} margin={{ top: 8, right: 8, bottom: 8, left: 8 }}>
          <XAxis dataKey="date" hide />
          <YAxis hide />
          <Line
            type="monotone"
            dataKey="clicks"
            stroke="#352090"
            strokeWidth={2}
            dot={false}
            animationDuration={1000}
            activeDot={{ r: 4, stroke: "#352090", strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="impressions"
            stroke="#AD66FF"
            strokeWidth={2}
            dot={false}
            animationDuration={1000}
            activeDot={{ r: 4, stroke: "#AD66FF", strokeWidth: 2 }}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ stroke: "#E5E7EB", strokeWidth: 1 }}
            position={{ x: "auto", y: "auto" }}
            allowEscapeViewBox={{ x: true, y: true }}
            wrapperStyle={{
              outline: "none",
              zIndex: 9999,
              pointerEvents: "none",
            }}
            coordinate={{ x: 0, y: 0 }}
            isAnimationActive={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
})

PerformanceSparkline.displayName = "PerformanceSparkline"

export default PerformanceSparkline
