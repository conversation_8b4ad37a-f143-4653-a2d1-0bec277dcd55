import { useState } from "react"
import LogoComponent from "../components/logo-component"
import PaymentModal from "../components/payment-modal"

const PricingPage = () => {
  const [selectedPlan, setSelectedPlan] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const plans = [
    {
      id: "basic",
      name: "Basic Plan",
      description: "Best for small teams",
      price: 100,
      features: [
        "Track up to 3 websites",
        "Daily performance reports",
        "Access to basic analytics",
        "Email alerts for key metrics",
        "Limited support (via email)",
      ],
    },
    {
      id: "pro",
      name: "Pro Plan",
      description: "Best for small teams",
      price: 100,
      features: [
        "Track up to 3 websites",
        "Daily performance reports",
        "Access to basic analytics",
        "Email alerts for key metrics",
        "Limited support (via email)",
      ],
      highlighted: true,
    },
    {
      id: "enterprise",
      name: "Enterprise Plan",
      description: "Best for small teams",
      price: 100,
      features: [
        "Track up to 3 websites",
        "Daily performance reports",
        "Access to basic analytics",
        "Email alerts for key metrics",
        "Limited support (via email)",
      ],
    },
  ]

  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handlePaymentSubmit = (paymentDetails) => {
    console.log("Payment submitted:", paymentDetails)
    console.log("Selected plan:", selectedPlan)
    setIsModalOpen(false)
    // Here you would typically handle the payment processing
  }

  return (
    <div className="min-h-screen bg-white font-noto flex flex-col items-center py-16 px-4">
      {/* Logo */}
      <div className="mb-8">
        <LogoComponent width={80} height={80} />
      </div>

      {/* Heading */}
      <h1 className="text-3xl font-bold text-gray-800 mb-16">Choose the Perfect Plan for You</h1>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl w-full">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`rounded-3xl border border-gray-200 overflow-hidden flex flex-col ${
              plan.highlighted ? "bg-[#f8f7ff]" : "bg-white"
            }`}
          >
            <div className="p-8 flex-grow">
              <h2 className="text-2xl font-semibold text-gray-800">{plan.name}</h2>
              <p className="text-sm text-gray-600 mb-6">{plan.description}</p>

              <div className="mb-6">
                <span className="text-5xl font-bold">${plan.price}</span>
                <p className="text-gray-500 text-sm mt-1">Per user/month, billed monthly</p>
              </div>

              <div className="mb-8">
                <h3 className="font-medium text-gray-700 mb-4">Core Features</h3>
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <svg
                        className="w-5 h-5 text-[#00ceff] mr-3 mt-0.5 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="px-8 pb-8 mt-auto">
              <button
                onClick={() => handleSelectPlan(plan)}
                className="w-full py-3 bg-[#352090] text-white rounded-lg hover:bg-[#2a1a70] transition-colors duration-300 text-base font-medium"
              >
                Start Now
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Payment Modal */}
      {selectedPlan && (
        <PaymentModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onSubmit={handlePaymentSubmit}
          plan={selectedPlan}
        />
      )}
    </div>
  )
}

export default PricingPage
