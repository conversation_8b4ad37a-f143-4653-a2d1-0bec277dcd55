import { useState, useEffect } from "react"
import { ArrowLeft } from "lucide-react"
import { useParams, useNavigate } from "react-router-dom"
import axios from "axios"
import Navbar from "../components/navbar"
import Sidebar from "../components/sidebar"
import DateRangeSelector from "../components/DateRangeSelector"

// Import the separated sections
import MetricCardsSection from "./sections/metric-cards-section"
import ChartSection from "./sections/chart-section"
import TableSection from "./sections/table-section"

const CLUSTERGAZER_SERVICE_URL = import.meta.env.VITE_CLUSTERGAZER_SERVICE

const ClusterPerformance = () => {
  // 1. Router and Initial Data Setup
  const { clusterId } = useParams()
  const navigate = useNavigate()

  // 2. State Management
  const [activeMetrics, setActiveMetrics] = useState({
    clicks: true,
    impressions: true,
    ctr: true,
    position: true,
  })

  const [chartDateRange, setChartDateRange] = useState({
    start_date: (() => {
      const today = new Date()
      const lastAvailableDate = new Date(today)
      lastAvailableDate.setDate(today.getDate() - 3)
      const startDate = new Date(lastAvailableDate)
      startDate.setMonth(lastAvailableDate.getMonth() - 3)
      return startDate.toISOString().split("T")[0]
    })(),
    end_date: (() => {
      const today = new Date()
      const lastAvailableDate = new Date(today)
      lastAvailableDate.setDate(today.getDate() - 3)
      return lastAvailableDate.toISOString().split("T")[0]
    })(),
  })

  const [dateRangeText, setDateRangeText] = useState("Last 3 months")
  const [chartLoading, setChartLoading] = useState(true)
  const [chartError, setChartError] = useState(null)
  const [chartData, setChartData] = useState([])
  const [chartComparisonData, setChartComparisonData] = useState([])
  const [dataAvailability, setDataAvailability] = useState({
    current: true,
    comparison: null,
    message: null,
  })

  const [totalMetrics, setTotalMetrics] = useState({
    clicks: "0",
    impressions: "0",
    ctr: "0%",
    position: "0",
    comparison: null,
  })

  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [tableFiltered, setTableFiltered] = useState(false)
  const [tableData, setTableData] = useState([])
  const [tableComparisonData, setTableComparisonData] = useState([])
  const [tablePagination, setTablePagination] = useState({
    total: 0,
    page: 1,
    per_page: 10,
    total_pages: 1,
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [dateSelectorKey, setDateSelectorKey] = useState(0)
  const [clusterName, setClusterName] = useState("")

  // 3. Helper Functions
  const formatDateString = (date) => {
    if (!date) return null
    if (typeof date === "string") return date
    const d = new Date(date)
    return d.toISOString().split("T")[0]
  }

  const getLastAvailableDate = () => {
    const today = new Date()
    const lastAvailableDate = new Date(today)
    lastAvailableDate.setDate(today.getDate() - 3)
    return lastAvailableDate
  }

  const calculatePresetRange = (preset) => {
    const lastAvailableDate = getLastAvailableDate()
    const endDate = new Date(lastAvailableDate)
    const startDate = new Date(lastAvailableDate)

    switch (preset) {
      case "7d":
        startDate.setDate(endDate.getDate() - 6) // 6 days back + today = 7 days total
        setDateRangeText("Last 7 days")
        break
      case "28d":
        startDate.setDate(endDate.getDate() - 27) // 27 days back + today = 28 days total
        setDateRangeText("Last 28 days")
        break
      case "3m":
        startDate.setDate(endDate.getDate() - 89) // Approximately 90 days for 3 months
        setDateRangeText("Last 3 months")
        break
      case "6m":
        startDate.setDate(endDate.getDate() - 179) // Approximately 180 days for 6 months
        setDateRangeText("Last 6 months")
        break
      case "12m":
        startDate.setDate(endDate.getDate() - 364) // 364 days back + today = 365 days total
        setDateRangeText("Last 12 months")
        break
      case "26m":
        startDate.setDate(endDate.getDate() - 779) // 779 days back + today = 780 days total
        setDateRangeText("Last 26 months")
        break
      default:
        startDate.setDate(endDate.getDate() - 89) // Default to 3 months
        setDateRangeText("Last 3 months")
    }

    return {
      start_date: formatDateString(startDate),
      end_date: formatDateString(endDate),
    }
  }

  const calculateComparePresetRange = (preset) => {
    const lastAvailableDate = getLastAvailableDate()
    let periodDays = 0
    let periodText = ""

    if (preset === "7d_prev") {
      periodDays = 7
      periodText = "Last 7 days vs previous period"
    } else if (preset === "28d_prev") {
      periodDays = 28
      periodText = "Last 28 days vs previous period"
    } else if (preset === "3m_prev") {
      periodDays = 90
      periodText = "Last 3 months vs previous period"
    } else if (preset === "6m_prev") {
      periodDays = 180
      periodText = "Last 6 months vs previous period"
    } else if (preset === "12m_prev") {
      periodDays = 365
      periodText = "Last 12 months vs previous period"
    } else if (preset === "26m_prev") {
      periodDays = 780
      periodText = "Last 26 months vs previous period"
    } else if (preset === "7d_yoy") {
      periodDays = 7
      periodText = "Last 7 days vs year over year"
    } else if (preset === "28d_yoy") {
      periodDays = 28
      periodText = "Last 28 days vs year over year"
    } else if (preset === "3m_yoy") {
      periodDays = 90
      periodText = "Last 3 months vs year over year"
    } else if (preset === "6m_yoy") {
      periodDays = 180
      periodText = "Last 6 months vs year over year"
    } else if (preset === "12m_yoy") {
      periodDays = 365
      periodText = "Last 12 months vs year over year"
    } else if (preset === "26m_yoy") {
      periodDays = 780
      periodText = "Last 26 months vs year over year"
    }

    setDateRangeText(periodText)

    // Current period: ends on lastAvailableDate, starts (periodDays-1) days before
    const currentEndDate = new Date(lastAvailableDate)
    const currentStartDate = new Date(currentEndDate)
    currentStartDate.setDate(currentEndDate.getDate() - (periodDays - 1))

    if (preset.endsWith("_prev")) {
      // Comparison period: ends 1 day before current period starts, same duration
      const compareEndDate = new Date(currentStartDate)
      compareEndDate.setDate(compareEndDate.getDate() - 1)
      const compareStartDate = new Date(compareEndDate)
      compareStartDate.setDate(compareEndDate.getDate() - (periodDays - 1))

      return {
        start_date: formatDateString(currentStartDate),
        end_date: formatDateString(currentEndDate),
        compare_start_date: formatDateString(compareStartDate),
        compare_end_date: formatDateString(compareEndDate),
      }
    }

    if (preset.endsWith("_yoy")) {
      // Year-over-year: same dates but 1 year earlier
      const compareEndDate = new Date(currentEndDate)
      compareEndDate.setFullYear(compareEndDate.getFullYear() - 1)
      const compareStartDate = new Date(currentStartDate)
      compareStartDate.setFullYear(compareStartDate.getFullYear() - 1)

      return {
        start_date: formatDateString(currentStartDate),
        end_date: formatDateString(currentEndDate),
        compare_start_date: formatDateString(compareStartDate),
        compare_end_date: formatDateString(compareEndDate),
      }
    }

    return {}
  }

  // 4. Data Fetching Effects
  useEffect(() => {
    const fetchTotalMetrics = async () => {
      if (!clusterId) return

      try {
        const params = {}
        if (chartDateRange.filter_type) params.filter_type = chartDateRange.filter_type
        if (chartDateRange.filter_mode) params.filter_mode = chartDateRange.filter_mode
        if (chartDateRange.start_date) params.start_date = chartDateRange.start_date
        if (chartDateRange.end_date) params.end_date = chartDateRange.end_date
        if (chartDateRange.compare_start_date) params.compare_start_date = chartDateRange.compare_start_date
        if (chartDateRange.compare_end_date) params.compare_end_date = chartDateRange.compare_end_date

        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/metrics/total`, {
          params: params,
          withCredentials: true,
        })

        if (response.data) {
          let currentMetrics, comparisonMetrics = null

          if (response.data.formatted) {
            currentMetrics = response.data.formatted
            comparisonMetrics = response.data.comparison?.formatted || null
          } else {
            const values = response.data
            currentMetrics = {
              clicks: values.clicks?.toLocaleString() ?? "0",
              impressions: values.impressions?.toLocaleString() ?? "0",
              ctr: values.ctr ? `${(values.ctr * 100).toFixed(1)}%` : "0%",
              position: values.position ? values.position.toFixed(1) : "0",
            }

            if (values.comparison) {
              comparisonMetrics = {
                clicks: values.comparison.clicks?.toLocaleString() ?? "0",
                impressions: values.comparison.impressions?.toLocaleString() ?? "0",
                ctr: values.comparison.ctr ? `${(values.comparison.ctr * 100).toFixed(1)}%` : "0%",
                position: values.comparison.position ? values.comparison.position.toFixed(1) : "0",
              }
            }
          }

          setTotalMetrics({
            ...currentMetrics,
            comparison: comparisonMetrics,
          })
        }
      } catch (err) {
        console.error("Error fetching total metrics:", err)
        setTotalMetrics({
          clicks: "0",
          impressions: "0",
          ctr: "0%",
          position: "0",
          comparison: null,
        })
      }
    }

    fetchTotalMetrics()
  }, [clusterId, chartDateRange])

  useEffect(() => {
    const fetchChartData = async () => {
      if (!clusterId) return

      try {
        setChartLoading(true)
        setChartError(null)

        const params = {}
        if (chartDateRange.filter_type) params.filter_type = chartDateRange.filter_type
        if (chartDateRange.filter_mode) params.filter_mode = chartDateRange.filter_mode
        if (chartDateRange.start_date) params.start_date = chartDateRange.start_date
        if (chartDateRange.end_date) params.end_date = chartDateRange.end_date
        if (chartDateRange.compare_start_date) params.compare_start_date = chartDateRange.compare_start_date
        if (chartDateRange.compare_end_date) params.compare_end_date = chartDateRange.compare_end_date

        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/performance/data`, {
          params: params,
          withCredentials: true,
        })

        if (response.data) {
          setDataAvailability(response.data.data_availability)
          setChartData(response.data.data || [])
          setChartComparisonData(response.data.comparison_data || [])
        }
      } catch (err) {
        setChartError(err.response?.data?.detail || err.message || "Failed to load chart data")
      } finally {
        setChartLoading(false)
      }
    }

    fetchChartData()
  }, [clusterId, chartDateRange])

  useEffect(() => {
    const fetchTableData = async () => {
      if (!clusterId) return

      setLoading(true)
      setError(null)

      try {
        const params = {
          page: currentPage,
          per_page: itemsPerPage,
        }

        if (chartDateRange.filter_type === "compare") {
          params.filter_type = "compare"
          params.filter_mode = chartDateRange.filter_mode
          params.start_date = chartDateRange.start_date
          params.end_date = chartDateRange.end_date
          params.compare_start_date = chartDateRange.compare_start_date
          params.compare_end_date = chartDateRange.compare_end_date
        } else {
          if (chartDateRange.start_date) params.start_date = chartDateRange.start_date
          if (chartDateRange.end_date) params.end_date = chartDateRange.end_date
        }

        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/performance/data`, {
          params: params,
          withCredentials: true,
        })

        if (response.data) {
          setDataAvailability(response.data.data_availability)
          setTableData(response.data.data || [])
          setTableComparisonData(response.data.comparison_data || [])
          setTablePagination(response.data.pagination)
        }
      } catch (err) {
        setError(err.response?.data?.detail || err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchTableData()
  }, [clusterId, chartDateRange, currentPage, itemsPerPage])

  // Fetch cluster name
  useEffect(() => {
    const fetchClusterName = async () => {
      if (!clusterId) return
      try {
        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/performance`, {
          params: { page: 1, per_page: 1 },
          withCredentials: true,
        })
        if (response.data && response.data.cluster_name) {
          setClusterName(response.data.cluster_name)
        }
      } catch (err) {
        // ignore
      }
    }
    fetchClusterName()
  }, [clusterId])

  // 5. Event Handlers
  const handleChartDateRangeSelection = (dateRange) => {
    if (dateRange.type === "filter") {
      if (dateRange.mode === "preset") {
        const { start_date, end_date } = calculatePresetRange(dateRange.preset)
        setChartDateRange({
          filter_type: "filter",
          filter_mode: "custom",
          start_date,
          end_date,
        })
      } else {
        const lastAvailableDate = getLastAvailableDate()
        const startDate = new Date(dateRange.startDate)
        const endDate = new Date(dateRange.endDate)

        const adjustedEndDate = endDate > lastAvailableDate ? lastAvailableDate : endDate

        const formattedStart = startDate.toLocaleDateString("en-US", { month: "short", day: "numeric" })
        const formattedEnd = adjustedEndDate.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        })
        setDateRangeText(`${formattedStart} - ${formattedEnd}`)

        setChartDateRange({
          filter_type: "filter",
          filter_mode: "custom",
          start_date: formatDateString(startDate),
          end_date: formatDateString(adjustedEndDate),
        })
      }
    } else if (dateRange.type === "compare") {
      if (dateRange.mode === "preset") {
        const { start_date, end_date, compare_start_date, compare_end_date } = calculateComparePresetRange(
          dateRange.preset,
        )
        setChartDateRange({
          filter_type: "compare",
          filter_mode: "custom",
          start_date,
          end_date,
          compare_start_date,
          compare_end_date,
        })
      } else {
        const lastAvailableDate = getLastAvailableDate()
        const startDate = new Date(dateRange.startDate)
        const endDate = new Date(dateRange.endDate)
        const compareStartDate = new Date(dateRange.compareStart)
        const compareEndDate = new Date(dateRange.compareEnd)

        const adjustedEndDate = endDate > lastAvailableDate ? lastAvailableDate : endDate
        const adjustedCompareEndDate = compareEndDate > lastAvailableDate ? lastAvailableDate : compareEndDate

        const formattedStart = startDate.toLocaleDateString("en-US", { month: "short", day: "numeric" })
        const formattedEnd = adjustedEndDate.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        })
        const formattedCompareStart = compareStartDate.toLocaleDateString("en-US", { month: "short", day: "numeric" })
        const formattedCompareEnd = adjustedCompareEndDate.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        })

        setDateRangeText(`${formattedStart} - ${formattedEnd} vs ${formattedCompareStart} - ${formattedCompareEnd}`)

        setChartDateRange({
          filter_type: "compare",
          filter_mode: "custom",
          start_date: formatDateString(startDate),
          end_date: formatDateString(adjustedEndDate),
          compare_start_date: formatDateString(compareStartDate),
          compare_end_date: formatDateString(adjustedCompareEndDate),
        })
      }
    }
    setTableFiltered(true)
    setCurrentPage(1)
  }

  const handleClearTableFilter = () => {
    setTableFiltered(false)
    const lastAvailableDate = getLastAvailableDate()
    const threeMonthsAgo = new Date(lastAvailableDate)
    threeMonthsAgo.setMonth(lastAvailableDate.getMonth() - 3)
    setChartDateRange({
      start_date: formatDateString(threeMonthsAgo),
      end_date: formatDateString(lastAvailableDate),
    })
    setDateRangeText("Last 3 months")
    setCurrentPage(1)
    setDateSelectorKey((prev) => prev + 1)
  }

  const handleMetricToggle = (metric, isChecked) => {
    const checkedCount = Object.values(activeMetrics).filter(Boolean).length
    if (!isChecked && checkedCount === 1) return

    setActiveMetrics((prev) => ({
      ...prev,
      [metric]: isChecked,
    }))
  }

  const handleBack = () => navigate(-1)
  const handlePageChange = (page) => page !== "..." && setCurrentPage(page)

  // Check if we have comparison data
  const hasComparison = dataAvailability.comparison && tableComparisonData.length > 0



  // 6. Render
  if (loading && !tableData.length) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <div className="flex flex-1 justify-center items-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#352090] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading performance data...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />

      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar activePage="overview" />
        </div>

        <main className="flex-1 p-6 pt-[100px] mt-6 md:ml-[200px] overflow-auto">
          <div className="flex items-center mb-6">
            <button className="mr-2 text-gray-600 hover:text-gray-800" onClick={handleBack}>
              <ArrowLeft size={20} />
            </button>
            <h1 className="text-xl font-semibold text-gray-800">
              Aggregated Performance for {clusterName || "Cluster"}
            </h1>
          </div>

          {/* Metric Cards Section */}
          <MetricCardsSection
            totalMetrics={totalMetrics}
            activeMetrics={activeMetrics}
            dateRangeText={dateRangeText}
            onMetricToggle={handleMetricToggle}
          />

          {/* Date Range Controls Section */}
          <div className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex-shrink-0">
                <DateRangeSelector key={dateSelectorKey} onApply={handleChartDateRangeSelection} />
              </div>
              {tableFiltered && (
                <button
                  onClick={handleClearTableFilter}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg border border-gray-300 transition-colors duration-200 flex-shrink-0 whitespace-nowrap shadow-sm hover:shadow-md"
                >
                  Clear Filter
                </button>
              )}
            </div>
          </div>
          {/* Chart Section */}
          <ChartSection
            chartData={chartData}
            comparisonData={chartComparisonData}
            chartLoading={chartLoading}
            chartError={chartError}
            dataAvailability={dataAvailability}
            activeMetrics={activeMetrics}
            chartDateRange={chartDateRange}
            dateRangeText={dateRangeText}
          />
          {/* Table Section */}
          <TableSection
            tableData={tableData}
            comparisonData={tableComparisonData}
            loading={loading}
            hasComparison={hasComparison}
            dateRangeText={dateRangeText}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            tablePagination={tablePagination}
            onPageChange={handlePageChange}
          />
        </main>
      </div>
    </div>
  )
}

export default ClusterPerformance
