import { useState, useEffect } from "react"
import { ArrowLeft, ChevronDown } from "lucide-react"
import { useParams, useNavigate } from "react-router-dom"
import axios from "axios"
import Navbar from "../components/navbar"
import Sidebar from "../components/sidebar"
import ClicksCard from "../components/cards/clicks-card"
import ImpressionsCard from "../components/cards/impressions-card"
import CTRCard from "../components/cards/ctr-card"
import PositionCard from "../components/cards/position-card"
import DateRangeSelector from "../components/DateRangeSelector"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"

const CLUSTERGAZER_SERVICE_URL = import.meta.env.VITE_CLUSTERGAZER_SERVICE

const ClusterPerformance = () => {
  const { clusterId } = useParams()
  const navigate = useNavigate()

  // States
  const [activeMetrics, setActiveMetrics] = useState({
    clicks: true,
    impressions: true,
    ctr: true,
    position: true,
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [totalMetrics, setTotalMetrics] = useState({
    clicks: "0",
    impressions: "0",
    ctr: "0%",
    position: "0",
    comparison: null, // Add comparison data structure
  })
  const [chartData, setChartData] = useState([])
  const [comparisonData, setComparisonData] = useState([])
  const [dataAvailability, setDataAvailability] = useState({
    current: true,
    comparison: null,
    message: null,
  })
  const [chartLoading, setChartLoading] = useState(true)
  const [chartError, setChartError] = useState(null)
  const [tableFiltered, setTableFiltered] = useState(false)
  const [tableData, setTableData] = useState([])
  const [tablePagination, setTablePagination] = useState({ total: 0, page: 1, per_page: 10, total_pages: 1 })
  const [chartDateRange, setChartDateRange] = useState({
    start_date: (() => {
      const lastAvailableDate = new Date()
      lastAvailableDate.setDate(lastAvailableDate.getDate() - 3)
      const startDate = new Date(lastAvailableDate)
      startDate.setMonth(lastAvailableDate.getMonth() - 3)
      return startDate.toISOString().split("T")[0]
    })(),
    end_date: (() => {
      const lastAvailableDate = new Date()
      lastAvailableDate.setDate(lastAvailableDate.getDate() - 3)
      return lastAvailableDate.toISOString().split("T")[0]
    })(),
  })
  const [clusterName, setClusterName] = useState("")
  const [dateSelectorKey, setDateSelectorKey] = useState(0)

  // Metric color and label maps
  const metricColors = {
    clicks: "#4285F4",
    impressions: "#7B61FF",
    ctr: "#00A389",
    position: "#FF9F43",
  }
  const metricLabels = {
    clicks: "Clicks",
    impressions: "Impressions",
    ctr: "CTR",
    position: "Position",
  }

  // Helper: get selected metrics
  const selectedMetrics = Object.entries(activeMetrics)
    .filter(([key, value]) => value)
    .map(([key]) => key)

  // Add this function after selectedMetrics
  const getLastAvailableDate = () => {
    const today = new Date()
    const lastAvailableDate = new Date(today)
    lastAvailableDate.setDate(today.getDate() - 3)
    return lastAvailableDate
  }

  const formatDateString = (date) => {
    if (!date) return null
    if (typeof date === "string") return date

    // Create date in local timezone
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, "0")
    const day = String(d.getDate()).padStart(2, "0")

    return `${year}-${month}-${day}`
  }

  const calculatePresetRange = (preset) => {
    const lastAvailableDate = getLastAvailableDate()
    const startDate = new Date(lastAvailableDate)

    switch (preset) {
      case "7d":
        startDate.setDate(lastAvailableDate.getDate() - 6)
        break
      case "28d":
        startDate.setDate(lastAvailableDate.getDate() - 27)
        break
      case "3m":
        startDate.setMonth(lastAvailableDate.getMonth() - 3)
        break
      case "6m":
        startDate.setMonth(lastAvailableDate.getMonth() - 6)
        break
      case "12m":
        startDate.setMonth(lastAvailableDate.getMonth() - 12)
        break
      case "26m":
        startDate.setMonth(lastAvailableDate.getMonth() - 26)
        break
      default:
        startDate.setMonth(lastAvailableDate.getMonth() - 3)
    }

    return {
      start_date: formatDateString(startDate),
      end_date: formatDateString(lastAvailableDate),
    }
  }

  const calculateComparePresetRange = (preset) => {
    const lastAvailableDate = getLastAvailableDate()
    let periodDays = 0

    if (preset === "7d_prev") periodDays = 7
    else if (preset === "28d_prev") periodDays = 28
    else if (preset === "3m_prev") periodDays = 90
    else if (preset === "6m_prev") periodDays = 180
    else if (preset === "12m_prev") periodDays = 365
    else if (preset === "26m_prev") periodDays = 780
    else if (preset === "7d_yoy") periodDays = 7
    else if (preset === "28d_yoy") periodDays = 28
    else if (preset === "3m_yoy") periodDays = 90
    else if (preset === "6m_yoy") periodDays = 180
    else if (preset === "12m_yoy") periodDays = 365
    else if (preset === "26m_yoy") periodDays = 780

    // Current period: ends on lastAvailableDate, starts periodDays before
    const currentEndDate = new Date(lastAvailableDate)
    const currentStartDate = new Date(currentEndDate)
    currentStartDate.setDate(currentEndDate.getDate() - (periodDays - 1))

    if (preset.endsWith("_prev")) {
      // Comparison period: ends 1 day before current period starts, same duration
      const compareEndDate = new Date(currentStartDate)
      compareEndDate.setDate(compareEndDate.getDate() - 1)
      const compareStartDate = new Date(compareEndDate)
      compareStartDate.setDate(compareEndDate.getDate() - (periodDays - 1))

      return {
        start_date: formatDateString(currentStartDate),
        end_date: formatDateString(currentEndDate),
        compare_start_date: formatDateString(compareStartDate),
        compare_end_date: formatDateString(compareEndDate),
      }
    }

    if (preset.endsWith("_yoy")) {
      // Year-over-year: same dates but 1 year earlier
      const compareEndDate = new Date(currentEndDate)
      compareEndDate.setFullYear(compareEndDate.getFullYear() - 1)
      const compareStartDate = new Date(currentStartDate)
      compareStartDate.setFullYear(compareStartDate.getFullYear() - 1)

      return {
        start_date: formatDateString(currentStartDate),
        end_date: formatDateString(currentEndDate),
        compare_start_date: formatDateString(compareStartDate),
        compare_end_date: formatDateString(compareEndDate),
      }
    }

    return {}
  }

  // Fetch total metrics (all-time, no filters)
  useEffect(() => {
    const fetchTotalMetrics = async () => {
      if (!clusterId) return
      try {
        console.log("Fetching total metrics for cluster:", clusterId)

        // Use the same parameter structure as chart API
        const params = {}
        if (chartDateRange.filter_type) params.filter_type = chartDateRange.filter_type
        if (chartDateRange.filter_mode) params.filter_mode = chartDateRange.filter_mode
        if (chartDateRange.start_date) params.start_date = chartDateRange.start_date
        if (chartDateRange.end_date) params.end_date = chartDateRange.end_date
        if (chartDateRange.compare_start_date) params.compare_start_date = chartDateRange.compare_start_date
        if (chartDateRange.compare_end_date) params.compare_end_date = chartDateRange.compare_end_date

        console.log("Making API request for total metrics with params:", params)

        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/metrics/total`, {
          params: params,
          withCredentials: true,
        })

        console.log("Total metrics response:", response.data)

        if (response.data) {
          let currentMetrics,
            comparisonMetrics = null

          if (response.data.formatted) {
            currentMetrics = response.data.formatted
            comparisonMetrics = response.data.comparison_formatted || null
          } else {
            const values = response.data
            currentMetrics = {
              clicks: values.clicks?.toLocaleString() ?? "0",
              impressions: values.impressions?.toLocaleString() ?? "0",
              ctr: values.ctr ? `${(values.ctr * 100).toFixed(1)}%` : "0%",
              position: values.position ? values.position.toFixed(1) : "0",
            }

            // Handle comparison data if present
            if (values.comparison) {
              comparisonMetrics = {
                clicks: values.comparison.clicks?.toLocaleString() ?? "0",
                impressions: values.comparison.impressions?.toLocaleString() ?? "0",
                ctr: values.comparison.ctr ? `${(values.comparison.ctr * 100).toFixed(1)}%` : "0%",
                position: values.comparison.position ? values.comparison.toFixed(1) : "0",
              }
            }
          }

          setTotalMetrics({
            ...currentMetrics,
            comparison: comparisonMetrics,
          })
        }
      } catch (err) {
        console.error("Error fetching total metrics:", err)
        setTotalMetrics({
          clicks: "0",
          impressions: "0",
          ctr: "0%",
          position: "0",
          comparison: null,
        })
      }
    }
    fetchTotalMetrics()
  }, [clusterId, chartDateRange])

  // Fetch cluster name (from summary or data)
  useEffect(() => {
    const fetchClusterName = async () => {
      if (!clusterId) return
      try {
        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/performance`, {
          params: { page: 1, per_page: 1 },
          withCredentials: true,
        })
        if (response.data && response.data.cluster_name) {
          setClusterName(response.data.cluster_name)
        }
      } catch (err) {
        // ignore
      }
    }
    fetchClusterName()
  }, [clusterId])

  // Fetch chart data (without pagination)
  useEffect(() => {
    const fetchChartData = async () => {
      if (!clusterId) return
      try {
        setChartLoading(true)
        setChartError(null)

        // Build params for backend - only date range and filter parameters
        const params = {}
        if (chartDateRange.filter_type) params.filter_type = chartDateRange.filter_type
        if (chartDateRange.filter_mode) params.filter_mode = chartDateRange.filter_mode
        if (chartDateRange.start_date) params.start_date = chartDateRange.start_date
        if (chartDateRange.end_date) params.end_date = chartDateRange.end_date
        if (chartDateRange.compare_start_date) params.compare_start_date = chartDateRange.compare_start_date
        if (chartDateRange.compare_end_date) params.compare_end_date = chartDateRange.compare_end_date

        console.log("Making API request for chart data with params:", params)

        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/performance/data`, {
          params: params,
          withCredentials: true,
        })

        if (response.data) {
          setDataAvailability(response.data.data_availability)
          setChartData(response.data.data || [])
          setComparisonData(response.data.comparison_data || [])
        }
      } catch (err) {
        console.error("Error fetching chart data:", err)
        setChartError(err.response?.data?.detail || err.message || "Failed to load chart data")
      } finally {
        setChartLoading(false)
      }
    }
    fetchChartData()
  }, [clusterId, chartDateRange])

  // Fetch table data (with pagination)
  useEffect(() => {
    const fetchTableData = async () => {
      if (!clusterId) return
      setLoading(true)
      setError(null)
      try {
        // Build params for table data - include pagination and filters
        const params = {}
        if (chartDateRange.filter_type) params.filter_type = chartDateRange.filter_type
        if (chartDateRange.filter_mode) params.filter_mode = chartDateRange.filter_mode
        if (chartDateRange.start_date) params.start_date = chartDateRange.start_date
        if (chartDateRange.end_date) params.end_date = chartDateRange.end_date
        if (chartDateRange.compare_start_date) params.compare_start_date = chartDateRange.compare_start_date
        if (chartDateRange.compare_end_date) params.compare_end_date = chartDateRange.compare_end_date
        // Always include pagination for table data
        params.page = currentPage
        params.per_page = itemsPerPage

        console.log("Making API request for table data with params:", params)

        const response = await axios.get(`${CLUSTERGAZER_SERVICE_URL}/clusters/${clusterId}/performance/data`, {
          params: params,
          withCredentials: true,
        })
        if (response.data) {
          // Only update table-specific state
          setTableData(response.data.data)
          setTablePagination(response.data.pagination)
        }
      } catch (err) {
        setError(err.response?.data?.detail || err.message)
      } finally {
        setLoading(false)
      }
    }
    fetchTableData()
  }, [clusterId, chartDateRange, currentPage, itemsPerPage])

  // Handle metric toggle
  const handleMetricToggle = (metric, isChecked) => {
    setActiveMetrics((prev) => ({
      ...prev,
      [metric]: isChecked,
    }))
  }

  const handleChartDateRangeSelection = (dateRange) => {
    const lastAvailableDate = getLastAvailableDate()

    if (dateRange.type === "filter") {
      if (dateRange.mode === "preset") {
        const { start_date, end_date } = calculatePresetRange(dateRange.preset)
        setChartDateRange({
          filter_type: "filter",
          filter_mode: "custom",
          start_date,
          end_date,
        })
      } else {
        // For custom date range
        const startDate = new Date(dateRange.startDate)
        const endDate = new Date(dateRange.endDate)

        // Only adjust the end date if it exceeds last available date
        const adjustedEndDate = endDate > lastAvailableDate ? lastAvailableDate : endDate

        setChartDateRange({
          filter_type: "filter",
          filter_mode: "custom",
          start_date: formatDateString(startDate), // Use exact start date
          end_date: formatDateString(adjustedEndDate),
        })
      }
    } else if (dateRange.type === "compare") {
      if (dateRange.mode === "preset") {
        const { start_date, end_date, compare_start_date, compare_end_date } = calculateComparePresetRange(
          dateRange.preset,
        )

        setChartDateRange({
          filter_type: "compare",
          filter_mode: "custom",
          start_date,
          end_date,
          compare_start_date,
          compare_end_date,
        })
      } else {
        // For custom comparison
        const startDate = new Date(dateRange.startDate)
        const endDate = new Date(dateRange.endDate)
        const compareStartDate = new Date(dateRange.compareStart)
        const compareEndDate = new Date(dateRange.compareEnd)

        // Only adjust end dates if they exceed last available date
        const adjustedEndDate = endDate > lastAvailableDate ? lastAvailableDate : endDate
        const adjustedCompareEndDate = compareEndDate > lastAvailableDate ? lastAvailableDate : compareEndDate

        setChartDateRange({
          filter_type: "compare",
          filter_mode: "custom",
          start_date: formatDateString(startDate), // Use exact start date
          end_date: formatDateString(adjustedEndDate),
          compare_start_date: formatDateString(compareStartDate), // Use exact compare start date
          compare_end_date: formatDateString(adjustedCompareEndDate),
        })
      }
    }
    setTableFiltered(true)
    setCurrentPage(1)
  }

  const handleClearTableFilter = () => {
    setTableFiltered(false)
    const today = new Date()
    const threeMonthsAgo = new Date()
    threeMonthsAgo.setMonth(today.getMonth() - 3)
    setChartDateRange({
      start_date: formatDateString(threeMonthsAgo),
      end_date: formatDateString(today),
    })
    setCurrentPage(1)
    setDateSelectorKey((prev) => prev + 1)
  }

  // Navigation
  const handleBack = () => {
    navigate(-1)
  }

  // Count checked metrics
  const checkedCount = Object.values(activeMetrics).filter(Boolean).length

  // Calculate time range gap based on date range
  const calculateDataGap = (startDate, endDate) => {
    if (!startDate || !endDate) return 1

    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())

    if (diffMonths > 12) return 20 // 20-day gaps for >12 months
    if (diffMonths > 3) return 15 // 15-day gaps for >3 months
    return 1 // Daily for ≤3 months
  }

  // Function to aggregate data by interval
  const aggregateDataByInterval = (data, interval) => {
    if (!data || data.length === 0) return []

    // Group data by interval
    const groupedData = data.reduce((acc, item) => {
      const date = new Date(item.date)
      let intervalKey

      if (interval === "month") {
        intervalKey = `${date.getFullYear()}-${date.getMonth() + 1}`
      } else if (interval === "quarter") {
        const quarter = Math.floor(date.getMonth() / 3) + 1
        intervalKey = `${date.getFullYear()}-Q${quarter}`
      } else if (interval === "week") {
        // Get the Monday of the current week
        const monday = new Date(date)
        monday.setDate(date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1))
        intervalKey = monday.toISOString().split("T")[0]
      } else {
        intervalKey = item.date // Daily data
      }

      if (!acc[intervalKey]) {
        acc[intervalKey] = {
          date: intervalKey,
          clicks: 0,
          impressions: 0,
          ctr: 0,
          position: 0,
          count: 0,
        }
      }

      // Sum up the metrics
      acc[intervalKey].clicks += item.clicks
      acc[intervalKey].impressions += item.impressions
      acc[intervalKey].ctr += item.ctr
      acc[intervalKey].position += item.position
      acc[intervalKey].count += 1

      return acc
    }, {})

    // Calculate averages for CTR and position
    return Object.values(groupedData).map((item) => ({
      date: item.date,
      clicks: item.clicks,
      impressions: item.impressions,
      ctr: item.ctr / item.count,
      position: item.position / item.count,
    }))
  }

  // Update the filterChartDataByGap function
  const filterChartDataByGap = (data, gap) => {
    if (!data || data.length === 0) return data

    const start = new Date(chartDateRange.start_date)
    const end = new Date(chartDateRange.end_date)
    const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())
    const diffDays = Math.round((end - start) / (1000 * 60 * 60 * 24))

    // Determine the appropriate interval based on the date range
    let interval
    if (diffMonths > 12) {
      interval = "quarter"
    } else if (diffMonths > 3) {
      interval = "month"
    } else if (diffDays > 31) {
      // More than 1 month but less than or equal to 3 months
      interval = "week"
    } else {
      interval = "day"
    }

    // Aggregate data based on the interval
    return aggregateDataByInterval(data, interval)
  }

  // Add helper function for formatting aggregated values
  const formatAggregatedValue = (metric, value, isAggregated) => {
    if (typeof value !== "number" || isNaN(value)) {
      if (metric === "ctr" || metric === "position") return "-"
      return "0"
    }

    if (metric === "ctr") {
      return `${(value * 100).toFixed(1)}%`
    }
    if (metric === "position") {
      return value.toFixed(1)
    }

    // For clicks and impressions, add 'Total' prefix for aggregated values
    const formattedValue = value.toLocaleString()
    return isAggregated ? `Total: ${formattedValue}` : formattedValue
  }

  // Process and combine current and comparison data
  const processChartData = () => {
    const hasComparison = dataAvailability.comparison && comparisonData.length > 0

    const filteredCurrent = filterChartDataByGap(
      chartData,
      calculateDataGap(chartDateRange.start_date, chartDateRange.end_date),
    )

    if (!hasComparison) {
      return filteredCurrent
    }

    const filteredComparison = filterChartDataByGap(
      comparisonData,
      calculateDataGap(chartDateRange.compare_start_date, chartDateRange.compare_end_date),
    )

    // Combine data with day labels when comparison exists
    const maxLength = Math.max(filteredCurrent.length, filteredComparison.length)
    const combinedData = []

    for (let i = 0; i < maxLength; i++) {
      const currentItem = filteredCurrent[i]
      const comparisonItem = filteredComparison[i]

      const dataPoint = {
        dayLabel: `Day ${i + 1}`,
        currentDate: currentItem?.date || null,
        comparisonDate: comparisonItem?.date || null,
      }

      // Add current data
      if (currentItem) {
        dataPoint.clicks = currentItem.clicks
        dataPoint.impressions = currentItem.impressions
        dataPoint.ctr = currentItem.ctr
        dataPoint.position = currentItem.position
      }

      // Add comparison data with different keys
      if (comparisonItem) {
        dataPoint.clicks_comparison = comparisonItem.clicks
        dataPoint.impressions_comparison = comparisonItem.impressions
        dataPoint.ctr_comparison = comparisonItem.ctr
        dataPoint.position_comparison = comparisonItem.position
      }

      combinedData.push(dataPoint)
    }

    return combinedData
  }

  const processedChartData = processChartData()
  const hasComparison = dataAvailability.comparison && comparisonData.length > 0

  // Custom tooltip for comparison data
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null

    const data = payload[0].payload

    if (hasComparison) {
      return (
        <div className="bg-white border border-[#DCDCDC] rounded-lg p-3 shadow-md min-w-[200px]">
          <div className="font-semibold mb-2 text-center">{label}</div>

          {/* Current Data */}
          <div className="mb-2">
            <div className="text-xs text-gray-600 mb-1">
              Current: {data.currentDate ? new Date(data.currentDate).toLocaleDateString() : "N/A"}
            </div>
            {selectedMetrics.map((metric) => {
              const value = data[metric]
              if (value !== undefined && value !== null) {
                return (
                  <div key={metric} className="text-sm flex justify-between">
                    <span style={{ color: metricColors[metric] }}>{metricLabels[metric]}:</span>
                    <span style={{ color: metricColors[metric] }}>{formatAggregatedValue(metric, value, false)}</span>
                  </div>
                )
              }
              return null
            })}
          </div>

          {/* Comparison Data */}
          <div>
            <div className="text-xs text-gray-600 mb-1">
              Comparison: {data.comparisonDate ? new Date(data.comparisonDate).toLocaleDateString() : "N/A"}
            </div>
            {selectedMetrics.map((metric) => {
              const value = data[`${metric}_comparison`]
              if (value !== undefined && value !== null) {
                return (
                  <div key={`${metric}_comparison`} className="text-sm flex justify-between">
                    <span style={{ color: metricColors[metric] }}>{metricLabels[metric]}:</span>
                    <span style={{ color: metricColors[metric] }}>{formatAggregatedValue(metric, value, false)}</span>
                  </div>
                )
              }
              return null
            })}
          </div>
        </div>
      )
    }

    // Original tooltip for non-comparison data
    const isAggregated = label.includes("-") || label.includes("Q")
    const periodLabel = isAggregated
      ? label.includes("Q")
        ? `Quarter ${label.split("Q")[1]}, ${label.split("-")[0]}`
        : label
      : label

    return (
      <div
        style={{
          background: "white",
          border: "1px solid #DCDCDC",
          borderRadius: 8,
          padding: 10,
          boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
          minWidth: 120,
        }}
      >
        <div style={{ fontWeight: 600, marginBottom: 4 }}>
          {periodLabel}
          {isAggregated && <span style={{ fontSize: "0.8em", color: "#666" }}> (Aggregated)</span>}
        </div>
        {payload.map((entry) => (
          <div key={entry.name} style={{ color: entry.color, fontSize: 13 }}>
            {metricLabels[entry.name]}: {formatAggregatedValue(entry.name, entry.value, isAggregated)}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar activePage="overview" />
        </div>
        <main className="flex-1 p-6 overflow-auto">
          <div className="flex items-center mb-6">
            <button className="mr-2 text-gray-600 hover:text-gray-800" onClick={handleBack}>
              <ArrowLeft size={20} />
            </button>
            <h1 className="text-xl font-semibold text-gray-800">
              Aggregated Performance for {clusterName || "Cluster"}
            </h1>
          </div>
          {/* Metric cards */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="w-[200px] h-[140px]">
              <ClicksCard
                clicks={totalMetrics.clicks}
                comparisonClicks={totalMetrics.comparison?.clicks}
                onToggle={(isChecked) => handleMetricToggle("clicks", isChecked)}
                isChecked={activeMetrics.clicks}
                title="Total Clicks"
                disabled={activeMetrics.clicks && checkedCount === 1}
              />
            </div>
            <div className="w-[200px] h-[140px]">
              <ImpressionsCard
                impressions={totalMetrics.impressions}
                comparisonImpressions={totalMetrics.comparison?.impressions}
                onToggle={(isChecked) => handleMetricToggle("impressions", isChecked)}
                isChecked={activeMetrics.impressions}
                title="Total Impressions"
                disabled={activeMetrics.impressions && checkedCount === 1}
              />
            </div>
            <div className="w-[200px] h-[140px]">
              <CTRCard
                ctr={totalMetrics.ctr}
                comparisonCtr={totalMetrics.comparison?.ctr}
                onToggle={(isChecked) => handleMetricToggle("ctr", isChecked)}
                isChecked={activeMetrics.ctr}
                title="Average CTR"
                disabled={activeMetrics.ctr && checkedCount === 1}
              />
            </div>
            <div className="w-[200px] h-[140px]">
              <PositionCard
                position={totalMetrics.position}
                comparisonPosition={totalMetrics.comparison?.position}
                onToggle={(isChecked) => handleMetricToggle("position", isChecked)}
                isChecked={activeMetrics.position}
                title="Average Position"
                disabled={activeMetrics.position && checkedCount === 1}
              />
            </div>
          </div>
          {/* Chart Container with Date Range Selector and cluster buttons */}
          <div className="bg-white rounded-[20px] border border-[#DCDCDC] p-6 mb-5">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800">Performance Chart</h2>
              <div className="flex items-center gap-3">
                <div style={{ width: 700, height: 43 }}>
                  <DateRangeSelector key={dateSelectorKey} onApply={handleChartDateRangeSelection} />
                </div>
                {tableFiltered && (
                  <button
                    onClick={handleClearTableFilter}
                    className="px-3 py-1 bg-gray-100 rounded text-sm text-gray-700 border border-gray-300 hover:bg-gray-200 ml-2"
                  >
                    Clear Filter
                  </button>
                )}
              </div>
            </div>
            <div className="flex h-[240px]">
              <div className="flex-grow relative">
                {chartLoading ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#352090] mx-auto"></div>
                      <p className="mt-4 text-gray-600">Loading chart data...</p>
                    </div>
                  </div>
                ) : chartError ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-red-500 text-sm">{chartError}</div>
                  </div>
                ) : !dataAvailability.current ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-gray-500 text-sm">{dataAvailability.message}</div>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={processedChartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#DCDCDC" />
                      <XAxis
                        dataKey={hasComparison ? "dayLabel" : "date"}
                        tick={
                          hasComparison
                            ? { fontSize: 12, fill: "#888" }
                            : ({ x, y, payload }) => {
                                const date = payload.value
                                const start = new Date(chartDateRange.start_date)
                                const end = new Date(chartDateRange.end_date)
                                const diffDays = Math.round((end - start) / (1000 * 60 * 60 * 24))
                                const diffMonths =
                                  (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())

                                let value
                                // Check different date formats based on aggregation
                                if (date.includes("Q")) {
                                  // Quarterly format: "2024-Q1" -> "Q1\n2024"
                                  const [year, quarter] = date.split("-Q")
                                  value = [`Q${quarter}`, year]
                                } else if (date.includes("-")) {
                                  const d = new Date(date)
                                  if (diffMonths > 12) {
                                    // Monthly format for > 12 months: "Jan\n2024"
                                    value = [
                                      d.toLocaleDateString("en-US", { month: "short" }),
                                      d.toLocaleDateString("en-US", { year: "numeric" }),
                                    ]
                                  } else if (diffMonths > 3) {
                                    // Monthly format for 3-12 months: "Jan\n2024"
                                    value = [
                                      d.toLocaleDateString("en-US", { month: "short" }),
                                      d.toLocaleDateString("en-US", { year: "numeric" }),
                                    ]
                                  } else if (diffDays > 31) {
                                    // Weekly format: "Jan 01-07\n2024"
                                    const endOfWeek = new Date(d)
                                    endOfWeek.setDate(d.getDate() + 6)
                                    value = [
                                      `${d.toLocaleDateString("en-US", { month: "short", day: "2-digit" })} - ${endOfWeek.toLocaleDateString("en-US", { day: "2-digit" })}`,
                                      d.toLocaleDateString("en-US", { year: "numeric" }),
                                    ]
                                  } else {
                                    // Daily format: "Jan 01\n2024"
                                    value = [
                                      d.toLocaleDateString("en-US", { month: "short", day: "2-digit" }),
                                      d.toLocaleDateString("en-US", { year: "numeric" }),
                                    ]
                                  }
                                } else {
                                  const d = new Date(date)
                                  value = [
                                    d.toLocaleDateString("en-US", { month: "short", day: "2-digit" }),
                                    d.toLocaleDateString("en-US", { year: "numeric" }),
                                  ]
                                }

                                return (
                                  <g transform={`translate(${x},${y})`}>
                                    <text x={0} y={0} dy={12} textAnchor="middle" fill="#888" fontSize={12}>
                                      {value[0]}
                                    </text>
                                    <text x={0} y={0} dy={28} textAnchor="middle" fill="#888" fontSize={12}>
                                      {value[1]}
                                    </text>
                                  </g>
                                )
                              }
                        }
                        tickLine={false}
                        axisLine={{ stroke: "#DCDCDC" }}
                        height={hasComparison ? 30 : 50}
                        minTickGap={10}
                      />

                      {/* Only show Y-axis when not in comparison mode */}
                      {!hasComparison &&
                        selectedMetrics.map((metric, index) => (
                          <YAxis
                            key={metric}
                            yAxisId={metric}
                            orientation={index === 0 ? "left" : "right"}
                            tick={{ fontSize: 12, fill: "#888" }}
                            tickFormatter={(value) => formatAggregatedValue(metric, value, false)}
                            tickLine={false}
                            axisLine={{ stroke: "#DCDCDC" }}
                            label={{
                              value: metricLabels[metric],
                              angle: -90,
                              position: index === 0 ? "insideLeft" : "insideRight",
                              style: {
                                textAnchor: "middle",
                                fill: metricColors[metric],
                                fontSize: 12,
                                fontWeight: "bold",
                              },
                            }}
                          />
                        ))}

                      <Tooltip content={<CustomTooltip />} />

                      {/* Current data lines (solid) */}
                      {selectedMetrics.map((metric) => (
                        <Line
                          key={metric}
                          type="monotone"
                          dataKey={metric}
                          yAxisId={hasComparison ? undefined : metric}
                          stroke={metricColors[metric]}
                          strokeWidth={2}
                          dot={{ r: 4, strokeWidth: 2, fill: metricColors[metric], stroke: "#fff" }}
                          activeDot={{ r: 6, strokeWidth: 2, fill: metricColors[metric], stroke: "#fff" }}
                          connectNulls={true}
                        />
                      ))}

                      {/* Comparison data lines (dotted, slightly offset) */}
                      {hasComparison &&
                        selectedMetrics.map((metric) => (
                          <Line
                            key={`${metric}_comparison`}
                            type="monotone"
                            dataKey={`${metric}_comparison`}
                            stroke={metricColors[metric]}
                            strokeWidth={2}
                            strokeDasharray="5,5"
                            dot={{ r: 3, strokeWidth: 2, fill: metricColors[metric], stroke: "#fff" }}
                            activeDot={{ r: 5, strokeWidth: 2, fill: metricColors[metric], stroke: "#fff" }}
                            connectNulls={true}
                          />
                        ))}
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </div>
            </div>
          </div>
          {/* Performance Table Container */}
          <div className="bg-white rounded-[20px] border border-[#DCDCDC] overflow-hidden mb-5">
            <div className="p-4 border-b border-[#DCDCDC] flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-800">Performance Data</h2>
                <p className="text-sm text-gray-500 mt-1">
                  Showing page {currentPage} of {tablePagination.total_pages} pages
                </p>
              </div>
            </div>
            <div className="overflow-x-auto">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#352090] mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading performance data...</p>
                  </div>
                </div>
              ) : (
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-gray-50 border-b border-[#DCDCDC]">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#4285F4] uppercase tracking-wider">
                        Clicks
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#7B61FF] uppercase tracking-wider">
                        Impressions
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#00A389] uppercase tracking-wider">
                        CTR
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#FF9F43] uppercase tracking-wider">
                        Position
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {tableData.length > 0 ? (
                      tableData.map((row, index) => (
                        <tr key={index} className="border-b border-[#DCDCDC] hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{row.date}</td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#4285F4] font-medium">
                            {row.clicks.toLocaleString()}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#7B61FF] font-medium">
                            {row.impressions.toLocaleString()}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#00A389] font-medium">
                            {(row.ctr * 100).toFixed(1)}%
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#FF9F43] font-medium">
                            {row.position.toFixed(1)}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="5" className="px-6 py-10 text-center text-gray-500">
                          No data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}
            </div>
            {/* Pagination */}
            {tablePagination.total_pages > 1 && (
              <div className="flex justify-between items-center p-4 border-t border-[#DCDCDC]">
                <div className="text-sm text-gray-500">
                  Showing page {currentPage} of {tablePagination.total_pages}
                </div>
                <nav className="flex items-center gap-1">
                  <button
                    className="p-2 border border-[#DCDCDC] rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronDown className="w-4 h-4 transform rotate-90" />
                  </button>
                  <button
                    className="p-2 border border-[#DCDCDC] rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, tablePagination.total_pages))}
                    disabled={currentPage === tablePagination.total_pages}
                  >
                    <ChevronDown className="w-4 h-4 transform -rotate-90" />
                  </button>
                </nav>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}

export default ClusterPerformance
