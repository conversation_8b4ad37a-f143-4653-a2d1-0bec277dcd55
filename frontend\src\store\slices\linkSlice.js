import { createSlice } from "@reduxjs/toolkit"

const initialState = {
  searchQuery: "",
  selectedLink: null,
  ui: {
    isAddModalOpen: false,
    isDeleteModalOpen: false,
    linkToDelete: null,
  },
  // Track processing links for polling
  processingLinks: [],
  // Track refreshing links
  refreshingLinks: [],
  // Store active links by cluster ID
  activeLinks: {},
}

const linkSlice = createSlice({
  name: "link",
  initialState,
  reducers: {
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload
    },
    setSelectedLink: (state, action) => {
      state.selectedLink = action.payload
    },
    openAddModal: (state) => {
      state.ui.isAddModalOpen = true
    },
    closeAddModal: (state) => {
      state.ui.isAddModalOpen = false
    },
    openDeleteModal: (state, action) => {
      state.ui.isDeleteModalOpen = true
      state.ui.linkToDelete = action.payload
    },
    closeDeleteModal: (state) => {
      state.ui.isDeleteModalOpen = false
      state.ui.linkToDelete = null
    },
    clearSearchQuery: (state) => {
      state.searchQuery = ""
    },
    // Processing status management
    addProcessingLink: (state, action) => {
      const linkId = action.payload
      if (!state.processingLinks.includes(linkId)) {
        state.processingLinks.push(linkId)
      }
    },
    removeProcessingLink: (state, action) => {
      const linkId = action.payload
      state.processingLinks = state.processingLinks.filter((id) => id !== linkId)
    },
    clearAllProcessingLinks: (state) => {
      state.processingLinks = []
    },
    // Refreshing status management
    addRefreshingLink: (state, action) => {
      const linkId = action.payload
      if (!state.refreshingLinks.includes(linkId)) {
        state.refreshingLinks.push(linkId)
      }
    },
    removeRefreshingLink: (state, action) => {
      const linkId = action.payload
      state.refreshingLinks = state.refreshingLinks.filter((id) => id !== linkId)
    },
    clearAllRefreshingLinks: (state) => {
      state.refreshingLinks = []
    },
    // Redux store management for links
    setActiveLinks: (state, action) => {
      const { clusterId, links } = action.payload
      state.activeLinks[clusterId] = links
    },
    linkCreated: (state, action) => {
      const { clusterId, newLink } = action.payload
      if (!state.activeLinks[clusterId]) {
        state.activeLinks[clusterId] = []
      }
      // Add to beginning of array (newest first)
      state.activeLinks[clusterId].unshift(newLink)
    },
    linkDeleted: (state, action) => {
      const { clusterId, linkId } = action.payload
      if (state.activeLinks[clusterId]) {
        state.activeLinks[clusterId] = state.activeLinks[clusterId].filter((link) => (link.id || link._id) !== linkId)
      }
    },
    linkRestored: (state, action) => {
      const { clusterId, restoredLink } = action.payload
      if (!state.activeLinks[clusterId]) {
        state.activeLinks[clusterId] = []
      }
      // Add restored link back to active links
      state.activeLinks[clusterId].unshift(restoredLink)
    },
  },
})

export const {
  setSearchQuery,
  setSelectedLink,
  openAddModal,
  closeAddModal,
  openDeleteModal,
  closeDeleteModal,
  clearSearchQuery,
  addProcessingLink,
  removeProcessingLink,
  clearAllProcessingLinks,
  addRefreshingLink,
  removeRefreshingLink,
  clearAllRefreshingLinks,
  setActiveLinks,
  linkCreated,
  linkDeleted,
  linkRestored,
} = linkSlice.actions

export default linkSlice.reducer
