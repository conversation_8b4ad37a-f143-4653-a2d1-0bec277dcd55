import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useSignupMutation } from "../store/api/authApi"
import { clearError, clearSignupSuccess } from "../store/slices/authSlice"
import { addNotification } from "../store/slices/uiSlice"
import LogoComponent from "../components/logo-component"
import SpiralImage from "../assets/image.png"
import GlowLoading from "/Glow loading.gif"
import PhoneInput from "react-phone-input-2"
import "react-phone-input-2/lib/style.css"

// Add custom styles for phone input
const phoneInputStyles = {
  container: {
    width: "100%",
  },
  inputStyle: {
    width: "100%",
    height: "48px",
    fontSize: "14px",
    borderRadius: "12px",
    border: "1px solid #D9D9D9",
  },
  buttonStyle: {
    border: "1px solid #D9D9D9",
    borderRadius: "12px 0 0 12px",
    borderRight: "none",
  },
}

const SignUpForm = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  // Redux state
  const { loading } = useSelector((state) => state.auth)
  const [signup] = useSignupMutation()

  // Local form state
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
  })

  const [validationErrors, setValidationErrors] = useState({})
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)

  // Clear errors when component mounts
  useEffect(() => {
    setValidationErrors({})
  }, [])

  // Handle successful signup
  useEffect(() => {
    if (showSuccessMessage) {
      // Clear form after successful signup
      setFormData({
        fullName: "",
        email: "",
        phoneNumber: "",
        password: "",
        confirmPassword: "",
      })

      // Show success message
      setShowSuccessMessage(true)

      // Show notification
      dispatch(
        addNotification({
          type: "success",
          title: "Signup Successful!",
          message:
            "Please check your email for a verification link. You'll need to verify your email before you can create your first workspace.",
          persistent: true,
        }),
      )
    }
  }, [showSuccessMessage, dispatch])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }))

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors((prev) => ({
        ...prev,
        [name]: "",
      }))
    }
  }

  const handlePhoneChange = (value) => {
    setFormData((prevState) => ({
      ...prevState,
      phoneNumber: value,
    }))

    // Clear validation error for phone
    if (validationErrors.phoneNumber) {
      setValidationErrors((prev) => ({
        ...prev,
        phoneNumber: "",
      }))
    }
  }

  const validateForm = () => {
    const errors = {}

    // Full name validation
    if (!formData.fullName.trim()) {
      errors.fullName = "Full name is required"
    }

    // Email validation
    if (!formData.email.trim()) {
      errors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Please enter a valid email address"
    }

    // Phone number validation
    if (!formData.phoneNumber || formData.phoneNumber.length < 8) {
      errors.phoneNumber = "Please enter a valid phone number"
    }

    // Password validation
    if (!formData.password) {
      errors.password = "Password is required"
    } else if (formData.password.length < 6) {
      errors.password = "Password must be at least 6 characters long"
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = "Please confirm your password"
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match"
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validate form
    if (!validateForm()) {
      return
    }

    try {
      await signup({
        fullName: formData.fullName,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        password: formData.password,
      }).unwrap()

      // Show success notification
      dispatch(
        addNotification({
          type: "success",
          message: "Verification email sent! Please check your inbox to complete registration.",
          persistent: true,
        })
      )

      // Clear form after successful signup
      setFormData({
        fullName: "",
        email: "",
        phoneNumber: "",
        password: "",
        confirmPassword: "",
      })

      // Show success message
      setShowSuccessMessage(true)

    } catch (error) {
      // Clear previous validation errors
      setValidationErrors({})

      const errorDetail = error.data?.detail || '';
      const errorMessage = error.message || '';

      // Check if it's an email already registered error
      if (errorDetail.toLowerCase().includes('error creating user') || 
          errorDetail.toLowerCase().includes('email already registered') ||
          errorDetail.toLowerCase().includes('already exists') ||
          errorMessage.toLowerCase().includes('email already registered')) {
        
        setValidationErrors({
          email: "This email is already registered. Please use a different email or login."
        });
      } else {
        // Handle other validation errors
        const finalErrorMessage = errorDetail || errorMessage || "Signup failed. Please try again.";
        setValidationErrors(prev => ({
          ...prev,
          [determineErrorField(finalErrorMessage)]: finalErrorMessage
        }));
      }
    }
  }

  // Helper function to determine which field an error belongs to
  const determineErrorField = (errorMessage) => {
    const message = errorMessage.toLowerCase()
    if (message.includes('email')) return 'email'
    if (message.includes('password')) return 'password'
    if (message.includes('phone')) return 'phoneNumber'
    if (message.includes('name')) return 'fullName'
    return 'email' // default to email if can't determine
  }

  // If showing success message, display it instead of the form
  if (showSuccessMessage) {
    return (
      <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
        <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10">
          <div className="w-full max-w-[426px] text-center">
            <div className="mb-6">
              <LogoComponent />
            </div>
            <h1 className="text-[30px] font-bold mb-4">Thank You for Signing Up!</h1>
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
              <p className="text-[16px] text-gray-700 mb-4">
                We've sent a verification email to <strong>{formData.email}</strong>
              </p>
              <p className="text-[14px] text-gray-600">
                Please check your inbox and click the verification link to complete your registration.
              </p>
            </div>
            <button
              onClick={() => navigate("/login")}
              className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300"
            >
              Go to Login
            </button>
          </div>
        </div>
        {/* Image Section */}
        <div className="hidden md:block w-1/2 relative">
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src={SpiralImage || "/placeholder.svg"}
              alt="Spiral"
              className="w-full h-full py-4 px-4 object-cover rounded-[40px]"
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen w-full font-['Noto Sans']">
      {/* Left Side: Logo + Form */}
      <div className="relative w-full md:w-full lg:w-1/2 flex items-start md:items-center justify-center p-4 md:p-6 lg:p-8 xl:p-10">
        <div className="w-full max-w-[426px] space-y-3 md:space-y-4 lg:space-y-5">
          {/* Logo */}
          <div className="flex-shrink-0">
            <LogoComponent />
          </div>

          {/* Heading */}
          <h1 className="text-[24px] md:text-[28px] lg:text-[32px] xl:text-[36px] leading-none font-bold text-black">
            Sign Up
          </h1>

          {/* Paragraph */}
          <p className="text-[14px] leading-relaxed text-black">
            Track your website's performance with ease! Sign up now to connect your Google Search Console and gain
            valuable insights.
          </p>

          {/* Error Message */}
          {/* Removed top error div */}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4 lg:space-y-5">
            {/* Full Name Field */}
            <div>
              <label htmlFor="fullName" className="block text-[13px] md:text-[14px] lg:text-[15px] leading-none text-black mb-1.5">
                Full Name
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className={`w-full h-10 md:h-11 lg:h-12 px-3 text-[13px] md:text-[14px] lg:text-[15px] leading-none bg-white border ${
                  validationErrors.fullName ? "border-red-500" : "border-[#D9D9D9]"
                } rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                required
              />
              {validationErrors.fullName && (
                <div className="flex items-center gap-1.5 mt-1.5">
                  <svg
                    className="w-4 h-4 text-red-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-sm text-red-600">{validationErrors.fullName}</p>
                </div>
              )}
            </div>

            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-[14px] md:text-[15px] lg:text-[16px] leading-none text-black mb-1.5">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full h-11 md:h-12 lg:h-14 px-3 md:px-4 text-[14px] md:text-[15px] lg:text-[16px] leading-none bg-white border ${
                  validationErrors.email ? "border-red-500" : "border-[#D9D9D9]"
                } rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                required
              />
              {validationErrors.email && (
                <div className="flex items-center gap-1.5 mt-1.5">
                  <svg
                    className="w-4 h-4 text-red-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-sm text-red-600">{validationErrors.email}</p>
                </div>
              )}
            </div>

            {/* Phone Number Field */}
            <div>
              <label htmlFor="phoneNumber" className="block text-[14px] md:text-[15px] lg:text-[16px] leading-none text-black mb-1.5">
                Phone Number
              </label>
              <PhoneInput
                country={"us"}
                value={formData.phoneNumber}
                onChange={handlePhoneChange}
                containerStyle={phoneInputStyles.container}
                inputStyle={{
                  ...phoneInputStyles.inputStyle,
                  border: validationErrors.phoneNumber ? "1px solid #ef4444" : "1px solid #D9D9D9",
                }}
                buttonStyle={phoneInputStyles.buttonStyle}
                containerClass="w-full"
                required
              />
              {validationErrors.phoneNumber && (
                <div className="flex items-center gap-1.5 mt-1.5">
                  <svg
                    className="w-4 h-4 text-red-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-sm text-red-600">{validationErrors.phoneNumber}</p>
                </div>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-[14px] md:text-[15px] lg:text-[16px] leading-none text-black mb-1.5">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full h-11 md:h-12 lg:h-14 px-3 md:px-4 text-[14px] md:text-[15px] lg:text-[16px] leading-none bg-white border ${
                  validationErrors.password ? "border-red-500" : "border-[#D9D9D9]"
                } rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                required
              />
              {validationErrors.password && (
                <div className="flex items-center gap-1.5 mt-1.5">
                  <svg
                    className="w-4 h-4 text-red-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-sm text-red-600">{validationErrors.password}</p>
                </div>
              )}
            </div>

            {/* Confirm Password Field */}
            <div>
              <label htmlFor="confirmPassword" className="block text-[14px] md:text-[15px] lg:text-[16px] leading-none text-black mb-1.5">
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className={`w-full h-11 md:h-12 lg:h-14 px-3 md:px-4 text-[14px] md:text-[15px] lg:text-[16px] leading-none bg-white border ${
                  validationErrors.confirmPassword ? "border-red-500" : "border-[#D9D9D9]"
                } rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                required
              />
              {validationErrors.confirmPassword && (
                <div className="flex items-center gap-1.5 mt-1.5">
                  <svg
                    className="w-4 h-4 text-red-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-sm text-red-600">{validationErrors.confirmPassword}</p>
                </div>
              )}
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full h-10 md:h-11 lg:h-12 bg-[#352090] text-white text-[14px] md:text-[15px] lg:text-[16px] leading-none font-bold rounded-[12px] hover:bg-[#2a1a70] transition duration-300 disabled:opacity-50 flex items-center justify-center"
            >
              {loading ? (
                <>
                  <img
                    src={GlowLoading}
                    alt="Loading..."
                    className="w-6 h-6 mr-2"
                  />
                  Signing up...
                </>
              ) : (
                "Sign Up"
              )}
            </button>
          </form>

          {/* Login Link */}
          <div className="text-center">
            <p className="text-[14px] md:text-[15px] lg:text-[16px] leading-none text-black">
              Already have an account?{" "}
              <a href="/login" className="ml-1 font-bold text-[#266DF0] hover:underline">
                Log in
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Right Side: Spiral Image */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <div className="absolute inset-0 m-5">
          <img
            src={SpiralImage || "/placeholder.svg"}
            alt="Decorative spiral"
            className="w-full h-full object-cover rounded-[40px]"
          />
        </div>
      </div>
    </div>
  )
}

export default SignUpForm
