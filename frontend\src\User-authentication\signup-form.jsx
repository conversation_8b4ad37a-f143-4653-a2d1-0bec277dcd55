import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useSignupMutation } from "../store/api/authApi"
import { clearError, clearSignupSuccess } from "../store/slices/authSlice"
import { addNotification } from "../store/slices/uiSlice"
import LogoComponent from "../components/logo-component"
import SpiralImage from "../assets/image.png"
import PhoneInput from "react-phone-input-2"
import "react-phone-input-2/lib/style.css"

// Add custom styles for phone input
const phoneInputStyles = {
  container: {
    width: "100%",
  },
  inputStyle: {
    width: "100%",
    height: "48px",
    fontSize: "14px",
    borderRadius: "12px",
    border: "1px solid #D9D9D9",
  },
  buttonStyle: {
    border: "1px solid #D9D9D9",
    borderRadius: "12px 0 0 12px",
    borderRight: "none",
  },
}

const SignUpForm = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  // Redux state
  const { loading, error, signupSuccess } = useSelector((state) => state.auth)
  const [signup] = useSignupMutation()

  // Local form state
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
  })

  const [validationErrors, setValidationErrors] = useState({})
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)

  // Clear errors when component mounts
  useEffect(() => {
    dispatch(clearError())
    dispatch(clearSignupSuccess())
  }, [dispatch])

  // Handle successful signup
  useEffect(() => {
    if (signupSuccess) {
      // Clear form after successful signup
      setFormData({
        fullName: "",
        email: "",
        phoneNumber: "",
        password: "",
        confirmPassword: "",
      })

      // Show success message
      setShowSuccessMessage(true)

      // Show notification
      dispatch(
        addNotification({
          type: "success",
          title: "Signup Successful!",
          message:
            "Please check your email for a verification link. You'll need to verify your email before you can create your first workspace.",
          persistent: true,
        }),
      )
    }
  }, [signupSuccess, dispatch])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }))

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors((prev) => ({
        ...prev,
        [name]: "",
      }))
    }
  }

  const handlePhoneChange = (value) => {
    setFormData((prevState) => ({
      ...prevState,
      phoneNumber: value,
    }))

    // Clear validation error for phone
    if (validationErrors.phoneNumber) {
      setValidationErrors((prev) => ({
        ...prev,
        phoneNumber: "",
      }))
    }
  }

  const validateForm = () => {
    const errors = {}

    // Full name validation
    if (!formData.fullName.trim()) {
      errors.fullName = "Full name is required"
    }

    // Email validation
    if (!formData.email.trim()) {
      errors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Please enter a valid email address"
    }

    // Phone number validation
    if (!formData.phoneNumber || formData.phoneNumber.length < 8) {
      errors.phoneNumber = "Please enter a valid phone number"
    }

    // Password validation
    if (!formData.password) {
      errors.password = "Password is required"
    } else if (formData.password.length < 6) {
      errors.password = "Password must be at least 6 characters long"
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = "Please confirm your password"
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match"
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Clear previous errors
    dispatch(clearError())

    // Validate form
    if (!validateForm()) {
      return
    }

    try {
      await signup({
        fullName: formData.fullName,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        password: formData.password,
      }).unwrap()

      console.log("Signup successful")
    } catch (error) {
      console.error("Signup failed:", error)
      // Error is handled by Redux slice
    }
  }

  // If showing success message, display it instead of the form
  if (showSuccessMessage) {
    return (
      <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
        <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10">
          <div className="w-full max-w-[426px] text-center">
            <div className="mb-6">
              <LogoComponent />
            </div>
            <h1 className="text-[30px] font-bold mb-4">Thank You for Signing Up!</h1>
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
              <p className="text-[16px] text-gray-700 mb-4">
                We've sent a verification email to <strong>{formData.email}</strong>
              </p>
              <p className="text-[14px] text-gray-600">
                Please check your inbox and click the verification link to complete your registration.
              </p>
            </div>
            <button
              onClick={() => navigate("/login")}
              className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300"
            >
              Go to Login
            </button>
          </div>
        </div>
        {/* Image Section */}
        <div className="hidden md:block w-1/2 relative">
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src={SpiralImage || "/placeholder.svg"}
              alt="Spiral"
              className="w-full h-full py-4 px-4 object-cover rounded-[40px]"
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
      {/* Form Section */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10 overflow-auto">
        <div className="w-full max-w-[426px]">
          {/* Logo */}
          <div className="mb-6">
            <LogoComponent />
          </div>

          {/* Heading */}
          <h1 className="text-[30px] font-bold mb-2">Sign Up</h1>
          <p className="text-[14px] text-black mb-6">
            Track your website's performance with ease! Sign up now to connect your Google Search Console and gain
            valuable insights.
          </p>

          {/* Error Message */}
          {error && <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-600">{error}</div>}

          {/* Form */}
          <form onSubmit={handleSubmit} className="w-full">
            <div className="mb-5">
              <label htmlFor="fullName" className="block text-[14px] mb-2">
                Full Name
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className={`w-full h-[48px] px-3 py-2 text-sm bg-white border rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090] ${
                  validationErrors.fullName ? "border-red-500" : "border-[#D9D9D9]"
                }`}
                required
              />
              {validationErrors.fullName && <p className="mt-1 text-sm text-red-600">{validationErrors.fullName}</p>}
            </div>

            <div className="mb-5">
              <label htmlFor="email" className="block text-[14px] mb-2">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full h-[48px] px-3 py-2 text-sm bg-white border rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090] ${
                  validationErrors.email ? "border-red-500" : "border-[#D9D9D9]"
                }`}
                required
              />
              {validationErrors.email && <p className="mt-1 text-sm text-red-600">{validationErrors.email}</p>}
            </div>

            <div className="mb-5">
              <label htmlFor="phoneNumber" className="block text-[14px] mb-2">
                Phone Number
              </label>
              <PhoneInput
                country={"us"}
                value={formData.phoneNumber}
                onChange={handlePhoneChange}
                containerStyle={phoneInputStyles.container}
                inputStyle={{
                  ...phoneInputStyles.inputStyle,
                  border: validationErrors.phoneNumber ? "1px solid #ef4444" : "1px solid #D9D9D9",
                }}
                buttonStyle={phoneInputStyles.buttonStyle}
                containerClass="w-full"
                required
              />
              {validationErrors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.phoneNumber}</p>
              )}
            </div>

            <div className="mb-5">
              <label htmlFor="password" className="block text-[14px] mb-2">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full h-[48px] px-3 py-2 text-sm bg-white border rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090] ${
                  validationErrors.password ? "border-red-500" : "border-[#D9D9D9]"
                }`}
                required
              />
              {validationErrors.password && <p className="mt-1 text-sm text-red-600">{validationErrors.password}</p>}
            </div>

            <div className="mb-6">
              <label htmlFor="confirmPassword" className="block text-[14px] mb-2">
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className={`w-full h-[48px] px-3 py-2 text-sm bg-white border rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090] ${
                  validationErrors.confirmPassword ? "border-red-500" : "border-[#D9D9D9]"
                }`}
                required
              />
              {validationErrors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.confirmPassword}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Signing up...
                </>
              ) : (
                "Sign Up"
              )}
            </button>
          </form>

          {/* Login Link */}
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <a href="/login" className="text-[#352090] hover:underline">
                Log in
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Image Section */}
      <div className="hidden md:block w-1/2 relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <img
            src={SpiralImage || "/placeholder.svg"}
            alt="Spiral"
            className="w-full h-full py-4 px-4 object-cover rounded-[40px]"
          />
        </div>
      </div>
    </div>
  )
}

export default SignUpForm
