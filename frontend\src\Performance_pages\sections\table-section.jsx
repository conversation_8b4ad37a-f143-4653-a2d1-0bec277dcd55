import { ChevronDown } from "lucide-react"

const TableSection = ({
  tableData,
  comparisonData,
  loading,
  hasComparison,
  dateRangeText,
  currentPage,
  itemsPerPage,
  tablePagination,
  onPageChange,
}) => {
  return (
    // Main container with consistent styling
    <div className="bg-white rounded-[20px] border border-[#DCDCDC] p-6 mb-5">
      {/* Header section */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800">Performance Data</h2>
        <p className="text-sm text-gray-500 mt-1">
          Showing page {currentPage} of {tablePagination.total_pages} pages
        </p>
      </div>

      {/* Tables container with loading state */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#352090] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading performance data...</p>
          </div>
        </div>
      ) : (
        // Flex container for tables with gap
        <div className="flex gap-6">
          {/* Current Period Table Container */}
          <div className={`${hasComparison ? "w-1/2" : "w-full"} bg-white rounded-[12px] border border-[#DCDCDC] overflow-hidden`}>
            <div className="bg-gray-50 px-6 py-3 border-b border-[#DCDCDC]">
              <h3 className="text-sm font-medium text-gray-800">
                {dateRangeText.split(" vs ")[0]}
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-[#DCDCDC]">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-[#4285F4] uppercase tracking-wider">
                      Clicks
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-[#7B61FF] uppercase tracking-wider">
                      Impressions
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-[#00A389] uppercase tracking-wider">
                      CTR
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-[#FF9F43] uppercase tracking-wider">
                      Position
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {tableData.length > 0 ? (
                    tableData.map((row, index) => (
                      <tr key={index} className="border-b border-[#DCDCDC] hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{row.date}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#4285F4] font-medium">
                          {row.clicks.toLocaleString()}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#7B61FF] font-medium">
                          {row.impressions.toLocaleString()}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#00A389] font-medium">
                          {(row.ctr * 100).toFixed(1)}%
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#FF9F43] font-medium">
                          {row.position.toFixed(1)}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-10 text-center text-gray-500">
                        No data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Comparison Period Table Container */}
          {hasComparison && (
            <div className="w-1/2 bg-white rounded-[12px] border border-[#DCDCDC] overflow-hidden">
              <div className="bg-gray-50 px-6 py-3 border-b border-[#DCDCDC]">
                <h3 className="text-sm font-medium text-gray-800">
                  {dateRangeText.split(" vs ")[1]}
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-gray-50 border-b border-[#DCDCDC]">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#4285F4] uppercase tracking-wider">
                        Clicks
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#7B61FF] uppercase tracking-wider">
                        Impressions
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#00A389] uppercase tracking-wider">
                        CTR
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-[#FF9F43] uppercase tracking-wider">
                        Position
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {comparisonData.length > 0 ? (
                      comparisonData.map((row, index) => (
                        <tr key={index} className="border-b border-[#DCDCDC] hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{row.date}</td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#4285F4] font-medium">
                            {row.clicks.toLocaleString()}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#7B61FF] font-medium">
                            {row.impressions.toLocaleString()}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#00A389] font-medium">
                            {(row.ctr * 100).toFixed(1)}%
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-[#FF9F43] font-medium">
                            {row.position.toFixed(1)}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="px-6 py-10 text-center text-gray-500">
                          No comparison data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Pagination */}
      {tablePagination.total_pages > 1 && (
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-500">
            Showing page {currentPage} of {tablePagination.total_pages}
          </div>
          <nav className="flex items-center gap-1">
            <button
              className="p-2 border border-[#DCDCDC] rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronDown className="w-4 h-4 transform rotate-90" />
            </button>
            <button
              className="p-2 border border-[#DCDCDC] rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => onPageChange(Math.min(currentPage + 1, tablePagination.total_pages))}
              disabled={currentPage === tablePagination.total_pages}
            >
              <ChevronDown className="w-4 h-4 transform -rotate-90" />
            </button>
          </nav>
        </div>
      )}
    </div>
  )
}

export default TableSection
