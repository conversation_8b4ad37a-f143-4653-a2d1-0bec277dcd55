import { createSlice } from "@reduxjs/toolkit"

const initialState = {
  theme: localStorage.getItem("theme") || "light",
  loading: {
    global: false,
    auth: false,
  },
  notifications: [],
  modals: {
    teamInvitation: false,
    settings: false,
  },
}

const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    setTheme: (state, action) => {
      state.theme = action.payload
      localStorage.setItem("theme", action.payload)
    },
    setLoading: (state, action) => {
      const { key, value } = action.payload
      state.loading[key] = value
    },
    addNotification: (state, action) => {
      state.notifications.push({
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...action.payload,
      })
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter((notification) => notification.id !== action.payload)
    },
    clearNotifications: (state) => {
      state.notifications = []
    },
    toggleModal: (state, action) => {
      const { modal, isOpen } = action.payload
      state.modals[modal] = isOpen
    },
  },
})

export const { setTheme, setLoading, addNotification, removeNotification, clearNotifications, toggleModal } =
  uiSlice.actions

export default uiSlice.reducer
