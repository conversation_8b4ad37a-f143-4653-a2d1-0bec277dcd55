import { use<PERSON><PERSON>back, useMemo, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate, useLocation, useParams } from "react-router-dom"
import { 
  setCurrentPage, 
  setNavigationState,
  updatePageState,
  resetPageState,
  initializeWorkspaceNavigation,
  handleWorkspaceSwitch,
  selectCurrentPage,
  selectIsNavigating,
  selectNavigationType,
  selectPageState,
  NAVIGATION_PAGES,
  buildRoute,
  getPageFromPath
} from "../store/slices/navigationSlice"
import { useWorkspace } from "./useWorkspace"
import { clusterApi } from "../store/api/clusterApi"
import { trashApi } from "../store/api/trashApi"

export const useNavigation = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const location = useLocation()
  const params = useParams()
  const { currentWorkspace, getCurrentWorkspaceId } = useWorkspace()

  // Get workspace ID with fallbacks
  const workspaceId = useMemo(() => {
    // First try the enhanced getCurrentWorkspaceId from useWorkspace
    const workspaceFromHook = getCurrentWorkspaceId()
    if (workspaceFromHook) {
      return workspaceFromHook
    }

    // Fallback 1: Try to get from URL params
    if (params.workspaceId) {
      console.log("🔧 useNavigation: Using workspace ID from URL params:", params.workspaceId)
      return params.workspaceId
    }

    // Fallback 2: Extract from pathname
    const match = location.pathname.match(/\/workspace\/([^/]+)/)
    if (match) {
      console.log("🔧 useNavigation: Using workspace ID from URL fallback:", match[1])
      return match[1]
    }

    return null
  }, [getCurrentWorkspaceId, params.workspaceId, location.pathname])
  
  // Selectors
  const currentPage = useSelector(selectCurrentPage)
  const isNavigating = useSelector(selectIsNavigating)
  const navigationType = useSelector(selectNavigationType)

  // Pre-fetch data for SPA experience when workspace ID is available
  useEffect(() => {
    if (workspaceId && !isNavigating) {
      console.log("🚀 SPA Pre-fetch: Pre-fetching data for workspace:", workspaceId)

      // Pre-fetch clusters data for smooth navigation
      dispatch(clusterApi.util.prefetch('getClusters', workspaceId, { force: false }))

      // Pre-fetch trash data for smooth navigation
      dispatch(trashApi.util.prefetch('getDeletedClusters', workspaceId, { force: false }))
      dispatch(trashApi.util.prefetch('getDeletedLinks', workspaceId, { force: false }))

      // This ensures instant navigation without loading states
      console.log("✅ SPA Pre-fetch: Data pre-fetching initiated for clusters and trash")
    }
  }, [workspaceId, isNavigating, dispatch])

  // Initialize navigation for current workspace
  const initializeNavigation = useCallback((targetWorkspaceId, currentPath) => {
    const effectiveWorkspaceId = targetWorkspaceId || workspaceId
    if (effectiveWorkspaceId) {
      dispatch(initializeWorkspaceNavigation({ workspaceId: effectiveWorkspaceId, currentPath }))
    }
  }, [dispatch, workspaceId])
  
  // Navigate within the same workspace (smooth navigation)
  const navigateToPage = useCallback((page, additionalPath = "") => {
    if (!workspaceId) {
      console.warn("Cannot navigate: no workspace ID available", {
        currentWorkspace: currentWorkspace?.id,
        urlWorkspaceId: params.workspaceId,
        pathname: location.pathname
      })
      return
    }

    // Don't navigate if already on the same page
    if (currentPage === page && !additionalPath) {
      return
    }

    console.log("🚀 useNavigation: Navigating to page", {
      page,
      workspaceId,
      additionalPath,
      currentPage
    })

    // Set navigation state
    dispatch(setNavigationState({ isNavigating: true, navigationType: "intra-workspace" }))

    // Update Redux state
    dispatch(setCurrentPage({
      page,
      workspaceId,
      navigationType: "intra-workspace"
    }))

    // Navigate using React Router with optimized settings
    const route = buildRoute(page, workspaceId, additionalPath)
    navigate(route, { replace: false }) // Use push navigation for better UX

    // Clear navigation state immediately for faster perceived performance
    setTimeout(() => {
      dispatch(setNavigationState({ isNavigating: false }))
    }, 50) // Reduced delay for faster UI response
  }, [workspaceId, currentWorkspace?.id, params.workspaceId, location.pathname, currentPage, dispatch, navigate])
  
  // Navigate to overview page
  const navigateToOverview = useCallback(() => {
    console.log("🔄 navigateToOverview called", { workspaceId, currentPage })
    navigateToPage(NAVIGATION_PAGES.OVERVIEW)
  }, [navigateToPage, workspaceId, currentPage])

  // Navigate to clusters page
  const navigateToClusters = useCallback((additionalPath = "") => {
    console.log("🔄 navigateToClusters called", { workspaceId, currentPage, additionalPath })
    navigateToPage(NAVIGATION_PAGES.CLUSTERS, additionalPath)
  }, [navigateToPage, workspaceId, currentPage])

  // Navigate to trash page
  const navigateToTrash = useCallback(() => {
    console.log("🔄 navigateToTrash called", {
      workspaceId,
      currentPage,
      targetRoute: buildRoute(NAVIGATION_PAGES.TRASH, workspaceId)
    })

    // Ensure trash data is available for immediate display
    if (workspaceId) {
      console.log("🗑️ Pre-fetching trash data for immediate display")
      dispatch(trashApi.util.prefetch('getDeletedClusters', workspaceId, { force: false }))
      dispatch(trashApi.util.prefetch('getDeletedLinks', workspaceId, { force: false }))
    }

    navigateToPage(NAVIGATION_PAGES.TRASH)
  }, [navigateToPage, workspaceId, currentPage, dispatch])

  // Navigate to settings page
  const navigateToSettings = useCallback(() => {
    console.log("🔄 navigateToSettings called", { workspaceId, currentPage })
    navigateToPage(NAVIGATION_PAGES.SETTINGS)
  }, [navigateToPage, workspaceId, currentPage])
  
  // Handle workspace switch (triggers reload)
  const handleWorkspaceSwitchNavigation = useCallback((newWorkspaceId) => {
    dispatch(setNavigationState({ isNavigating: true, navigationType: "inter-workspace" }))
    dispatch(handleWorkspaceSwitch({ newWorkspaceId, currentPath: location.pathname }))
    
    // The actual workspace switch and navigation will be handled by useWorkspace hook
    // This just updates the navigation state
  }, [dispatch, location.pathname])
  
  // Update page-specific state
  const updateCurrentPageState = useCallback((stateUpdate) => {
    dispatch(updatePageState({ page: currentPage, stateUpdate }))
  }, [dispatch, currentPage])
  
  // Get page-specific state
  const getPageState = useCallback((page) => {
    return useSelector(selectPageState(page))
  }, [])
  
  // Reset page state
  const resetCurrentPageState = useCallback(() => {
    dispatch(resetPageState({ page: currentPage }))
  }, [dispatch, currentPage])
  
  // Sync navigation state with current URL (for direct URL access)
  const syncWithCurrentUrl = useCallback(() => {
    const currentPageFromUrl = getPageFromPath(location.pathname)
    if (currentPageFromUrl !== currentPage && currentWorkspace?.id) {
      dispatch(setCurrentPage({ 
        page: currentPageFromUrl, 
        workspaceId: currentWorkspace.id, 
        navigationType: "direct" 
      }))
    }
    return currentPageFromUrl;
  }, [location.pathname, currentPage, currentWorkspace?.id, dispatch])
  
  // Check if a page is currently active
  const isPageActive = useCallback((page) => {
    return currentPage === page
  }, [currentPage])
  
  // Get navigation helpers for specific pages
  const getNavigationHelpers = useCallback(() => {
    return {
      // Overview page helpers
      overview: {
        navigate: navigateToOverview,
        isActive: isPageActive(NAVIGATION_PAGES.OVERVIEW),
        state: getPageState(NAVIGATION_PAGES.OVERVIEW)
      },
      
      // Clusters page helpers
      clusters: {
        navigate: navigateToClusters,
        isActive: isPageActive(NAVIGATION_PAGES.CLUSTERS),
        state: getPageState(NAVIGATION_PAGES.CLUSTERS),
        navigateToCluster: (clusterId) => navigateToClusters(`/${clusterId}`),
        navigateToClusterPerformance: (clusterId) => navigateToClusters(`/${clusterId}/performance`)
      },
      
      // Trash page helpers
      trash: {
        navigate: navigateToTrash,
        isActive: isPageActive(NAVIGATION_PAGES.TRASH),
        state: getPageState(NAVIGATION_PAGES.TRASH)
      },
      
      // Settings page helpers
      settings: {
        navigate: navigateToSettings,
        isActive: isPageActive(NAVIGATION_PAGES.SETTINGS),
        state: getPageState(NAVIGATION_PAGES.SETTINGS)
      }
    }
  }, [navigateToOverview, navigateToClusters, navigateToTrash, navigateToSettings, isPageActive, getPageState])
  
  return {
    // Current state
    currentPage,
    isNavigating,
    navigationType,
    
    // Navigation functions
    navigateToPage,
    navigateToOverview,
    navigateToClusters,
    navigateToTrash,
    navigateToSettings,
    
    // Workspace navigation
    handleWorkspaceSwitchNavigation,
    
    // State management
    updateCurrentPageState,
    getPageState,
    resetCurrentPageState,
    
    // Utilities
    initializeNavigation,
    syncWithCurrentUrl,
    isPageActive,
    getNavigationHelpers,
    
    // Constants
    PAGES: NAVIGATION_PAGES
  }
}

export default useNavigation
