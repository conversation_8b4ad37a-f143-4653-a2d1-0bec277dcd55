import { useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useWorkspace } from "../hooks/useWorkspace"
import InviteUserModal from "../team_invitation/invite-user"

export const Switch = ({ checked = false, onCheckedChange, name, navigationPath = null, workspaceId = null }) => {
  const [isChecked, setIsChecked] = useState(checked)
  const [isInviteModalOpen, setInviteModalOpen] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { currentWorkspace } = useWorkspace()

  // Update checked state when prop changes
  useEffect(() => {
    setIsChecked(checked)
  }, [checked])

  // Handle navigation if path is provided
  const handleNavigation = () => {
    if (navigationPath) {
      const workspaceIdToUse = workspaceId || currentWorkspace?.workspace_id
      const path = workspaceIdToUse ? `/workspace/${workspaceIdToUse}${navigationPath}` : navigationPath
      navigate(path)
    }
  }

  const handleChange = () => {
    const newChecked = !isChecked
    setIsChecked(newChecked)

    // Call the onCheckedChange callback if provided
    if (onCheckedChange) {
      onCheckedChange(newChecked)
    }

    // Handle navigation if path is provided
    if (navigationPath) {
      handleNavigation()
    }
  }

  // Determine if the switch should be active based on current path
  useEffect(() => {
    if (navigationPath) {
      const workspaceIdToUse = workspaceId || currentWorkspace?.workspace_id
      const expectedPath = workspaceIdToUse ? `/workspace/${workspaceIdToUse}${navigationPath}` : navigationPath

      setIsChecked(location.pathname === expectedPath)
    }
  }, [location.pathname, navigationPath, workspaceId, currentWorkspace])

  // Get the correct path for each menu item
  const getItemPath = (id) => {
    if (id === "overview") {
      return currentWorkspace?.workspace_id ? `/workspace/${currentWorkspace.workspace_id}/clusters` : "/"
    }
    if (id === "trash") {
      if (!currentWorkspace?.workspace_id) {
        console.error("No workspace ID available for trash navigation")
        return "/"
      }
      return `/workspace/${currentWorkspace.workspace_id}/trash`
    }
    return `/${id}`
  }

  const openInviteModal = () => setInviteModalOpen(true)
  const closeInviteModal = () => setInviteModalOpen(false)

  return (
    <div className="inline-flex">
      <label className="relative inline-block w-10 h-5 cursor-pointer">
        <input
          type="checkbox"
          className="sr-only peer"
          checked={isChecked}
          onChange={handleChange}
          name={name}
          aria-label={name || "Toggle switch"}
        />
        <div
          className={`absolute inset-0 rounded-full transition duration-200 ease-in-out ${
            isChecked ? "bg-[#352090]" : "bg-gray-200"
          }`}
        ></div>
        <div
          className={`absolute left-0.5 top-0.5 h-4 w-4 rounded-full bg-white transition-transform duration-200 ease-in-out ${
            isChecked ? "translate-x-5" : "translate-x-0"
          }`}
        ></div>
      </label>

      {/* Invite Team Modal */}
      <InviteUserModal
        isOpen={isInviteModalOpen}
        onClose={closeInviteModal}
        workspaceId={currentWorkspace?.workspace_id}
      />
    </div>
  )
}
