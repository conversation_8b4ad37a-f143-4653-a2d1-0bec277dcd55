import { useSelector } from "react-redux"
import {
  useGetDeletedClustersQuery,
  useGetDeletedLinksQuery,
  useRestoreClusterMutation,
  useRestoreLinkMutation,
  usePermanentlyDeleteClusterMutation,
  usePermanentlyDeleteLinkMutation,
} from "../store/api/trashApi"

export const useTrash = (workspaceId) => {
  // Get data from Redux store instead of RTK Query cache
  const { deletedClusters, deletedLinks, searchQuery, restoringItems, deletingItems } = useSelector(
    (state) => state.trash,
  )

  // Still use RTK Query for API calls, but data comes from Redux store
  const { isLoading: isFetchingClusters, error: clustersError } = useGetDeletedClustersQuery(workspaceId, {
    skip: !workspaceId,
  })

  const { isLoading: isFetchingLinks, error: linksError } = useGetDeletedLinksQuery(workspaceId, {
    skip: !workspaceId,
  })

  const [restoreCluster] = useRestoreClusterMutation()
  const [restoreLink] = useRestoreLinkMutation()
  const [permanentlyDeleteCluster] = usePermanentlyDeleteClusterMutation()
  const [permanentlyDeleteLink] = usePermanentlyDeleteLinkMutation()

  // Filter items based on search query
  const filteredClusters = deletedClusters.filter((cluster) =>
    cluster.name?.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const filteredLinks = deletedLinks.filter((link) => link.url?.toLowerCase().includes(searchQuery.toLowerCase()))

  const handleRestoreCluster = async (clusterId) => {
    try {
      await restoreCluster({ clusterId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error restoring cluster:", error)
      return false
    }
  }

  const handleRestoreLink = async (linkId) => {
    try {
      // Find the link to get cluster ID
      const link = deletedLinks.find((l) => l.id === linkId)
      if (!link) return false

      await restoreLink({ clusterId: link.clusterId, linkId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error restoring link:", error)
      return false
    }
  }

  const handlePermanentlyDeleteCluster = async (clusterId) => {
    try {
      await permanentlyDeleteCluster({ clusterId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error permanently deleting cluster:", error)
      return false
    }
  }

  const handlePermanentlyDeleteLink = async (linkId) => {
    try {
      // Find the link to get cluster ID
      const link = deletedLinks.find((l) => l.id === linkId)
      if (!link) return false

      await permanentlyDeleteLink({ clusterId: link.clusterId, linkId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error permanently deleting link:", error)
      return false
    }
  }

  const isItemRestoring = (itemId) => {
    return restoringItems.includes(itemId)
  }

  const isItemDeleting = (itemId) => {
    return deletingItems.includes(itemId)
  }

  return {
    clusters: filteredClusters,
    links: filteredLinks,
    isLoading: isFetchingClusters || isFetchingLinks,
    error: clustersError || linksError,
    handleRestoreCluster,
    handleRestoreLink,
    handlePermanentlyDeleteCluster,
    handlePermanentlyDeleteLink,
    totalClusters: deletedClusters.length,
    totalLinks: deletedLinks.length,
    filteredClustersCount: filteredClusters.length,
    filteredLinksCount: filteredLinks.length,
    isItemRestoring,
    isItemDeleting,
  }
}
