import { useEffect, useState } from "react"
import { useNavigate, usePara<PERSON>, useLocation } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useCheckOnboardingStatusQuery } from "../store/api/onboardingApi"
import { setCurrentWorkspace } from "../store/slices/workspaceSlice"
import { updateWorkspaceState } from "../store/slices/onboardingSlice"
import { addNotification } from "../store/slices/uiSlice"

const OnboardingCheck = () => {
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { workspaceId: urlWorkspaceId } = useParams()
  const { currentWorkspace } = useSelector((state) => state.workspace)
  const { workspaceStates } = useSelector((state) => state.onboarding)
  const { isAuthenticated } = useSelector((state) => state.auth)

  // Determine target workspace ID with priority: URL > Redux state
  const targetWorkspaceId = urlWorkspaceId || currentWorkspace

  // 🔧 FIX: Add deduplication state to prevent rapid duplicate calls
  const [lastProcessedWorkspace, setLastProcessedWorkspace] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)

  // 🔍 DEBUGGING: Component lifecycle logging
  useEffect(() => {
    console.log("🟢 OnboardingCheck MOUNTED", {
      urlWorkspaceId,
      currentWorkspace,
      targetWorkspaceId,
      pathname: location.pathname,
      isAuthenticated,
      timestamp: new Date().toISOString()
    })

    return () => {
      console.log("🔴 OnboardingCheck UNMOUNTED", {
        targetWorkspaceId,
        pathname: location.pathname,
        timestamp: new Date().toISOString()
      })
    }
  }, []) // Empty dependency array for mount/unmount only

  // 🔍 DEBUGGING: Log render only when workspace changes
  if (targetWorkspaceId) {
    console.log("🔄 OnboardingCheck RENDER", {
      targetWorkspaceId,
      pathname: location.pathname,
      isAuthenticated,
      timestamp: new Date().toISOString()
    })
  }

  // 🔍 DEBUGGING: Track RTK Query behavior
  useEffect(() => {
    if (targetWorkspaceId) {
      console.log("📡 RTK Query useCheckOnboardingStatusQuery SETUP", {
        targetWorkspaceId,
        isAuthenticated,
        skip: !targetWorkspaceId || !isAuthenticated,
        timestamp: new Date().toISOString()
      })
    }
  }, [targetWorkspaceId, isAuthenticated])

  const {
    data: onboardingData,
    isLoading,
    error,
    requestId,
    originalArgs,
  } = useCheckOnboardingStatusQuery(targetWorkspaceId, {
    skip: !targetWorkspaceId || !isAuthenticated,
    retry: 1,
    // 🔧 FIX: Optimized refetch strategy for workspace switching
    // Cache invalidation from switchWorkspace ensures fresh data when needed
    refetchOnMountOrArgChange: true, // Refetch when workspace changes
  })

  // 🔍 DEBUGGING: Track RTK Query state changes
  useEffect(() => {
    console.log("📡 RTK Query STATE CHANGE", {
      targetWorkspaceId,
      isLoading,
      hasData: !!onboardingData,
      hasError: !!error,
      requestId,
      originalArgs,
      timestamp: new Date().toISOString()
    })
  }, [isLoading, onboardingData, error, requestId, originalArgs, targetWorkspaceId])

  useEffect(() => {
    console.log("🔍 OnboardingCheck effect triggered:", {
      isLoading,
      error: error?.status,
      targetWorkspaceId,
      isAuthenticated,
      hasOnboardingData: !!onboardingData
    })

    // If not authenticated, redirect to login
    if (!isAuthenticated) {
      console.log("User not authenticated, redirecting to login")
      navigate("/login", { replace: true })
      return
    }

    if (isLoading) {
      setLoading(true)
      return
    }

    if (error) {
      console.error("Error checking onboarding status:", error)
      // If error is 401, redirect to login
      if (error.status === 401) {
        navigate("/login", { replace: true })
      } else {
        // For other errors, redirect to workspace selection
        navigate("/create-workspace", { replace: true })
      }
      setLoading(false)
      return
    }

    if (onboardingData && targetWorkspaceId) {
      console.log("📋 Onboarding data received:", onboardingData)

      // 🔧 FIX: Deduplication check to prevent processing the same workspace multiple times
      if (isProcessing || lastProcessedWorkspace === targetWorkspaceId) {
        console.log("⏭️ Skipping duplicate processing for workspace:", targetWorkspaceId)
        return
      }

      setIsProcessing(true)
      setLastProcessedWorkspace(targetWorkspaceId)

      // Update workspace state in Redux only if it's different
      const currentState = workspaceStates[targetWorkspaceId]
      const newState = {
        isCompleted: onboardingData.completed,
        currentStep: onboardingData.current_step,
        domainInfo: onboardingData.domain_info,
      }

      // Only update if state has changed
      if (!currentState || 
          currentState.isCompleted !== newState.isCompleted ||
          currentState.currentStep !== newState.currentStep ||
          JSON.stringify(currentState.domainInfo) !== JSON.stringify(newState.domainInfo)) {
        dispatch(updateWorkspaceState({
          workspaceId: targetWorkspaceId,
          onboardingState: newState
        }))
      }

      // Set current workspace in Redux if different
      if (currentWorkspace !== targetWorkspaceId) {
        dispatch(setCurrentWorkspace(targetWorkspaceId))
      }

      // Use redirect_to from backend if available, otherwise determine target path
      const targetPath = onboardingData.redirect_to ||
        (onboardingData.completed
          ? `/workspace/${targetWorkspaceId}/clusters`
          : {
              google_auth: `/google_auth/${targetWorkspaceId}`,
              site_selection: `/workspace/${targetWorkspaceId}/site_selection`,
              clusters: `/workspace/${targetWorkspaceId}/clusters`,
            }[onboardingData.current_step] || `/workspace/${targetWorkspaceId}/clusters`)

      // Only navigate if we're not already on the target path
      if (location.pathname !== targetPath) {
        console.log(`🔄 Redirecting to ${targetPath} from ${location.pathname}`)
        navigate(targetPath, { replace: true })
      }

      // Reset processing state after navigation
      setIsProcessing(false)
    }

    setLoading(false)
  }, [onboardingData, isLoading, error, navigate, dispatch, targetWorkspaceId, workspaceStates, currentWorkspace, location.pathname, isProcessing, lastProcessedWorkspace])

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090] mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking your progress...</p>
        </div>
      </div>
    )
  }

  return null
}

export default OnboardingCheck
