import { Search, Refresh<PERSON>w, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { useParams } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useWorkspace } from "../hooks/useWorkspace"
import { useTrash } from "../hooks/useTrash"
import { setActiveTab, setSearchQuery, openDeleteModal, closeDeleteModal } from "../store/slices/trashSlice"
import Navbar from "../components/navbar"
import Sidebar from "../components/sidebar"
import NavigationTest from "../components/navigation-test"
import DeleteConfirmationModal from "../components/delete-confirmation-modal"

const Trash = () => {
  const dispatch = useDispatch()
  const { workspaceId: urlWorkspaceId } = useParams()
  const { currentWorkspace } = useWorkspace()

  // Use currentWorkspace from Redux, fallback to URL parameter
  const activeWorkspaceId = currentWorkspace || urlWorkspaceId

  // Redux state
  const { activeTab, searchQuery, ui } = use<PERSON>elector((state) => state.trash)
  const { isDeleteModalOpen, itemToDelete } = ui

  // Custom hook for trash data and operations
  const {
    clusters,
    links,
    isLoading,
    error,
    handleRestoreCluster,
    handleRestoreLink,
    handlePermanentlyDeleteCluster,
    handlePermanentlyDeleteLink,
    handleManualRefresh,
    totalClusters,
    totalLinks,
    filteredClustersCount,
    filteredLinksCount,
    isItemRestoring,
    isItemDeleting,
  } = useTrash(activeWorkspaceId)

  const handleSearchChange = (e) => {
    dispatch(setSearchQuery(e.target.value))
  }

  const handleTabChange = (tab) => {
    dispatch(setActiveTab(tab))
  }

  const handleRestore = async (id, type) => {
    if (type === "cluster") {
      await handleRestoreCluster(id)
    } else {
      await handleRestoreLink(id)
    }
  }

  const handleDeleteClick = (id, type, name) => {
    dispatch(openDeleteModal({ id, type, name }))
  }

  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return

    let success = false

    if (itemToDelete.type === "cluster") {
      success = await handlePermanentlyDeleteCluster(itemToDelete.id)
    } else {
      success = await handlePermanentlyDeleteLink(itemToDelete.id)
    }

    // Close modal only if deletion was successful
    if (success) {
      dispatch(closeDeleteModal())
    }
  }

  // Render status icon based on status value
  const renderStatusIcon = (status) => {
    switch (status) {
      case "active":
      case "completed":
        return (
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
            <span>Active</span>
          </div>
        )
      case "inactive":
      case "in_progress":
        return (
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
            <span>{status === "in_progress" ? "In Progress" : "Inactive"}</span>
          </div>
        )
      case "error":
      case "no_data":
        return (
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
            <span>{status === "no_data" ? "No Data" : "Error"}</span>
          </div>
        )
      default:
        return (
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-gray-500 mr-2"></div>
            <span>Unknown</span>
          </div>
        )
    }
  }

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090]"></div>
            <p className="mt-4 text-gray-600">Loading trash items...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error && !totalClusters && !totalLinks) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="p-4 bg-red-50 rounded-lg max-w-md">
              <div className="flex items-center gap-2 text-red-600 mb-2">
                <AlertCircle className="w-5 h-5" />
                <h3 className="font-medium">Error</h3>
              </div>
              <p className="text-red-600 mb-4">{error.data?.detail || error.message || "Failed to load trash items"}</p>
              <button onClick={handleManualRefresh} className="text-[#352090] hover:text-[#2a1a70] font-medium">
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />

      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar activePage="trash" />
        </div>

        <main className="flex-1 p-6 overflow-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div className="flex items-center gap-4 mb-4 md:mb-0">
              <h1 className="text-xl font-semibold text-gray-800">Trash</h1>
              <button
                onClick={handleManualRefresh}
                className="flex items-center text-[#352090] hover:text-[#2a1a70] text-sm"
                title="Refresh trash items"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                Refresh
              </button>
            </div>

            <div className="relative w-full md:w-auto">
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-[8px] w-full sm:w-[250px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200 mb-6">
            <button
              className={`px-6 py-3 font-medium text-sm ${
                activeTab === "clusters"
                  ? "text-[#352090] border-b-2 border-[#352090]"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => handleTabChange("clusters")}
            >
              Clusters ({filteredClustersCount})
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm ${
                activeTab === "links"
                  ? "text-[#352090] border-b-2 border-[#352090]"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => handleTabChange("links")}
            >
              Links ({filteredLinksCount})
            </button>
          </div>

          {/* Clusters Table */}
          {activeTab === "clusters" && (
            <div className="bg-white rounded-md border border-gray-200 overflow-x-auto">
              <div className="bg-[#DCDCDC] min-w-full grid grid-cols-5 border-b border-gray-200">
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-[#DCDCDC]">Cluster Name</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Device Filter</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Country Filter</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Date Deleted</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090]">Actions</div>
              </div>

              {clusters.length === 0 && (
                <div className="flex flex-col items-center justify-center py-16">
                  <div className="w-12 h-12 rounded-full bg-[#F8F7FC] flex items-center justify-center mb-4">
                    <Trash2 className="w-6 h-6 text-[#AD66FF]" />
                  </div>
                  <h3 className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                    {searchQuery ? "No matching clusters found" : "No Deleted Clusters"}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {searchQuery ? "Try adjusting your search query" : "Your trash is empty"}
                  </p>
                </div>
              )}

              {clusters.map((cluster) => (
                <div
                  key={cluster.id}
                  className="min-w-full grid grid-cols-5 border-b border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <div className="p-4 text-gray-800 border-r border-gray-200">{cluster.name}</div>
                  <div className="p-4 text-gray-800 border-r border-gray-200">{cluster.deviceFilter}</div>
                  <div className="p-4 text-gray-800 border-r border-gray-200">
                    <div className="flex flex-wrap gap-1">
                      {cluster.countryFilter === "ALL" ? (
                        <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">
                          All Countries
                        </span>
                      ) : (
                        cluster.countryFilter
                          .split(",")
                          .slice(0, 3)
                          .map((country, index) => (
                            <span key={index} className="inline-block px-2 py-1 bg-gray-100 rounded-md text-sm">
                              {country.trim()}
                            </span>
                          ))
                      )}
                      {cluster.countryFilter !== "ALL" && cluster.countryFilter.split(",").length > 3 && (
                        <span className="inline-block px-2 py-1 bg-gray-100 rounded-md text-sm">
                          +{cluster.countryFilter.split(",").length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="p-4 text-gray-800 border-r border-gray-200">
                    <div>
                      <div className="text-sm">{cluster.dateDeleted}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        Permanent deletion: {cluster.permanentDeletionDate}
                        <span className="ml-1 text-red-500">
                          {cluster.daysLeft <= 5 ? `(${cluster.daysLeft} days left)` : ""}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 flex items-center space-x-4">
                    <button
                      className="flex items-center text-[#352090] hover:text-[#2a1a70] disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => handleRestore(cluster.id, "cluster")}
                      disabled={isItemRestoring(cluster.id) || isItemDeleting(cluster.id)}
                    >
                      {isItemRestoring(cluster.id) ? (
                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4 mr-1" />
                      )}
                      <span>Restore</span>
                    </button>
                    <button
                      className="flex items-center text-red-500 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => handleDeleteClick(cluster.id, "cluster", cluster.name)}
                      disabled={isItemRestoring(cluster.id) || isItemDeleting(cluster.id)}
                    >
                      {isItemDeleting(cluster.id) ? (
                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      ) : (
                        <Trash2 className="w-4 h-4 mr-1" />
                      )}
                      <span>Delete Permanently</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Links Table */}
          {activeTab === "links" && (
            <div className="bg-white rounded-md border border-gray-200 overflow-x-auto">
              <div className="bg-[#DCDCDC] min-w-full grid grid-cols-5 border-b border-gray-200">
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-[#DCDCDC]">Cluster Name</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">URL</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Status</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090] border-r border-gray-200">Date Deleted</div>
                <div className="p-4 font-['Noto Sans'] text-[#352090]">Actions</div>
              </div>

              {links.length === 0 && (
                <div className="flex flex-col items-center justify-center py-16">
                  <div className="w-12 h-12 rounded-full bg-[#F8F7FC] flex items-center justify-center mb-4">
                    <Trash2 className="w-6 h-6 text-[#AD66FF]" />
                  </div>
                  <h3 className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                    {searchQuery ? "No matching links found" : "No Deleted Links"}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {searchQuery ? "Try adjusting your search query" : "Your trash is empty"}
                  </p>
                </div>
              )}

              {links.map((link) => (
                <div
                  key={link.id}
                  className="min-w-full grid grid-cols-5 border-b border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <div className="p-4 text-gray-800 border-r border-gray-200">{link.clusterName}</div>
                  <div className="p-4 text-gray-800 border-r border-gray-200 truncate">
                    <a
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {link.url}
                    </a>
                  </div>
                  <div className="p-4 text-gray-800 border-r border-gray-200">{renderStatusIcon(link.status)}</div>
                  <div className="p-4 text-gray-800 border-r border-gray-200">
                    <div>
                      <div className="text-sm">{link.dateDeleted}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        Permanent deletion: {link.permanentDeletionDate}
                        <span className="ml-1 text-red-500">
                          {link.daysLeft <= 5 ? `(${link.daysLeft} days left)` : ""}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 flex items-center space-x-4">
                    <button
                      className="flex items-center text-[#352090] hover:text-[#2a1a70] disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => handleRestore(link.id, "link")}
                      disabled={isItemRestoring(link.id) || isItemDeleting(link.id)}
                    >
                      {isItemRestoring(link.id) ? (
                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4 mr-1" />
                      )}
                      <span>Restore</span>
                    </button>
                    <button
                      className="flex items-center text-red-500 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => handleDeleteClick(link.id, "link", link.url)}
                      disabled={isItemRestoring(link.id) || isItemDeleting(link.id)}
                    >
                      {isItemDeleting(link.id) ? (
                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      ) : (
                        <Trash2 className="w-4 h-4 mr-1" />
                      )}
                      <span>Delete Permanently</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </main>
      </div>

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => dispatch(closeDeleteModal())}
        onConfirm={handleDeleteConfirm}
        title="Confirm Permanent Deletion"
        message={`Are you sure you want to permanently delete this ${itemToDelete?.type}? This action cannot be undone.`}
        isDeleting={itemToDelete ? isItemDeleting(itemToDelete.id) : false}
        confirmText="Delete Permanently"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
      />

      {/* Navigation Test Component - Remove in production */}
      {process.env.NODE_ENV === 'development' && <NavigationTest />}
    </div>
  )
}

export default Trash
