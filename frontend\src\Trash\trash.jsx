import { <PERSON>, Refresh<PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2, <PERSON>, Loader } from "lucide-react"
import { useParams } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useWorkspace } from "../hooks/useWorkspace"
import { useTrash } from "../hooks/useTrash"
import { setActiveTab, setSearchQuery, openDeleteModal, closeDeleteModal } from "../store/slices/trashSlice"
import Navbar from "../components/navbar"
import Sidebar from "../components/sidebar"
import DeleteConfirmationModal from "../components/delete-confirmation-modal"
import Tippy from "@tippyjs/react"
import "tippy.js/dist/tippy.css"

const Trash = () => {
  const dispatch = useDispatch()
  const { workspaceId: urlWorkspaceId } = useParams()
  const { currentWorkspace } = useWorkspace()

  // Use currentWorkspace from Redux, fallback to URL parameter
  const activeWorkspaceId = currentWorkspace || urlWorkspaceId

  // Redux state
  const { activeTab, searchQuery, ui } = useSelector((state) => state.trash)
  const { isDeleteModalOpen, itemToDelete } = ui

  // Custom hook for trash data and operations
  const {
    clusters,
    links,
    isLoading,
    error,
    handleRestoreCluster,
    handleRestoreLink,
    handlePermanentlyDeleteCluster,
    handlePermanentlyDeleteLink,
    handleManualRefresh,
    totalClusters,
    totalLinks,
    filteredClustersCount,
    filteredLinksCount,
    isItemRestoring,
    isItemDeleting,
  } = useTrash(activeWorkspaceId)

  const handleSearchChange = (e) => {
    dispatch(setSearchQuery(e.target.value))
  }

  const handleTabChange = (tab) => {
    dispatch(setActiveTab(tab))
  }

  const handleRestore = async (id, type) => {
    if (type === "cluster") {
      await handleRestoreCluster(id)
    } else {
      await handleRestoreLink(id)
    }
  }

  const handleDeleteClick = (id, type, name) => {
    dispatch(openDeleteModal({ id, type, name }))
  }

  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return

    let success = false

    if (itemToDelete.type === "cluster") {
      success = await handlePermanentlyDeleteCluster(itemToDelete.id)
    } else {
      success = await handlePermanentlyDeleteLink(itemToDelete.id)
    }

    // Close modal only if deletion was successful
    if (success) {
      dispatch(closeDeleteModal())
    }
  }

  // Render status icon based on status value
  const renderStatusIcon = (status) => {
    switch (status) {
      case "completed":
        return (
          <div className="flex items-center">
            <Tippy content="Link data ready" arrow={true} placement="top">
              <img
                src="/success.svg"
                alt="Success"
                className="w-5 h-5 text-green-600 cursor-pointer"
              />
            </Tippy>
          </div>
        )
      case "in_progress":
        return (
          <div className="flex items-center">
            <Tippy content="Processing link data..." arrow={true} placement="top">
              <Loader className="w-5 h-5 animate-spin" />
            </Tippy>
          </div>
        )
      case "error":
        return (
          <div className="flex items-center">
            <Tippy content="Link processing error" arrow={true} placement="top">
              <AlertCircle className="w-5 h-5" />
            </Tippy>
          </div>
        )
      default:
        return (
          <div className="flex items-center">
            <Tippy content="Pending link processing" arrow={true} placement="top">
              <Clock className="w-5 h-5" />
            </Tippy>
          </div>
        )
    }
  }

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <div className="fixed top-0 left-0 right-0 h-16 bg-white z-30">
          <Navbar />
        </div>
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090]"></div>
            <p className="mt-4 text-gray-600">Loading trash items...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error && !totalClusters && !totalLinks) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="p-4 bg-red-50 rounded-lg max-w-md">
              <div className="flex items-center gap-2 text-red-600 mb-2">
                <AlertCircle className="w-5 h-5" />
                <h3 className="font-medium">Error</h3>
              </div>
              <p className="text-red-600 mb-4">{error.data?.detail || error.message || "Failed to load trash items"}</p>
              <button onClick={handleManualRefresh} className="text-[#352090] hover:text-[#2a1a70] font-medium">
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />

      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block fixed top-16 bottom-0 left-0 bg-white z-20">
          <Sidebar activePage="Trash" />
        </div>

        <main className="flex-1 p-6 pt-[100px] mt-6 md:ml-[200px] overflow-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <div className="flex items-center gap-4 mb-4 md:mb-0">
              <h1 className="text-xl font-semibold text-gray-800">Trash</h1>
            </div>

            <div className="relative w-full md:w-auto">
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-[8px] w-full sm:w-[250px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
          </div>

          {/* Tabs */}
          <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px]">
            <button
              className={`p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] ${
                activeTab === "clusters"
                  ? "text-[#352090] border-b-2 border-[#352090]"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => handleTabChange("clusters")}
            >
              Clusters ({filteredClustersCount})
            </button>
            <button
              className={`p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] ${
                activeTab === "links"
                  ? "text-[#352090] border-b-2 border-[#352090]"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => handleTabChange("links")}
            >
              Links ({filteredLinksCount})
            </button>
          </div>

          {/* Clusters Table */}
          {activeTab === "clusters" && (
            <div className="bg-white rounded-md border border-gray-200 overflow-x-auto">
              <div className="bg-[#F7F7F7] min-w-full grid grid-cols-5 border-b border-gray-200">
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Cluster Name</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Device Filter</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Country Filter</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Days</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Actions</div>
              </div>

              {clusters.length === 0 && (
                <div className="flex flex-col items-center justify-center py-16">
                  <div className="w-12 h-12 rounded-full bg-[#F8F7FC] flex items-center justify-center mb-4">
                    <Trash2 className="w-6 h-6 text-[#AD66FF]" />
                  </div>
                  <h3 className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                    {searchQuery ? "No matching clusters found" : "No Deleted Clusters"}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {searchQuery ? "Try adjusting your search query" : "Your trash is empty"}
                  </p>
                </div>
              )}

              {clusters.map((cluster) => (
                <div
                  key={cluster.id}
                  className="min-w-full grid grid-cols-5 border-b border-gray-200 relative hover:bg-gray-50 transition-colors"
                  style={{ position: "relative", zIndex: 1 }}
                >
                  <div className="text-gray-800 border-r border-gray-200 flex items-center justify-start min-h-[20px] px-4">
                    <span className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal" title={cluster.name}>
                      {cluster.name}
                    </span> 
                     </div>
                  <div className="text-gray-800 border-r border-gray-200 flex items-center justify-start min-h-[20px] px-4">
                    <span className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal" title={cluster.deviceFilter}>
                      {cluster.deviceFilter}
                    </span>
                  </div>
                  <div className="text-gray-800 border-r border-gray-200 flex items-center justify-start min-h-[20px] px-4">
                    <div className="flex flex-wrap gap-1">
                      {cluster.countryFilter === "ALL" ? (
                        <span className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal">
                          All Countries
                        </span>
                      ) : (
                        cluster.countryFilter
                          .split(",")
                          .slice(0, 3)
                          .map((country, index) => (
                            <span key={index} className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal">
                              {country.trim()}
                            </span>
                          ))
                      )}
                      {cluster.countryFilter !== "ALL" && cluster.countryFilter.split(",").length > 3 && (
                        <span className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal">
                          +{cluster.countryFilter.split(",").length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-gray-800 border-r border-gray-200 flex items-center min-h-[20px] px-4">
                    {cluster.daysLeft != null && (
                      <Tippy
                        content={
                          <div className="text-xs leading-tight">
                            <div><strong>Deleted:</strong> {cluster.dateDeleted}</div>
                            <div><strong>Permanent delete:</strong> {cluster.permanentDeletionDate}</div>
                          </div>
                        }
                        arrow={true}
                        placement="top"
                        delay={[200, 100]}
                      >
                        <span
                          className={`font-['Noto Sans'] text-[14px] leading-[20px] truncate ${
                            cluster.daysLeft <= 5 ? "text-red-600" : "text-gray-800"
                          } cursor-help`}
                        >
                          {cluster.daysLeft} days left
                        </span>
                      </Tippy>
                    )}
                  </div>
                  <div className="p-4 flex items-center justify-start space-x-4 w-full border-r border-gray-200">
                    {/* Restore Button with Tooltip */}
                    <Tippy content="Restore cluster" arrow={true} placement="top" delay={[200, 100]}>
                      <button
                        className="text-gray-500 p-1 rounded-full transition-colors hover:text-red-600 hover:bg-red-50"
                        onClick={() => handleRestore(cluster.id, "cluster")}
                        disabled={isItemRestoring(cluster.id) || isItemDeleting(cluster.id)}
                      >
                        {isItemRestoring(cluster.id) ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <img src="/Restore.svg" alt="Restore" className="w-5 h-5" />
                        )}
                      </button>
                    </Tippy>

                    {/* Delete Button with Tooltip */}
                    <Tippy content="Delete cluster" arrow={true} placement="top" delay={[200, 100]}>
                      <button
                        className="flex items-center text-red-500 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed p-1 rounded-full transition-colors"
                        onClick={() => handleDeleteClick(cluster.id, "cluster", cluster.name)}
                        disabled={isItemRestoring(cluster.id) || isItemDeleting(cluster.id)}
                      >
                        {isItemDeleting(cluster.id) ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <img src="/Trash_1.svg" alt="Delete" className="w-5 h-5" />
                        )}
                      </button>
                    </Tippy>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Links Table */}
          {activeTab === "links" && (
            <div className="bg-white rounded-md border border-gray-200 overflow-x-auto">
              <div className="bg-[#F7F7F7] min-w-full grid grid-cols-5 border-b border-gray-200">
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Cluster Name</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">URL</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Status</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Days</div>
                <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">Actions</div>
              </div>

              {links.length === 0 && (
                <div className="flex flex-col items-center justify-center py-16">
                  <div className="w-12 h-12 rounded-full bg-[#F8F7FC] flex items-center justify-center mb-4">
                    <Trash2 className="w-6 h-6 text-[#AD66FF]" />
                  </div>
                  <h3 className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                    {searchQuery ? "No matching links found" : "No Deleted Links"}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {searchQuery ? "Try adjusting your search query" : "Your trash is empty"}
                  </p>
                </div>
              )}

              {links.map((link) => (
                <div
                  key={link.id}
                  className="min-w-full grid grid-cols-5 border-b border-gray-200 relative hover:bg-gray-50 transition-colors"
                  style={{ position: "relative", zIndex: 1 }}
                >
                  <div className="text-gray-800 border-r border-gray-200 flex items-center justify-start min-h-[20px] px-4">
                    <span className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal" title={link.clusterName}>
                      {link.clusterName}
                    </span>
                  </div>
                    <div className="p-4 text-gray-800 border-r border-gray-200">
                      <div className="flex items-center gap-2">
                        <span className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal" title={link.url}>
                          {link.url}
                          {}
                        </span>
                        {link.is_pillar && (
                          <span
                            className="inline-flex items-center justify-center bg-[#AD66FF] text-white rounded-[24px] whitespace-nowrap"
                            style={{
                              minWidth: '90px',
                              height: '28px',
                              padding: '4px 16px',
                              font: 'Noto Sans',
                              fontSize: '14px',
                              fontWeight: 400,
                              lineHeight: '20px',
                              letterSpacing: '0%',
                              opacity: 1,
                              marginLeft: '8px',
                            }}
                          >
                            Pillar Page
                          </span>
                        )}
                      </div>
                    </div>
                  <div className="p-4 text-gray-800 border-r border-gray-200">{renderStatusIcon(link.status)}</div>
                  <div className="text-gray-800 border-r border-gray-200 flex items-center min-h-[20px] px-4">
                    {link.daysLeft != null && (
                      <Tippy
                        content={
                          <div className="text-xs leading-tight">
                            <div><strong>Deleted:</strong> {link.dateDeleted}</div>
                            <div><strong>Permanent delete:</strong> {link.permanentDeletionDate}</div>
                          </div>
                        }
                        arrow={true}
                        placement="top"
                        delay={[200, 100]}
                      >
                        <span
                          className={`font-['Noto Sans'] text-[14px] leading-[20px] truncate ${
                            link.daysLeft <= 5 ? "text-red-600" : "text-gray-800"
                          } cursor-help`}
                        >
                          {link.daysLeft} days left
                        </span>
                      </Tippy>
                    )}
                  </div>
                  <div className="p-4 flex items-center justify-start space-x-4 w-full border-r border-gray-200">
                    <Tippy content="Restore cluster" arrow={true} placement="top" delay={[200, 100]}>
                      <button
                        className="text-gray-500 p-1 rounded-full transition-colors hover:text-red-600 hover:bg-red-50"
                        onClick={() => handleRestore(link.id, "link")}
                        disabled={isItemRestoring(link.id) || isItemDeleting(link.id)}
                      >
                        {isItemRestoring(link.id) ? (
                          <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                        ) : (
                          <img src="/Restore.svg" alt="Restore" className="w-5 h-5" />
                        )}
                      </button>
                    </Tippy>
                    <Tippy content="Delete cluster" arrow={true} placement="top" delay={[200, 100]}>
                      <button
                        className="flex items-center text-red-500 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed p-1 rounded-full transition-colors"
                        onClick={() => handleDeleteClick(link.id, "link", link.url)}
                        disabled={isItemRestoring(link.id) || isItemDeleting(link.id)}
                      >
                        {isItemDeleting(link.id) ? (
                          <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                        ) : (
                          <img src="/Trash_1.svg" alt="Delete" className="w-5 h-5" />
                        )}
                      </button>
                    </Tippy>
                  </div>
                </div>
              ))}
            </div>
          )}
        </main>
      </div>

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => dispatch(closeDeleteModal())}
        onConfirm={handleDeleteConfirm}
        title="Confirm Permanent Deletion"
        message={`Are you sure you want to permanently delete this ${itemToDelete?.type}? This action cannot be undone.`}
        isDeleting={itemToDelete ? isItemDeleting(itemToDelete.id) : false}
        confirmText="Delete Permanently"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
      />
    </div>
  )
}

export default Trash
