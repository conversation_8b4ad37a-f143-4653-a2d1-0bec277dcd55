import { apiSlice } from "./apiSlice"

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE || "http://localhost:8000"

export const authApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Signup
    signup: builder.mutation({
      query: (userData) => ({
        url: "/auth/signup",
        method: "POST",
        body: {
          full_name: userData.fullName,
          email: userData.email,
          phone_number: userData.phoneNumber,
          password: userData.password,
        },
        baseUrl: USER_SERVICE_URL,
      }),
      transformResponse: (response) => {
        // Store onboarding state if provided
        if (response.onboarding_state) {
          localStorage.setItem("onboarding_state", JSON.stringify(response.onboarding_state))
        }
        return response
      },
    }),

    // Login
    login: builder.mutation({
      query: (credentials) => ({
        url: "/auth/login",
        method: "POST",
        body: {
          email: credentials.email,
          password: credentials.password,
          remember_me: credentials.rememberMe || false,
        },
        baseUrl: USER_SERVICE_URL,
      }),
      async onQueryStarted(credentials, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          console.log("Login successful:", data)

          // Store user data in localStorage if remember me is checked
          if (credentials.rememberMe) {
            localStorage.setItem(
              "userData",
              JSON.stringify({
                email: data.email,
                role: data.role,
              }),
            )
            console.log("User data stored in localStorage")
          }
        } catch (error) {
          console.error("Login failed:", error)
        }
      },
    }),

    // Check Onboarding Status
    checkOnboarding: builder.query({
      query: () => ({
        url: "/auth/check-onboarding",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: ["Onboarding"],
    }),

    // Verify Email
    verifyEmail: builder.mutation({
      query: (token) => ({
        url: `/auth/verify`,
        method: "GET",
        params: { token },
        baseUrl: USER_SERVICE_URL,
      }),
      transformResponse: (response) => {
        // Store workspace ID and onboarding state if provided
        if (response.workspace_id) {
          localStorage.setItem("currentWorkspaceId", response.workspace_id)
        }
        if (response.onboarding_state) {
          localStorage.setItem("onboarding_state", JSON.stringify(response.onboarding_state))
        }
        return response
      },
    }),

    // Forgot Password - Fixed to send just email string
    forgotPassword: builder.mutation({
      query: (email) => ({
        url: "/auth/forgot-password",
        method: "POST",
        body: { email }, // Send as object with email property
        baseUrl: USER_SERVICE_URL,
      }),
    }),

    // Reset Password
    resetPassword: builder.mutation({
      query: ({ token, password }) => ({
        url: "/auth/reset-password",
        method: "POST",
        body: {
          token,
          new_password: password,
        },
        baseUrl: USER_SERVICE_URL,
      }),
    }),

    // Get User Profile
    getUserProfile: builder.query({
      query: () => ({
        url: "/auth/user-profile",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: ["User"],
    }),

    // Validate Session
    validateSession: builder.query({
      query: () => ({
        url: "/session/validate",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: ["User"],
    }),

    // Logout
    logout: builder.mutation({
      query: () => ({
        url: "/auth/logout",
        method: "POST",
        baseUrl: USER_SERVICE_URL,
      }),
      async onQueryStarted(_, { dispatch }) {
        // Clear localStorage on logout
        localStorage.removeItem("userData")
        localStorage.removeItem("onboarding_state")
        localStorage.removeItem("currentWorkspaceId")
      },
      invalidatesTags: ["User", "Onboarding"],
    }),
  }),
})

export const {
  useSignupMutation,
  useLoginMutation,
  useCheckOnboardingQuery,
  useVerifyEmailMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useGetUserProfileQuery,
  useValidateSessionQuery,
  useLogoutMutation,
} = authApi
