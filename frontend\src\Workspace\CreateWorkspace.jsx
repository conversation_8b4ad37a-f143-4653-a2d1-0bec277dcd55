import { useState, useMemo } from "react"
import { useNavigate } from "react-router-dom"
import { useDispatch } from "react-redux"
import Navbar from "../components/navbar"
import CreateWorkspaceModal from "./create-workspace-modal"
import { useWorkspace } from "../hooks/useWorkspace"
import { useCreateWorkspaceMutation } from "../store/api/workspaceApi"
import { useGetWorkspaceMembersBatchQuery } from "../store/api/teamApi"
import { addNotification } from "../store/slices/uiSlice"
import { setCurrentWorkspace } from "../store/slices/workspaceSlice"
import userAvatar from "/image.svg"
import React from "react"

const CreateWorkspace = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [setupMessage, setSetupMessage] = useState("")
  const [isSwitching, setIsSwitching] = useState(false)
  const navigate = useNavigate()
  const dispatch = useDispatch()

  // Use the workspace hook for consistent state management
  const { 
    workspaces, 
    loading: workspacesLoading,
    switchWorkspace,
    updateLastAccessedWorkspace 
  } = useWorkspace()



  // Create workspace mutation
  const [createWorkspace, { isLoading: isCreating }] = useCreateWorkspaceMutation()

  // Get workspace IDs for batch query
  const workspaceIds = useMemo(() => workspaces.map(ws => ws.id || ws._id), [workspaces])
  
  // Use the batch query endpoint
  const { data: workspaceMembersData, isLoading: membersLoading } = useGetWorkspaceMembersBatchQuery(
    { workspace_ids: workspaceIds },
    { 
      skip: workspaceIds.length === 0 
    }
  )

  // Memoize the processed members data
  const workspaceMembers = useMemo(() => {
    if (!workspaceMembersData?.data) return {}
    return workspaceMembersData.data
  }, [workspaceMembersData])

  const handleCreateWorkspace = () => {
    setIsModalOpen(true)
  }

  const handleModalSubmit = async (workspaceName) => {
    try {
      setIsProcessing(true)
      setSetupMessage("Creating your new workspace...")
      
      // Create new workspace using the API
      const result = await createWorkspace({ name: workspaceName }).unwrap()
      const workspaceId = result.id || result._id
      
      dispatch(
        addNotification({
          type: "success",
          title: "Success",
          message: "Workspace created successfully! Setting up workspace...",
        })
      )

      try {
        // First update the session so backend knows about the new workspace
        setSetupMessage("Updating workspace session...")
        console.log("🔄 Updating session for new workspace:", workspaceId)
        const sessionResult = await updateLastAccessedWorkspace(workspaceId).unwrap()
        
        // Add a 5-second delay with loading message
        setSetupMessage("Please wait while we set up everything for you...")
        await new Promise(resolve => setTimeout(resolve, 5000))
        
        // Update Redux state directly
        console.log("🔄 Updating Redux state with new workspace:", workspaceId)
        dispatch(setCurrentWorkspace(workspaceId))

        // Navigate to google auth (first onboarding step)
        console.log("✅ Navigating to onboarding for workspace:", workspaceId)
        navigate(`/google_auth/${workspaceId}`, {
          state: {
            current_step: "google_auth",
            workspace_id: workspaceId
          }
        })
      } catch (error) {
        console.error("❌ Error during workspace setup:", error)
        dispatch(
          addNotification({
            type: "error",
            title: "Setup Error",
            message: "Please try accessing the workspace again after setup.",
          })
        )
      }
      
      return true
    } catch (error) {
      console.error("Error creating workspace:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Error",
          message: error.data?.detail || "Failed to create workspace",
        })
      )
      return false
    } finally {
      setIsProcessing(false)
      setSetupMessage("")
    }
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
  }

  const handleSelectWorkspace = async (workspaceId) => {
    if (isSwitching) {
      return // Prevent multiple clicks
    }

    try {
      setIsSwitching(true)
      setSetupMessage("Please wait while we prepare your workspace...")

      // Add initial delay for UX
      await new Promise(resolve => setTimeout(resolve, 2000))

      console.log("🔄 WORKSPACE SELECTION: Starting workspace switch", {
        workspaceId,
        currentPath: window.location.pathname,
        timestamp: new Date().toISOString()
      })

      await switchWorkspace(workspaceId)

      console.log("✅ WORKSPACE SELECTION: Workspace switch completed", {
        workspaceId,
        timestamp: new Date().toISOString()
      })

      // switchWorkspace already handles navigation to the onboarding check route
      // No additional navigation needed here
      
    } catch (error) {
      console.error("❌ Error selecting workspace:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Switch Failed",
          message: error.message || "Failed to switch workspace",
        })
      )
    } finally {
      setIsSwitching(false)
      setSetupMessage("")
    }
  }

  // Get workspace display name (consistent with dropdown logic)
  const getWorkspaceDisplayName = (workspace) => {
    const workspaceId = workspace.id || workspace._id

    // If workspace has a name and it's not empty, use it
    if (workspace.name && workspace.name.trim()) {
      return workspace.name
    }

    // Otherwise, show truncated ID
    return workspaceId.length > 12
      ? `${workspaceId.substring(0, 8)}...${workspaceId.substring(workspaceId.length - 4)}`
      : workspaceId
  }

  // Get domain display info
  const getWorkspaceDomainInfo = (workspace) => {
    if (workspace.domain_info && workspace.domain_info.siteUrl) {
      return workspace.domain_info.siteUrl
    }
    
    if (workspace.has_domain === false) {
      return "No domain set"
    }
    
    return "Loading domain..."
  }

  // Update loading state to include all loading states
  const isLoading = workspacesLoading || isCreating || isProcessing || isSwitching

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="flex-1 flex items-center justify-center" style={{ minHeight: "calc(100vh - 100px)" }}>
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#352090] mx-auto"></div>
            <p className="mt-4 text-gray-600">
              {setupMessage || (isCreating ? "Creating workspace..." : "Loading workspaces...")}
            </p>
            {(isProcessing || isSwitching) && (
              <p className="mt-2 text-sm text-gray-500">
                This may take a few moments. Please don't close this window.
              </p>
            )}
          </div>
        </main>
      </div>
    )
  }

  // No workspaces state - show create button
  if (workspaces.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="flex-1 flex items-center justify-center" style={{ minHeight: "calc(100vh - 100px)" }}>
          <div className="text-center">
            <div className="flex items-center justify-center">
              <button
                onClick={handleCreateWorkspace}
                className={`
                  flex items-center gap-[9px] cursor-pointer
                  transition-opacity duration-200
                  focus:outline-none focus:ring-2 focus:ring-[#352090] focus:ring-offset-2 rounded-lg
                  hover:opacity-80
                `}
              >
                <svg
                  width="45"
                  height="45"
                  viewBox="0 0 45 45"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="flex-shrink-0"
                >
                  <rect width="45" height="45" rx="14" fill="#E4EEFF" />
                  <path
                    d="M22.5 17.5417V27.4584M17.5417 22.5001H27.4584"
                    stroke="#352090"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span
                  className="text-[#352090] text-[14px] leading-[100%] tracking-[0%] whitespace-nowrap"
                  style={{ fontFamily: "Poppins, sans-serif", fontWeight: 400 }}
                >
                  Create New Workspace
                </span>
              </button>
            </div>
          </div>
        </main>
        <CreateWorkspaceModal isOpen={isModalOpen} onClose={handleModalClose} onSubmit={handleModalSubmit} />
      </div>
    )
  }

  // Workspaces exist - show workspace selection interface
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="flex-1 px-6 py-8" style={{ minHeight: "calc(100vh - 100px)" }}>
        <div className="max-w-4xl mx-auto">
          {/* Header with Create New Workspace button */}
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-lg font-medium text-gray-900">Select your workspace to continue:</h1>
            <button
              onClick={handleCreateWorkspace}
              className="flex items-center gap-[9px] cursor-pointer transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-[#352090] focus:ring-offset-2 rounded-lg hover:opacity-80"
            >
              <svg
                width="45"
                height="45"
                viewBox="0 0 45 45"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="flex-shrink-0"
              >
                <rect width="45" height="45" rx="14" fill="#E4EEFF" />
                <path
                  d="M22.5 17.5417V27.4584M17.5417 22.5001H27.4584"
                  stroke="#352090"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span
                className="text-[#352090] text-[14px] leading-[100%] tracking-[0%] whitespace-nowrap"
                style={{ fontFamily: "Poppins, sans-serif", fontWeight: 400 }}
              >
                Create New Workspace
              </span>
            </button>
          </div>

          {/* Workspace Cards */}
          <div className="space-y-4">
            {workspaces.map((workspace) => {
              const displayName = getWorkspaceDisplayName(workspace)
              const domainInfo = getWorkspaceDomainInfo(workspace)
              const workspaceId = workspace.id || workspace._id
              const members = workspaceMembers[workspaceId] || []

              return (
                <div
                  key={workspaceId}
                  className="bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 w-full min-w-[400px] max-w-2xl mx-auto"
                >
                  <div className="flex items-center justify-between">
                    {/* Workspace Info */}
                    <div className="flex flex-col gap-2">
                      <h3 className="text-lg font-medium text-gray-900">{displayName}</h3>
                      <div className="flex flex-col gap-2">
                        <span className="text-sm text-gray-500">{domainInfo}</span>
                        <div className="flex items-center gap-2">
                          <div className="flex -space-x-2">
                            {members.slice(0, 5).map((member, index) => (
                              <img
                                key={member.email}
                                src={userAvatar}
                                alt={member.full_name || member.email}
                                className="w-6 h-6 rounded-full border-2 border-white"
                                style={{ zIndex: 5 - index }}
                              />
                            ))}
                            {members.length > 5 && (
                              <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs text-gray-600" style={{ zIndex: 0 }}>
                                +{members.length - 5}
                              </div>
                            )}
                          </div>
                          <span className="text-sm text-gray-500">
                            {members.length} member{members.length !== 1 ? "s" : ""}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Select Button */}
                    <button
                      onClick={() => handleSelectWorkspace(workspaceId)}
                      className="px-6 py-2 bg-[#352090] hover:bg-[#2a1a70] text-white font-medium text-sm rounded-xl transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#352090] focus:ring-offset-2"
                    >
                      Select
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </main>

      <CreateWorkspaceModal isOpen={isModalOpen} onClose={handleModalClose} onSubmit={handleModalSubmit} />
    </div>
  )
}

export default CreateWorkspace
