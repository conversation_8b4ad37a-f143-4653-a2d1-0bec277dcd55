import MetricCard from "./metric-card"

const ImpressionsCard = ({
  impressions,
  comparisonImpressions,
  onToggle,
  isChecked,
  title = "Total Impressions",
  disabled,
  dateRange,
}) => {
  return (
    <MetricCard
      title={title}
      value={impressions}
      comparisonValue={comparisonImpressions}
      color="purple"
      onToggle={onToggle}
      isChecked={isChecked}
      disabled={disabled}
      dateRange={dateRange}
    />
  )
}

export default ImpressionsCard
