import {
  Search,
  FileText,
  AlertCircle,
  Clock,
  Loader,
} from "lucide-react"
import { useNavigate, useParams, useLocation } from "react-router-dom"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useWorkspace } from "../hooks/useWorkspace"
import { useLinks } from "../hooks/useLinks"
import {
  setSearchQuery,
  openAddModal,
  closeAddModal,
  openDeleteModal,
  closeDeleteModal,
} from "../store/slices/linkSlice"
import { addNotification } from "../store/slices/uiSlice"
import Navbar from "../components/navbar"
import Sidebar from "../components/sidebar"
import AddLinksModal from "../components/add-links-modal"
import DeleteConfirmationModal from "../components/delete-confirmation-modal"
import LinkPerformanceSparkline from "../components/LinkPerformanceSparkline"
import DateRangeSelector from "../components/DateRangeSelector"

const ClusterLinksPage = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { workspaceId: urlWorkspaceId, clusterId } = useParams()
  const { state } = useLocation()
  const { currentWorkspace } = useWorkspace()

  // Initialize with 3-month date range
  const [selectedDateRange, setSelectedDateRange] = useState(() => {
    const now = new Date();
    // Set end date to 3 days ago
    const endDate = new Date(now.getTime() - (3 * 24 * 60 * 60 * 1000));
    const threeMonthsAgo = new Date(endDate.getTime() - (90 * 24 * 60 * 60 * 1000));
    return {
      startDate: threeMonthsAgo.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  });

  // Track selected filter
  const [selectedFilter, setSelectedFilter] = useState("3m");
  
  // Add loading state for date range changes
  const [isDateRangeLoading, setIsDateRangeLoading] = useState(false);

  // Use currentWorkspace from Redux, fallback to URL parameter
  const activeWorkspaceId = currentWorkspace || urlWorkspaceId
  const clusterName = state?.clusterName ?? "Cluster Links"

  // Redux state
  const { searchQuery, ui } = useSelector((state) => state.link)
  const { isAddModalOpen, isDeleteModalOpen, linkToDelete } = ui

  // Custom hook for links data and operations
  const {
    links,
    isLoading,
    isAdding,
    isDeleting,
    error,
    handleAddLinks,
    handleRefreshLink,
    handleDeleteLink,
    totalLinks,
    filteredCount,
    processingCount,
    isLinkProcessing,
    isLinkRefreshing,
  } = useLinks(clusterId, activeWorkspaceId)

  console.log("ClusterLinksPage - links:", links)

  // 🔑 NEW: Check URL synchronization with Redux state
  useEffect(() => {
    if (activeWorkspaceId && urlWorkspaceId && urlWorkspaceId !== activeWorkspaceId) {
      console.log("🔄 URL out of sync with Redux state, updating URL...")
      console.log("  - URL workspace:", urlWorkspaceId)
      console.log("  - Redux workspace:", activeWorkspaceId)
      
      // Update URL to match Redux state
      navigate(`/workspace/${activeWorkspaceId}/clusters/${clusterId}/links`, { replace: true })
    }
  }, [activeWorkspaceId, urlWorkspaceId, clusterId, navigate])

  // 🔑 KEY FIX: Redirect if no workspace is set
  useEffect(() => {
    if (!activeWorkspaceId) {
      console.log("No active workspace, redirecting to login")
      navigate("/login")
      return
    }
  }, [activeWorkspaceId, navigate])

  const handleSearchChange = (e) => {
    dispatch(setSearchQuery(e.target.value))
  }

  const handleAddLinksClick = () => {
    dispatch(openAddModal())
  }

  const handleCloseModal = () => {
    dispatch(closeAddModal())
  }

  const handleSubmitLinks = async (linkObjects) => {
    const success = await handleAddLinks(linkObjects)
    return success
  }

  const handleDeleteClick = (link) => {
    console.log("handleDeleteClick - link:", link)
    dispatch(openDeleteModal(link))
  }

  const handleDeleteConfirm = async () => {
    if (!linkToDelete) return
    const linkId = linkToDelete.id || linkToDelete._id
    console.log("handleDeleteConfirm - linkToDelete:", linkToDelete, "linkId:", linkId)
    const success = await handleDeleteLink(linkId)
    if (success) {
      dispatch(closeDeleteModal())
    }
  }

  const handleViewPerformance = (link) => {
    const linkId = link.id || link._id
    navigate(`/clusters/${linkId}/performance`, {
      state: {
        linkUrl: link.url,
        clusterId: clusterId,
      },
    })
  }

  const handleViewClusterPerformance = () => {
    navigate(`/cluster_performance/${clusterId}`)
  }

  const renderStatusIcon = (link) => {
    const linkId = link.id || link._id
    const isProcessing = isLinkProcessing(linkId)
    const isRefreshing = isLinkRefreshing(linkId)

    if (isProcessing || isRefreshing || link.status === "in_progress" || link.status === "pending") {
      return (
        <div className="text-amber-600" title="Processing link data...">
          <Loader className="w-5 h-5 animate-spin" />
        </div>
      )
    }

    if (link.status === "completed") {
      return (
        <div className="text-green-600" title="Link data ready">
          <img src="/success.svg" alt="Success" className="w-5 h-5" />
        </div>
      )
    }

    if (link.status === "error" || link.status === "failed") {
      return (
        <div className="text-red-600" title="Link processing error">
          <AlertCircle className="w-5 h-5" />
        </div>
      )
    }

    return (
      <div className="text-gray-500" title="Pending link processing">
        <Clock className="w-5 h-5" />
      </div>
    )
  }

  const renderPerformance = (link) => {
    const linkId = link.id || link._id
    const isProcessing = isLinkProcessing(linkId)
    const isRefreshing = isLinkRefreshing(linkId)

    if (isProcessing || isRefreshing || link.status === "in_progress" || link.status === "pending") {
      return (
        <div className="flex flex-col items-center justify-center h-full min-h-[60px] text-amber-600">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-600 mb-1"></div>
          <span className="text-xs">Processing Link Data...</span>
        </div>
      )
    }

    if (link.status === "error" || link.status === "failed") {
      return (
        <div className="flex flex-col items-center justify-center h-full min-h-[60px] text-red-500">
          <AlertCircle className="w-6 h-6 mb-1" />
          <span className="text-xs">Link Error</span>
        </div>
      )
    }

    if (link.status === "completed") {
      return (
        <div className="flex items-center justify-center h-full min-h-[60px] relative">
          <LinkPerformanceSparkline linkId={linkId} clusterId={clusterId} />
        </div>
      )
    }

    // Default case for pending
    return (
      <div className="flex flex-col items-center justify-center h-full min-h-[60px] text-gray-500">
        <Clock className="w-6 h-6 mb-1" />
        <span className="text-xs">Waiting for processing...</span>
      </div>
    )
  }

  // Show loading while fetching links
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <div className="fixed top-0 left-0 right-0 h-16 bg-white z-30">
          <Navbar />
        </div>
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090]"></div>
            <p className="mt-4 text-gray-600">Loading links...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show error if there's an issue
  if (error && !links.length) {
    return (
      <div className="flex flex-col min-h-screen font-noto">
        <Navbar />
        <div className="flex-1 p-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="p-4 bg-red-50 rounded-lg max-w-md">
              <div className="flex items-center gap-2 text-red-600 mb-2">
                <AlertCircle className="w-5 h-5" />
                <h3 className="font-medium">Error</h3>
              </div>
              <p className="text-red-600 mb-4">{error.data?.detail || error.message || "Failed to load links"}</p>
              <button
                onClick={() => window.location.reload()}
                className="text-[#352090] hover:text-[#2a1a70] font-medium"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />

      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block fixed top-16 bottom-0 left-0 bg-white z-20">
          <Sidebar activePage="overview" />
        </div>

        <main className="flex-1 p-6 pt-[100px] mt-6 md:ml-[200px] overflow-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <div>
              <h1 className="text-xl font-semibold text-gray-800 mb-2">
                <span
                  className="cursor-pointer hover:text-[#352090] transition-colors duration-300"
                  onClick={() => navigate(`/workspace/${activeWorkspaceId}/clusters`)}
                >
                  {clusterName}
                </span>
                <span className="text-gray-500"> / </span>
                Links
              </h1>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span>
                  ({filteredCount} of {totalLinks} links)
                </span>
                {processingCount > 0 && (
                  <span className="flex items-center gap-1 text-amber-600">
                    <Clock className="w-4 h-4 animate-pulse" />
                    {processingCount} processing
                  </span>
                )}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3 mt-4 md:mt-0">
              <DateRangeSelector 
                showCompare={false}
                initialFilter={selectedFilter}
                onApply={(dateRange) => {
                  console.log("Date range selected:", dateRange);
                  // Set loading state to true when filter changes
                  setIsDateRangeLoading(true);
                  
                  if (dateRange.type === "filter") {
                    const now = new Date();
                    // Always set end date to 3 days ago
                    const endDate = new Date(now.getTime() - (3 * 24 * 60 * 60 * 1000));
                    let start = new Date();

                    if (dateRange.mode === "custom") {
                      // For custom dates, if user's end date is less than 3 days ago, use it
                      // otherwise use 3 days ago as end date
                      const userEndDate = new Date(dateRange.endDate);
                      const actualEndDate = userEndDate < endDate ? userEndDate : endDate;
                      
                      setSelectedDateRange({
                        startDate: dateRange.startDate.toISOString().split('T')[0],
                        endDate: actualEndDate.toISOString().split('T')[0]
                      });
                      setSelectedFilter("custom");
                    } else {
                      // Handle preset ranges
                      switch(dateRange.preset) {
                        case "28d":
                          start = new Date(endDate.getTime() - (28 * 24 * 60 * 60 * 1000));
                          setSelectedFilter("28d");
                          break;
                        case "3m":
                          start = new Date(endDate.getTime() - (90 * 24 * 60 * 60 * 1000));
                          setSelectedFilter("3m");
                          break;
                        case "6m":
                          start = new Date(endDate.getTime() - (180 * 24 * 60 * 60 * 1000));
                          setSelectedFilter("6m");
                          break;
                        case "12m":
                          start = new Date(endDate.getTime() - (365 * 24 * 60 * 60 * 1000));
                          setSelectedFilter("12m");
                          break;
                        case "16m":
                          start = new Date(endDate.getTime() - (480 * 24 * 60 * 60 * 1000));
                          setSelectedFilter("16m");
                          break;
                        default:
                          start = new Date(endDate.getTime() - (90 * 24 * 60 * 60 * 1000));
                          setSelectedFilter("3m");
                      }
                      
                      setSelectedDateRange({
                        startDate: start.toISOString().split('T')[0],
                        endDate: endDate.toISOString().split('T')[0]
                      });
                    }
                    
                    // Reset loading state after a short delay to ensure data has time to load
                    setTimeout(() => {
                      setIsDateRangeLoading(false);
                    }, 1000);
                  }
                }}
              />
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search links..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-[8px] w-full sm:w-[250px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={handleViewClusterPerformance}
                  className="bg-[#34A853] text-white px-4 py-2 rounded-[8px] hover:bg-green-600 transition-colors duration-300"
                >
                  View Performance
                </button>
                <button
                  onClick={handleAddLinksClick}
                  disabled={isAdding}
                  className="bg-[#352090] text-white px-4 py-2 rounded-[8px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {isAdding ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Adding...
                    </>
                  ) : (
                    "Add Links"
                  )}
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-4 p-4 bg-red-50 rounded-lg flex items-start gap-2">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <p className="text-red-600">{error.data?.detail || error.message}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="text-[#352090] hover:text-[#2a1a70] font-medium mt-2"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          <div className="bg-white rounded-md border border-gray-200 overflow-x-auto">
            {/* Updated header - now with Clicks and Impressions columns */}
            <div className="bg-[#F7F7F7] min-w-full grid grid-cols-[minmax(0,3fr)_minmax(0,0.5fr)_minmax(0,0.5fr)_minmax(225px,1fr)] border-b border-gray-200">
              <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">
                URL
              </div>
              <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">
                Clicks
              </div>
              <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">
                Impressions
              </div>
              <div className="p-4 font-['Noto Sans'] font-bold text-[#352090] text-[14px] leading-[20px] border-r border-[#DCDCDC] bg-[#F7F7F7] border-t">
                Actions
              </div>
            </div>

            {/* Links Content */}
            {links.length === 0 && (
              <div className="flex flex-col items-center justify-center py-16">
                <div className="w-12 h-12 rounded-full bg-[#F8F7FC] flex items-center justify-center mb-4">
                  <FileText className="w-6 h-6 text-[#AD66FF]" />
                </div>
                <h3 className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                  {searchQuery ? "No matching links found" : "No Links Found"}
                </h3>
                <p className="text-lg font-['Noto Sans'] text-gray-800 mb-1">
                  {searchQuery ? "Try adjusting your search query" : "Add links to this cluster to start tracking"}
                </p>
                {!searchQuery && (
                  <button
                    onClick={handleAddLinksClick}
                    className="mt-4 bg-[#352090] text-white px-4 py-2 rounded-[8px] hover:bg-[#2a1a70] transition-colors"
                  >
                    Add Your First Link
                  </button>
                )}
              </div>
            )}

            {/* Display Links */}
            {[...links]
              .sort((a, b) => {
                if (a.is_pillar) return -1;
                if (b.is_pillar) return 1;
                return b._id?.localeCompare(a._id);
              })
              .map((link) => {
                const linkId = link.id || link._id;
                return (
                  <div
                    key={linkId}
                    className="min-w-full grid grid-cols-[minmax(0,3fr)_minmax(0,0.5fr)_minmax(0,0.5fr)_minmax(225px,1fr)] border-b border-gray-200 relative hover:bg-gray-50 transition-colors"
                    style={{ position: "relative", zIndex: 1 }}
                  >
                    {/* URL Column */}
                    <div className="p-4 text-gray-800 border-r border-gray-200">
                      <div className="flex items-center gap-2">
                        <span className="truncate block font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal " title={link.url}>
                          {link.url}
                        </span>
                        {link.is_pillar && (
                          <span
                            className="inline-flex items-center justify-center bg-[#AD66FF] text-white rounded-[24px] whitespace-nowrap"
                            style={{
                              minWidth: '90px',
                              height: '28px',
                              padding: '4px 16px',
                              fontFamily: 'Noto Sans',
                              fontSize: '14px',
                              fontWeight: 400,
                              lineHeight: '20px',
                              letterSpacing: '0%',
                              opacity: 1,
                              marginLeft: '8px',
                            }}
                          >
                            Pillar page
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Clicks Column */}
                    <div className="text-gray-800 border-r border-gray-200 flex items-center justify-start min-h-[20px] px-4">
                      <LinkPerformanceSparkline
                        linkId={linkId}
                        clusterId={clusterId}
                        metricType="clicks"
                        startDate={selectedDateRange.startDate}
                        endDate={selectedDateRange.endDate}
                        isLoading={isDateRangeLoading}
                      />
                    </div>

                    {/* Impressions Column */}
                    <div className="text-gray-800 border-r border-gray-200 flex items-center justify-start min-h-[20px] px-4">
                      <LinkPerformanceSparkline
                        linkId={linkId}
                        clusterId={clusterId}
                        metricType="impressions"
                        startDate={selectedDateRange.startDate}
                        endDate={selectedDateRange.endDate}
                        isLoading={isDateRangeLoading}
                      />
                    </div>

                    {/* Actions Column */}
                    <div className="p-4 flex items-center justify-start space-x-4 w-full border-r border-gray-200">

                      {/* View Performance Button */}
                      <button
                        className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
                        title="View Performance"
                        onClick={() => handleViewPerformance(link)}
                      >
                        <img src="/performance_chart.svg" alt="View Performance" className="w-5 h-5" />
                      </button>

                      {/* Refresh Button */}
                      <button
                        className={`text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors ${
                          isLinkRefreshing(linkId) ? 'animate-spin text-yellow-500' : ''
                        }`}
                        title="Refresh"
                        onClick={() => handleRefreshLink(link)}
                        disabled={isLinkRefreshing(linkId)}
                      >
                        <img src="/recall.svg" alt="View Performance" className="w-5 h-5" />
                      </button>

                      {/* Delete Button */}
                      <button
                        className="text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        title="Delete"
                        onClick={() => handleDeleteClick(link)}
                        disabled={isDeleting}
                      >
                        {isDeleting && linkToDelete?.id === linkId ? (
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
                        ) : (
                          <img src="/Trash_1.svg" alt="Refresh" className="w-5 h-5" />
                        )}
                      </button>
                        {/* Status Icon */}
                      <div className="flex items-center justify-center">{renderStatusIcon(link)}</div>
                    </div>
                  </div>
                );
              })}
          </div>
        </main>
      </div>

      <AddLinksModal
        isOpen={isAddModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmitLinks}
        clusterName={clusterName}
        clusterDomain="sc-domain:apimio.com" // You might want to get this from props or state
        isSubmitting={isAdding}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => dispatch(closeDeleteModal())}
        onConfirm={handleDeleteConfirm}
        title="Delete Link"
        message="This link will be moved to trash. You have 30 days to recover your data."
        isDeleting={isDeleting}
      />
    </div>
  )
}

export default ClusterLinksPage
