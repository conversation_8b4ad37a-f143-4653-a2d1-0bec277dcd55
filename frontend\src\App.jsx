import { Routes, Route } from "react-router-dom"

// Import your existing components
import Signup from "./User-authentication/signup-form"
import Login from "./User-authentication/login-form"
import VerifyEmail from "./User-authentication/verify-email"
import ForgotPassword from "./User-authentication/forgot-password"
import ResetPassword from "./User-authentication/reset-password"
import TeamInvitation from "./team_invitation/team-invitation"
import GoogleAuth from "./Google_authentication/google_auth"
import AdminPage from "./User-authentication/Guard"
import SiteSelection from "./Cluster_pages/site-selection-page"
import ClustersPage from "./Cluster_pages/clusters-page"
import ClusterLinksPage from "./Cluster_pages/cluster-links-page"
import ClusterPerformance from "./Performance_pages/cluster-performance"
import LinkPerformance from "./Performance_pages/link-performance"
import OnboardingCheck from "./components/onboarding-check"
import WorkspaceLayout from "./components/workspace-layout"
import Trash from "./Trash/trash"
import InvitedUserInfo from "./team_invitation/invited-user-info"
import CreateWorkspace from "./Workspace/CreateWorkspace"
import Settings from "./Settings/settings"

// Import new components
import NotificationContainer from "./components/common/NotificationContainer"
import SessionRestoration from "./components/SessionRestoration"

// Import context providers (keeping for backward compatibility during transition)
import { InvitationProvider } from "./context/InvitationContext"

function App() {
  return (
    <div className="App">
      <SessionRestoration>
        <Routes>
          {/* Root route - shows onboarding check */}
          <Route path="/" element={<OnboardingCheck />} />

          {/* Authentication routes */}
          <Route path="/signup" element={<Signup />} />
          <Route path="/login" element={<Login />} />
          <Route path="/verify-email" element={<VerifyEmail />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
          <Route path="/session_validation" element={<AdminPage />} />

          {/* Workspace-independent routes */}
          <Route path="/invited-user-info" element={<InvitedUserInfo />} />
          <Route path="/create-workspace" element={<CreateWorkspace />} />
          <Route path="/workspace/:workspace_id/invite" element={<TeamInvitation />} />
          <Route path="/google_auth/:workspaceId" element={<GoogleAuth />} />

          {/* Legacy routes that need workspace context but don't use layout */}
          <Route path="/clusters/:linkId/performance" element={<LinkPerformance />} />
          <Route path="/cluster_performance/:clusterId" element={<ClusterPerformance />} />

          {/* Workspace routes with optimized navigation */}
          <Route path="/workspace/:workspaceId" element={<WorkspaceLayout />}>
            {/* Default workspace route - shows onboarding check or clusters */}
            <Route index element={<OnboardingCheck />} />

            {/* Workspace pages with smooth navigation */}
            <Route path="site_selection" element={<SiteSelection />} />
            <Route path="clusters" element={<ClustersPage />} />
            <Route path="clusters/:clusterId/links" element={<ClusterLinksPage />} />
            <Route path="trash" element={<Trash />} />
            <Route path="settings" element={<Settings />} />
          </Route>
        </Routes>

      {/* Global Notification Container */}
      <NotificationContainer />
      </SessionRestoration>
    </div>
  )
}

export default App
