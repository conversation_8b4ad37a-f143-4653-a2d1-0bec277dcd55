import { Routes, Route } from "react-router-dom"

// Import your existing components
import Signup from "./User-authentication/signup-form"
import Login from "./User-authentication/login-form"
import VerifyEmail from "./User-authentication/verify-email"
import ForgotPassword from "./User-authentication/forgot-password"
import ResetPassword from "./User-authentication/reset-password"
import TeamInvitation from "./team_invitation/team-invitation"
import GoogleAuth from "./Google_authentication/google_auth"
import AdminPage from "./User-authentication/Guard"
import SiteSelection from "./Cluster_pages/site-selection-page"
import ClustersPage from "./Cluster_pages/clusters-page"
import ClusterLinksPage from "./Cluster_pages/cluster-links-page"
import ClusterPerformance from "./Performance_pages/cluster-performance"
import LinkPerformance from "./Performance_pages/link-performance"
import OnboardingCheck from "./components/onboarding-check"
import Trash from "./Trash/trash"
import InvitedUserInfo from "./team_invitation/invited-user-info"
import CreateWorkspace from "./Workspace/CreateWorkspace"

// Import new components
import NotificationContainer from "./components/common/NotificationContainer"
import SessionRestoration from "./components/SessionRestoration"
import { useParams } from "react-router-dom"

// Wrapper component to force OnboardingCheck remount when workspace changes
const OnboardingCheckWrapper = () => {
  const { workspaceId } = useParams()
  return <OnboardingCheck key={workspaceId} />
}

// Import context providers (keeping for backward compatibility during transition)
import { InvitationProvider } from "./context/InvitationContext"

function App() {
  return (
    <div className="App">
      <SessionRestoration>
        <Routes>
        <Route path="/" element={<OnboardingCheck key="root" />} />
        <Route
          path="/workspace/:workspaceId"
          element={<OnboardingCheckWrapper />}
        />
        <Route path="/signup" element={<Signup />} />
        <Route path="/login" element={<Login />} />
        <Route
          path="/invited-user-info"
          element={
              <InvitedUserInfo />
          }
        />
        <Route path="/verify-email" element={<VerifyEmail />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password" element={<ResetPassword />} />
        <Route path="/session_validation" element={<AdminPage />} />
        <Route path="/workspace/:workspace_id/invite" element={<TeamInvitation />} />
        <Route path="/google_auth/:workspaceId" element={<GoogleAuth />} />
        <Route path="/workspace/:workspaceId/site_selection" element={<SiteSelection />} />
        <Route path="/workspace/:workspaceId/clusters/:clusterId/links" element={<ClusterLinksPage />} />
        <Route path="/workspace/:workspaceId/clusters" element={<ClustersPage />} />
        <Route path="/clusters/:linkId/performance" element={<LinkPerformance />} />
        <Route path="/workspace/:workspaceId/trash" element={<Trash />} />
        <Route path="/cluster_performance/:clusterId" element={<ClusterPerformance />} />

        {/* // Add more workspace pages */}
        <Route path="/create-workspace" element={<CreateWorkspace />} />
      </Routes>

      {/* Global Notification Container */}
      <NotificationContainer />
      </SessionRestoration>
    </div>
  )
}

export default App
