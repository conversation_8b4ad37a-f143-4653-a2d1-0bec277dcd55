import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"

const ChartSection = ({
  chartData,
  comparisonData,
  chartLoading,
  chartError,
  dataAvailability,
  activeMetrics,
  chartDateRange,
  dateRangeText,
}) => {
  // Helper functions for chart data processing
  const calculateDataGap = (startDate, endDate) => {
    if (!startDate || !endDate) return 1

    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())

    if (diffMonths > 12) return 20
    if (diffMonths > 3) return 15
    return 1
  }

  const aggregateDataByInterval = (data, interval) => {
    if (!data || data.length === 0) return []

    const groupedData = data.reduce((acc, item) => {
      const date = new Date(item.date)
      let intervalKey

      if (interval === "month") {
        intervalKey = `${date.getFullYear()}-${date.getMonth() + 1}`
      } else if (interval === "quarter") {
        const quarter = Math.floor(date.getMonth() / 3) + 1
        intervalKey = `${date.getFullYear()}-Q${quarter}`
      } else if (interval === "week") {
        const monday = new Date(date)
        monday.setDate(date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1))
        intervalKey = monday.toISOString().split("T")[0]
      } else {
        intervalKey = item.date
      }

      if (!acc[intervalKey]) {
        acc[intervalKey] = {
          date: intervalKey,
          clicks: 0,
          impressions: 0,
          ctr: 0,
          position: 0,
          count: 0,
        }
      }

      acc[intervalKey].clicks += item.clicks
      acc[intervalKey].impressions += item.impressions
      acc[intervalKey].ctr += item.ctr
      acc[intervalKey].position += item.position
      acc[intervalKey].count += 1

      return acc
    }, {})

    return Object.values(groupedData).map((item) => ({
      date: item.date,
      clicks: item.clicks,
      impressions: item.impressions,
      ctr: item.ctr / item.count,
      position: item.position / item.count,
    }))
  }

  const filterChartDataByGap = (data, gap) => {
    if (!data || data.length === 0) return data

    const start = new Date(chartDateRange.start_date)
    const end = new Date(chartDateRange.end_date)
    const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())
    const diffDays = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))

    let interval
    if (diffMonths > 12) interval = "quarter"
    else if (diffMonths > 3) interval = "month"
    else if (diffDays > 31) interval = "week"
    else interval = "day"

    return aggregateDataByInterval(data, interval)
  }

  const processChartData = () => {
    const hasComparison = dataAvailability.comparison && comparisonData.length > 0

    const filteredCurrent = filterChartDataByGap(
      chartData,
      calculateDataGap(chartDateRange.start_date, chartDateRange.end_date),
    )

    if (!hasComparison) {
      return filteredCurrent
    }

    const filteredComparison = filterChartDataByGap(
      comparisonData,
      calculateDataGap(chartDateRange.compare_start_date, chartDateRange.compare_end_date),
    )

    const maxLength = Math.max(filteredCurrent.length, filteredComparison.length)
    const combinedData = []

    for (let i = 0; i < maxLength; i++) {
      const currentItem = filteredCurrent[i]
      const comparisonItem = filteredComparison[i]

      const dataPoint = {
        dayLabel: `Day ${i + 1}`,
        currentDate: currentItem?.date || null,
        comparisonDate: comparisonItem?.date || null,
      }

      if (currentItem) {
        dataPoint.clicks = currentItem.clicks
        dataPoint.impressions = currentItem.impressions
        dataPoint.ctr = currentItem.ctr
        dataPoint.position = currentItem.position
      }

      if (comparisonItem) {
        dataPoint.clicks_comparison = comparisonItem.clicks
        dataPoint.impressions_comparison = comparisonItem.impressions
        dataPoint.ctr_comparison = comparisonItem.ctr
        dataPoint.position_comparison = comparisonItem.position
      }

      combinedData.push(dataPoint)
    }

    return combinedData
  }

  const selectedMetrics = Object.entries(activeMetrics)
    .filter(([_, value]) => value)
    .map(([key]) => key)

  const metricColors = {
    clicks: "#4285F4",
    impressions: "#7B61FF",
    ctr: "#00A389",
    position: "#FF9F43",
  }

  const metricLabels = {
    clicks: "Clicks",
    impressions: "Impressions",
    ctr: "CTR",
    position: "Position",
  }

  const formatAggregatedValue = (metric, value, isAggregated) => {
    if (typeof value !== "number" || isNaN(value)) {
      if (metric === "ctr" || metric === "position") return "-"
      return "0"
    }

    if (metric === "ctr") return `${(value * 100).toFixed(1)}%`
    if (metric === "position") return value.toFixed(1)

    const formattedValue = value.toLocaleString()
    return isAggregated ? `Total: ${formattedValue}` : formattedValue
  }

  const processedChartData = processChartData()
  const hasComparison = dataAvailability.comparison && comparisonData.length > 0

  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null

    const data = payload[0].payload

    if (hasComparison) {
      return (
        <div className="bg-white border border-[#DCDCDC] rounded-lg p-3 shadow-md min-w-[200px]">
          <div className="font-semibold mb-2 text-center">{label}</div>

          <div className="mb-2">
            <div className="text-xs text-gray-600 mb-1">
              Current: {data.currentDate ? new Date(data.currentDate).toLocaleDateString() : "N/A"}
            </div>
            {selectedMetrics.map((metric) => {
              const value = data[metric]
              if (value !== undefined && value !== null) {
                return (
                  <div key={metric} className="text-sm flex justify-between">
                    <span style={{ color: metricColors[metric] }}>{metricLabels[metric]}:</span>
                    <span style={{ color: metricColors[metric] }}>{formatAggregatedValue(metric, value, false)}</span>
                  </div>
                )
              }
              return null
            })}
          </div>

          <div>
            <div className="text-xs text-gray-600 mb-1">
              Comparison: {data.comparisonDate ? new Date(data.comparisonDate).toLocaleDateString() : "N/A"}
            </div>
            {selectedMetrics.map((metric) => {
              const value = data[`${metric}_comparison`]
              if (value !== undefined && value !== null) {
                return (
                  <div key={`${metric}_comparison`} className="text-sm flex justify-between">
                    <span style={{ color: metricColors[metric] }}>{metricLabels[metric]}:</span>
                    <span style={{ color: metricColors[metric] }}>{formatAggregatedValue(metric, value, false)}</span>
                  </div>
                )
              }
              return null
            })}
          </div>
        </div>
      )
    }

    const isAggregated = label.includes("-") || label.includes("Q")
    const periodLabel = isAggregated
      ? label.includes("Q")
        ? `Quarter ${label.split("Q")[1]}, ${label.split("-")[0]}`
        : label
      : label

    return (
      <div className="bg-white border border-[#DCDCDC] rounded-lg p-2 shadow-md min-w-[120px]">
        <div className="font-semibold mb-1">
          {periodLabel}
          {isAggregated && <span className="text-xs text-gray-500 ml-1">(Aggregated)</span>}
        </div>
        {payload.map((entry) => (
          <div key={entry.name} className="text-sm" style={{ color: entry.color }}>
            {metricLabels[entry.name]}: {formatAggregatedValue(entry.name, entry.value, isAggregated)}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="bg-white rounded-[20px] border border-[#DCDCDC] p-6 mb-5">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Performance Chart</h2>
      </div>

      <div className="flex h-[240px]">
        <div className="flex-grow relative">
          {chartLoading ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#352090] mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading chart data...</p>
              </div>
            </div>
          ) : chartError ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-red-500 text-sm">{chartError}</div>
            </div>
          ) : !dataAvailability.current ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-gray-500 text-sm">{dataAvailability.message}</div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={processedChartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#DCDCDC" />
                <XAxis
                  dataKey={hasComparison ? "dayLabel" : "date"}
                  tick={
                    hasComparison
                      ? { fontSize: 12, fill: "#888" }
                      : ({ x, y, payload }) => {
                          const date = payload.value
                          const start = new Date(chartDateRange.start_date)
                          const end = new Date(chartDateRange.end_date)
                          const diffDays = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
                          const diffMonths =
                            (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())

                          let value
                          if (date.includes("Q")) {
                            const [year, quarter] = date.split("-Q")
                            value = [`Q${quarter}`, year]
                          } else if (date.includes("-")) {
                            const d = new Date(date)
                            if (diffMonths > 12) {
                              value = [
                                d.toLocaleDateString("en-US", { month: "short" }),
                                d.toLocaleDateString("en-US", { year: "numeric" }),
                              ]
                            } else if (diffMonths > 3) {
                              value = [
                                d.toLocaleDateString("en-US", { month: "short" }),
                                d.toLocaleDateString("en-US", { year: "numeric" }),
                              ]
                            } else if (diffDays > 31) {
                              const endOfWeek = new Date(d)
                              endOfWeek.setDate(d.getDate() + 6)
                              value = [
                                `${d.toLocaleDateString("en-US", { month: "short", day: "2-digit" })} - ${endOfWeek.toLocaleDateString("en-US", { day: "2-digit" })}`,
                                d.toLocaleDateString("en-US", { year: "numeric" }),
                              ]
                            } else {
                              value = [
                                d.toLocaleDateString("en-US", { month: "short", day: "2-digit" }),
                                d.toLocaleDateString("en-US", { year: "numeric" }),
                              ]
                            }
                          } else {
                            const d = new Date(date)
                            value = [
                              d.toLocaleDateString("en-US", { month: "short", day: "2-digit" }),
                              d.toLocaleDateString("en-US", { year: "numeric" }),
                            ]
                          }

                          return (
                            <g transform={`translate(${x},${y})`}>
                              <text x={0} y={0} dy={12} textAnchor="middle" fill="#888" fontSize={12}>
                                {value[0]}
                              </text>
                              <text x={0} y={0} dy={28} textAnchor="middle" fill="#888" fontSize={12}>
                                {value[1]}
                              </text>
                            </g>
                          )
                        }
                  }
                  tickLine={false}
                  axisLine={{ stroke: "#DCDCDC" }}
                  height={hasComparison ? 30 : 50}
                  minTickGap={10}
                />

                {!hasComparison &&
                  selectedMetrics.map((metric, index) => (
                    <YAxis
                      key={metric}
                      yAxisId={metric}
                      orientation={index === 0 ? "left" : "right"}
                      tick={{ fontSize: 12, fill: "#888" }}
                      tickFormatter={(value) => formatAggregatedValue(metric, value, false)}
                      tickLine={false}
                      axisLine={{ stroke: "#DCDCDC" }}
                      label={{
                        value: metricLabels[metric],
                        angle: -90,
                        position: index === 0 ? "insideLeft" : "insideRight",
                        style: {
                          textAnchor: "middle",
                          fill: metricColors[metric],
                          fontSize: 12,
                          fontWeight: "bold",
                        },
                      }}
                    />
                  ))}

                <Tooltip content={<CustomTooltip />} />

                {selectedMetrics.map((metric) => (
                  <Line
                    key={metric}
                    type="monotone"
                    dataKey={metric}
                    yAxisId={hasComparison ? undefined : metric}
                    stroke={metricColors[metric]}
                    strokeWidth={2}
                    dot={{
                      r: 4,
                      strokeWidth: 2,
                      fill: metricColors[metric],
                      stroke: "#fff",
                    }}
                    activeDot={{
                      r: 6,
                      strokeWidth: 2,
                      fill: metricColors[metric],
                      stroke: "#fff",
                    }}
                    connectNulls={true}
                  />
                ))}

                {hasComparison &&
                  selectedMetrics.map((metric) => (
                    <Line
                      key={`${metric}_comparison`}
                      type="monotone"
                      dataKey={`${metric}_comparison`}
                      stroke={metricColors[metric]}
                      strokeWidth={2}
                      strokeDasharray="5,5"
                      dot={{
                        r: 3,
                        strokeWidth: 2,
                        fill: metricColors[metric],
                        stroke: "#fff",
                      }}
                      activeDot={{
                        r: 5,
                        strokeWidth: 2,
                        fill: metricColors[metric],
                        stroke: "#fff",
                      }}
                      connectNulls={true}
                    />
                  ))}
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChartSection
