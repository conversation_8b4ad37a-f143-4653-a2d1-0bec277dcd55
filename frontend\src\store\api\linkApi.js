import { apiSlice } from "./apiSlice"
import { setActiveLinks, linkCreated, linkDeleted, addRefreshingLink, removeRefreshingLink } from "../slices/linkSlice"
import { linkMovedToTrash } from "../slices/trashSlice"

const CLUSTERGAZER_SERVICE_URL = import.meta.env.VITE_CLUSTERGAZER_SERVICE || "http://localhost:8001"

export const linkApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get links for a specific cluster
    getClusterLinks: builder.query({
      query: ({ clusterId, workspaceId }) => ({
        url: `/clusters/${clusterId}/links`,
        headers: {
          "X-Workspace-ID": workspaceId,
        },
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      providesTags: (result, error, { clusterId }) => [
        { type: "Link", id: "LIST" },
        { type: "Link", id: clusterId },
        ...(result || []).map(({ _id }) => ({ type: "Link", id: _id })),
      ],
      transformResponse: (response) => {
        console.log("getClusterLinks response:", response)
        // Normalize and sort links by _id in descending order (newest first)
        const normalized = response.map((link) => ({
          ...link,
          id: link._id ?? link.id,
        }))
        return normalized.sort((a, b) => {
          if (a.id > b.id) return -1
          if (a.id < b.id) return 1
          return 0
        })
      },
      keepUnusedDataFor: 300, // Keep cached for 5 minutes
      // Force refetch when navigating back to links page
      refetchOnMountOrArgChange: 30, // Refetch if data is older than 30 seconds
      refetchOnFocus: true, // Refetch when window regains focus
      // Dispatch action to update Redux store
      async onQueryStarted({ clusterId }, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          console.log(`Updating Redux store with ${data.length} links for cluster ${clusterId}`)
          dispatch(setActiveLinks({ clusterId, links: data }))
        } catch (error) {
          console.error("Error fetching links:", error)
        }
      },
    }),

    // Add single link to cluster (Version 26 working approach)
    addLinkToCluster: builder.mutation({
      query: ({ clusterId, workspaceId, url }) => {
        console.log("addLinkToCluster payload:", { clusterId, workspaceId, url })

        return {
          url: `/clusters/${clusterId}/links`,
          method: "POST",
          body: { url }, // Single URL object - matches version 26
          headers: {
            "Content-Type": "application/json",
            "X-Workspace-ID": workspaceId,
          },
          baseUrl: CLUSTERGAZER_SERVICE_URL,
        }
      },
      invalidatesTags: (result, error, { clusterId }) => [
        { type: "Link", id: "LIST" },
        { type: "Link", id: clusterId },
      ],
      // Dispatch action to add link to Redux store
      async onQueryStarted({ clusterId }, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          console.log("addLinkToCluster response:", data)
          dispatch(linkCreated({ clusterId, newLink: data }))
        } catch (error) {
          console.error("Error adding link:", error)
        }
      },
    }),

    // Delete link (soft delete) - Fixed linkId extraction
    deleteLink: builder.mutation({
      query: ({ clusterId, linkId, workspaceId }) => {
        console.log("deleteLink params:", { clusterId, linkId, workspaceId })

        if (!linkId || linkId === "undefined") {
          throw new Error("Invalid linkId provided for deletion")
        }

        return {
          url: `/clusters/${clusterId}/links/${linkId}`,
          method: "DELETE",
          headers: {
            "X-Workspace-ID": workspaceId,
          },
          baseUrl: CLUSTERGAZER_SERVICE_URL,
        }
      },
      invalidatesTags: (result, error, { clusterId }) => [
        { type: "Link", id: "LIST" },
        { type: "Link", id: clusterId },
        { type: "DeletedLink", id: "LIST" },
      ],
      // Dispatch actions to update Redux store
      async onQueryStarted({ clusterId, linkId, workspaceId }, { dispatch, queryFulfilled, getState }) {
        try {
          await queryFulfilled

          // Get the link data before removing it
          const state = getState()
          const clusterLinks = state.link.activeLinks[clusterId] || []
          const linkToDelete = clusterLinks.find((l) => (l.id || l._id) === linkId)

          // Remove from active links
          dispatch(linkDeleted({ clusterId, linkId }))

          // Add to trash with proper formatting
          if (linkToDelete) {
            const deletedAtDate = new Date()
            const permanentDeletionDate = new Date(deletedAtDate)
            permanentDeletionDate.setDate(permanentDeletionDate.getDate() + 30)

            const deletedLink = {
              id: linkToDelete.id || linkToDelete._id,
              url: linkToDelete.url,
              clusterId: clusterId,
              clusterName: `Cluster ${clusterId.substring(0, 8)}...`,
              status: linkToDelete.status || "inactive",
              dateDeleted: deletedAtDate.toLocaleDateString(),
              permanentDeletionDate: permanentDeletionDate.toLocaleDateString(),
              daysLeft: 30,
            }
            dispatch(linkMovedToTrash(deletedLink))
          }
        } catch (error) {
          console.error("Error deleting link:", error)
        }
      },
    }),

    // Refresh link data - Enhanced with proper polling integration
    refreshLinkData: builder.mutation({
      query: ({ clusterId, linkId, workspaceId }) => {
        console.log("refreshLinkData API call:", { clusterId, linkId, workspaceId })

        if (!linkId || linkId === "undefined") {
          throw new Error("Invalid linkId provided for refresh")
        }

        return {
          url: `/clusters/${clusterId}/links/${linkId}/refresh`,
          method: "POST",
          headers: {
            "X-Workspace-ID": workspaceId,
          },
          baseUrl: CLUSTERGAZER_SERVICE_URL,
        }
      },
      invalidatesTags: (result, error, { clusterId, linkId }) => [
        { type: "Link", id: linkId },
        { type: "Link", id: clusterId },
        { type: "LinkPerformance", id: linkId }, // Also invalidate performance data
      ],
      async onQueryStarted({ linkId, clusterId }, { dispatch, queryFulfilled }) {
        console.log(`Starting refresh for link ${linkId} in cluster ${clusterId}`)
        dispatch(addRefreshingLink(linkId))

        try {
          const result = await queryFulfilled
          console.log(`Refresh API successful for link ${linkId}:`, result)

          // The link will be added to processing list by useLinks hook
          // Keep refreshing state briefly for visual feedback
        } catch (error) {
          console.error(`Refresh API failed for link ${linkId}:`, error)
          dispatch(removeRefreshingLink(linkId))
        }
      },
    }),

    // Get link performance data - Fixed linkId extraction
    getLinkPerformance: builder.query({
      query: ({ clusterId, linkId, startDate, endDate, perPage = 30 }) => {
        console.log("getLinkPerformance params:", { clusterId, linkId, startDate, endDate })

        if (!linkId || linkId === "undefined") {
          throw new Error("Invalid linkId provided for performance query")
        }

        return {
          url: `/clusters/${clusterId}/links/${linkId}/performance/data`,
          params: {
            start_date: startDate,
            end_date: endDate,
            per_page: perPage,
          },
          baseUrl: CLUSTERGAZER_SERVICE_URL,
        }
      },
      providesTags: (result, error, { linkId }) => [{ type: "LinkPerformance", id: linkId }],
      keepUnusedDataFor: 300, // Keep cached for 5 minutes
      transformResponse: (response) => {
        console.log("Raw API response:", response)

        // Handle different response formats
        let performanceData = []

        if (Array.isArray(response)) {
          performanceData = response
        } else if (response && response.data && Array.isArray(response.data)) {
          performanceData = response.data
        } else if (response && response.performance_data && Array.isArray(response.performance_data)) {
          performanceData = response.performance_data
        } else {
          console.warn("Unexpected response format:", response)
          return { data: [] }
        }

        // Transform and validate data format
        const transformedData = performanceData.map((item) => ({
          date: item.date || item.Date || new Date().toISOString().split("T")[0],
          clicks: Number(item.clicks || item.Clicks || 0),
          impressions: Number(item.impressions || item.Impressions || 0),
          ctr: Number(item.ctr || item.CTR || (item.clicks && item.impressions ? item.clicks / item.impressions : 0)),
          position: Number(item.position || item.Position || item.avg_position || 0),
        }))

        console.log("Transformed data:", transformedData)

        return {
          data: transformedData.sort((a, b) => new Date(a.date) - new Date(b.date)),
        }
      },
    }),
  }),
})

export const {
  useGetClusterLinksQuery,
  useAddLinkToClusterMutation,
  useDeleteLinkMutation,
  useRefreshLinkDataMutation,
  useGetLinkPerformanceQuery,
} = linkApi
