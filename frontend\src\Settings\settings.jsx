import Navbar from "../components/navbar"
import Sidebar from "../components/sidebar"
import NavigationTest from "../components/navigation-test"

const Settings = () => {
  return (
    <div className="flex flex-col min-h-screen font-noto">
      <Navbar />
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar activePage="settings" />
        </div>
        <main className="flex-1 p-6 overflow-auto">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">Settings</h1>
            
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Workspace Settings</h2>
              <p className="text-gray-600">
                Settings page is under development. This page is used for testing navigation optimization.
              </p>
              
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="font-semibold text-blue-800 mb-2">Navigation Test</h3>
                <p className="text-blue-700 text-sm">
                  Use the navigation test panel to verify that navigation between pages works smoothly 
                  without full page reloads when staying within the same workspace.
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
      
      {/* Navigation Test Component - Remove in production */}
      {process.env.NODE_ENV === 'development' && <NavigationTest />}
    </div>
  )
}

export default Settings
