import { apiSlice } from "./apiSlice"

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE || "http://localhost:8000"

export const workspaceApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get user workspaces - Now includes domain info
    getUserWorkspaces: builder.query({
      query: () => ({
        url: "/workspaces",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: ["Workspace"],
      transformResponse: (response) => {
        // Handle different response formats
        let workspaceObjects = []

        if (Array.isArray(response)) {
          workspaceObjects = response
        } else if (response && Array.isArray(response.workspaces)) {
          workspaceObjects = response.workspaces
        } else if (response && response.data && Array.isArray(response.data)) {
          workspaceObjects = response.data
        }

        // Validate and process workspace objects
        return workspaceObjects.filter((workspace) => {
          return workspace && (workspace.id || workspace._id)
        })
      },
    }),

    // Update last accessed workspace - Now returns domain info
    updateLastAccessedWorkspace: builder.mutation({
      query: (workspaceId) => ({
        url: `/workspaces/workspace/${workspaceId}/access`,
        method: "GET",
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: ["Workspace", "WorkspaceDomain"],
      // Transform response to include domain info
      transformResponse: (response) => {
        console.log("🔄 Workspace access response:", response)
        return response
      },
      // 🔑 NEW: Update workspace list cache with fresh domain info
      async onQueryStarted(workspaceId, { dispatch, queryFulfilled }) {
        try {
          const { data: result } = await queryFulfilled
          console.log("✅ Workspace access result:", result)
          
          // If the response includes updated workspace info with domain
          if (result && result.workspace) {
            console.log("🔄 Updating workspace list cache with fresh domain info")
            
            // Update the getUserWorkspaces cache with fresh workspace data
            dispatch(
              workspaceApi.util.updateQueryData('getUserWorkspaces', undefined, (draft) => {
                const workspaceIndex = draft.findIndex(ws => 
                  (ws.id || ws._id) === workspaceId
                )
                
                if (workspaceIndex !== -1) {
                  // Update the workspace with fresh domain info
                  draft[workspaceIndex] = {
                    ...draft[workspaceIndex],
                    ...result.workspace,
                    domain_info: result.workspace.domain_info || result.domain_info,
                    has_domain: result.workspace.has_domain !== undefined 
                      ? result.workspace.has_domain 
                      : (result.workspace.domain_info ? true : false)
                  }
                  console.log("✅ Updated workspace in cache:", draft[workspaceIndex])
                }
              })
            )
          }
        } catch (error) {
          console.error("❌ Error updating workspace cache:", error)
        }
      },
    }),

    // Get workspace domain info
    getWorkspaceDomain: builder.query({
      query: (workspaceId) => ({
        url: `/workspaces/workspace/${workspaceId}/domain`,
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: (result, error, workspaceId) => [{ type: "WorkspaceDomain", id: workspaceId }],
    }),

    // Get workspace by ID
    getWorkspaceById: builder.query({
      query: (workspaceId) => ({
        url: `/workspaces/workspace/${workspaceId}`,
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: (result, error, workspaceId) => [{ type: "Workspace", id: workspaceId }],
    }),

    // Create workspace
    createWorkspace: builder.mutation({
      query: (workspaceData) => ({
        url: "/workspaces",
        method: "POST",
        body: workspaceData,
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: ["Workspace"],
    }),

    // Update workspace
    updateWorkspace: builder.mutation({
      query: ({ workspaceId, ...workspaceData }) => ({
        url: `/workspaces/workspace/${workspaceId}`,
        method: "PUT",
        body: workspaceData,
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: ["Workspace"],
    }),

    // Delete workspace
    deleteWorkspace: builder.mutation({
      query: (workspaceId) => ({
        url: `/workspaces/workspace/${workspaceId}`,
        method: "DELETE",
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: ["Workspace"],
    }),
  }),
})

export const {
  useGetUserWorkspacesQuery,
  useUpdateLastAccessedWorkspaceMutation,
  useGetWorkspaceDomainQuery,
  useGetWorkspaceByIdQuery,
  useCreateWorkspaceMutation,
  useUpdateWorkspaceMutation,
  useDeleteWorkspaceMutation,
} = workspaceApi
