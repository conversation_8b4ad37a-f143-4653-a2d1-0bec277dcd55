import { useEffect, useState } from "react"
import { Outlet, usePara<PERSON>, useLocation } from "react-router-dom"
import { useNavigation } from "../hooks/useNavigation"
import { useWorkspace } from "../hooks/useWorkspace"
import OnboardingCheck from "./onboarding-check"

/**
 * WorkspaceLayout component that provides a persistent layout for workspace pages
 * This prevents unnecessary remounting when navigating within the same workspace
 */
const WorkspaceLayout = () => {
  const { workspaceId } = useParams()
  const location = useLocation()
  const { currentWorkspace, loading: workspaceLoading } = useWorkspace()
  const { initializeNavigation, syncWithCurrentUrl } = useNavigation()
  const [hasInitialized, setHasInitialized] = useState(false)

  // Initialize navigation state when workspace changes
  useEffect(() => {
    if (workspaceId && currentWorkspace?.id === workspaceId && !hasInitialized) {
      initializeNavigation(workspaceId, location.pathname)
      setHasInitialized(true)
      console.log("🔄 WorkspaceLayout: Navigation initialized for workspace", workspaceId)
    }
  }, [workspaceId, currentWorkspace?.id, location.pathname, initializeNavigation, hasInitialized])

  // Sync navigation state with URL changes
  useEffect(() => {
    if (currentWorkspace?.id === workspaceId) {
      syncWithCurrentUrl()
    }
  }, [location.pathname, syncWithCurrentUrl, currentWorkspace?.id, workspaceId])

  // Reset initialization flag when workspace changes
  useEffect(() => {
    if (currentWorkspace?.id !== workspaceId) {
      setHasInitialized(false)
    }
  }, [currentWorkspace?.id, workspaceId])

  console.log("🔍 WorkspaceLayout Debug:", {
    workspaceId,
    currentWorkspaceId: currentWorkspace?.id,
    workspaceLoading,
    pathname: location.pathname,
    hasInitialized
  })

  // Show loading while workspace is being loaded
  if (workspaceLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading workspace...</p>
        </div>
      </div>
    )
  }

  // If no current workspace or workspace mismatch, show onboarding check
  // But only if we're on the root workspace path or if workspace is completely missing
  const isRootWorkspacePath = location.pathname === `/workspace/${workspaceId}`
  const shouldShowOnboarding = !currentWorkspace ||
    (currentWorkspace.id !== workspaceId && isRootWorkspacePath)

  if (shouldShowOnboarding) {
    console.log("🔄 WorkspaceLayout: Showing OnboardingCheck", {
      reason: !currentWorkspace ? "no workspace" : "workspace mismatch",
      currentWorkspaceId: currentWorkspace?.id,
      urlWorkspaceId: workspaceId,
      isRootPath: isRootWorkspacePath
    })
    return <OnboardingCheck />
  }

  // If workspace exists but doesn't match URL workspace, and we're not on root path,
  // this might be a timing issue - render the content anyway
  if (currentWorkspace && currentWorkspace.id !== workspaceId && !isRootWorkspacePath) {
    console.log("⚠️ WorkspaceLayout: Workspace mismatch but not on root path, rendering content anyway", {
      currentWorkspaceId: currentWorkspace.id,
      urlWorkspaceId: workspaceId,
      pathname: location.pathname
    })
  }

  // Render the workspace content
  console.log("✅ WorkspaceLayout: Rendering workspace content")
  return <Outlet />
}

export default WorkspaceLayout
