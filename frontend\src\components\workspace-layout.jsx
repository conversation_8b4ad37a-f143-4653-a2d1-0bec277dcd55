import { useEffect } from "react"
import { Outlet, useParams, useLocation } from "react-router-dom"
import { useNavigation } from "../hooks/useNavigation"
import { useWorkspace } from "../hooks/useWorkspace"
import OnboardingCheck from "./onboarding-check"

/**
 * WorkspaceLayout component that provides a persistent layout for workspace pages
 * This prevents unnecessary remounting when navigating within the same workspace
 */
const WorkspaceLayout = () => {
  const { workspaceId } = useParams()
  const location = useLocation()
  const { currentWorkspace } = useWorkspace()
  const { initializeNavigation, syncWithCurrentUrl } = useNavigation()
  
  // Initialize navigation state when workspace changes
  useEffect(() => {
    if (workspaceId && currentWorkspace?.id === workspaceId) {
      initializeNavigation(workspaceId, location.pathname)
    }
  }, [workspaceId, currentWorkspace?.id, location.pathname, initializeNavigation])
  
  // Sync navigation state with URL changes
  useEffect(() => {
    syncWithCurrentUrl()
  }, [location.pathname, syncWithCurrentUrl])
  
  // If no current workspace or workspace mismatch, show onboarding check
  if (!currentWorkspace || currentWorkspace.id !== workspaceId) {
    return <OnboardingCheck />
  }
  
  // Render the workspace content
  return <Outlet />
}

export default WorkspaceLayout
