import { createSlice } from "@reduxjs/toolkit"
import { workspace<PERSON>pi } from "../api/workspaceApi"

const initialState = {
  currentWorkspace: localStorage.getItem("currentWorkspaceId") || null,
  workspaces: [],
  loading: false,
  error: null,
}

const workspaceSlice = createSlice({
  name: "workspace",
  initialState,
  reducers: {
    setCurrentWorkspace: (state, action) => {
      state.currentWorkspace = action.payload
      if (action.payload) {
        localStorage.setItem("currentWorkspaceId", action.payload)
      } else {
        localStorage.removeItem("currentWorkspaceId")
      }
    },
    clearWorkspaceError: (state) => {
      state.error = null
    },
    updateWorkspaceInList: (state, action) => {
      const { workspaceId, workspaceData } = action.payload
      const workspaceIndex = state.workspaces.findIndex(ws => 
        (ws.id || ws._id) === workspaceId
      )
      
      if (workspaceIndex !== -1) {
        state.workspaces[workspaceIndex] = {
          ...state.workspaces[workspaceIndex],
          ...workspaceData
        }
        console.log("✅ Updated workspace in Redux state:", state.workspaces[workspaceIndex])
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Get user workspaces
      .addMatcher(workspaceApi.endpoints.getUserWorkspaces.matchPending, (state) => {
        state.loading = true
        state.error = null
      })
      .addMatcher(workspaceApi.endpoints.getUserWorkspaces.matchFulfilled, (state, action) => {
        state.loading = false
        state.workspaces = action.payload

        // Set current workspace if not set
        if (!state.currentWorkspace && action.payload.length > 0) {
          const firstWorkspaceId = action.payload[0].id || action.payload[0]._id
          state.currentWorkspace = firstWorkspaceId
          localStorage.setItem("currentWorkspaceId", firstWorkspaceId)
        }
      })
      .addMatcher(workspaceApi.endpoints.getUserWorkspaces.matchRejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.data?.detail || "Failed to fetch workspaces"
      })

      // Update last accessed workspace
      .addMatcher(workspaceApi.endpoints.updateLastAccessedWorkspace.matchFulfilled, (state, action) => {
        const result = action.payload
        if (result && result.workspace) {
          const workspaceId = action.meta.arg
          const workspaceIndex = state.workspaces.findIndex(ws => 
            (ws.id || ws._id) === workspaceId
          )
          
          if (workspaceIndex !== -1) {
            state.workspaces[workspaceIndex] = {
              ...state.workspaces[workspaceIndex],
              ...result.workspace,
              domain_info: result.workspace.domain_info || result.domain_info,
              has_domain: result.workspace.has_domain !== undefined 
                ? result.workspace.has_domain 
                : (result.workspace.domain_info ? true : false)
            }
            console.log("✅ Updated workspace in Redux slice:", state.workspaces[workspaceIndex])
          }
        }
      })
      .addMatcher(workspaceApi.endpoints.updateLastAccessedWorkspace.matchRejected, (state, action) => {
        state.error = action.payload?.data?.detail || "Failed to update workspace access"
      })
  },
})

export const { setCurrentWorkspace, clearWorkspaceError, updateWorkspaceInList } = workspaceSlice.actions
export default workspaceSlice.reducer
