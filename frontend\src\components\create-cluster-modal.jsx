import { useState, useEffect, useRef } from "react"
import { X, ChevronDown, Trash2, AlertCircle } from "lucide-react"
import { useDispatch } from "react-redux"
import { addNotification } from "../store/slices/uiSlice"

const TOP_COUNTRIES = ["usa", "ind", "gbr", "can", "aus"]

const ALL_COUNTRIES = [
  { code: "afg", name: "Afghanistan", continent: "Asia", region: "South Asia" },
  { code: "ala", name: "Åland Islands", continent: "Europe", region: "Northern Europe" },
  { code: "alb", name: "Albania", continent: "Europe", region: "Southern Europe" },
  { code: "dza", name: "Algeria", continent: "Africa", region: "North Africa" },
  { code: "asm", name: "American Samoa", continent: "Oceania", region: "Polynesia" },
  { code: "and", name: "Andorra", continent: "Europe", region: "Southern Europe" },
  { code: "ago", name: "Angola", continent: "Africa", region: "Central Africa" },
  { code: "aia", name: "Anguilla", continent: "North America", region: "Caribbean" },
  { code: "ata", name: "Antarctica", continent: "Antarctica", region: "Antarctica" },
  { code: "atg", name: "Antigua and Barbuda", continent: "North America", region: "Caribbean" },
  { code: "arg", name: "Argentina", continent: "South America", region: "South America" },
  { code: "arm", name: "Armenia", continent: "Asia", region: "Western Asia" },
  { code: "abw", name: "Aruba", continent: "North America", region: "Caribbean" },
  { code: "aus", name: "Australia", continent: "Oceania", region: "Australia and New Zealand" },
  { code: "aut", name: "Austria", continent: "Europe", region: "Western Europe" },
  { code: "aze", name: "Azerbaijan", continent: "Asia", region: "Western Asia" },
  { code: "bhs", name: "Bahamas", continent: "North America", region: "Caribbean" },
  { code: "bhr", name: "Bahrain", continent: "Asia", region: "Western Asia" },
  { code: "bgd", name: "Bangladesh", continent: "Asia", region: "South Asia" },
  { code: "brb", name: "Barbados", continent: "North America", region: "Caribbean" },
  { code: "blr", name: "Belarus", continent: "Europe", region: "Eastern Europe" },
  { code: "bel", name: "Belgium", continent: "Europe", region: "Western Europe" },
  { code: "blz", name: "Belize", continent: "North America", region: "Central America" },
  { code: "ben", name: "Benin", continent: "Africa", region: "West Africa" },
  { code: "btn", name: "Bhutan", continent: "Asia", region: "South Asia" },
  { code: "bol", name: "Bolivia", continent: "South America", region: "South America" },
  { code: "bih", name: "Bosnia and Herzegovina", continent: "Europe", region: "Southern Europe" },
  { code: "bwa", name: "Botswana", continent: "Africa", region: "Southern Africa" },
  { code: "bra", name: "Brazil", continent: "South America", region: "South America" },
  { code: "brn", name: "Brunei Darussalam", continent: "Asia", region: "Southeast Asia" },
  { code: "bgr", name: "Bulgaria", continent: "Europe", region: "Eastern Europe" },
  { code: "bfa", name: "Burkina Faso", continent: "Africa", region: "West Africa" },
  { code: "bdi", name: "Burundi", continent: "Africa", region: "East Africa" },
  { code: "cpv", name: "Cabo Verde", continent: "Africa", region: "West Africa" },
  { code: "khm", name: "Cambodia", continent: "Asia", region: "Southeast Asia" },
  { code: "cmr", name: "Cameroon", continent: "Africa", region: "Central Africa" },
  { code: "can", name: "Canada", continent: "North America", region: "North America" },
  { code: "caf", name: "Central African Republic", continent: "Africa", region: "Central Africa" },
  { code: "tcd", name: "Chad", continent: "Africa", region: "Central Africa" },
  { code: "chl", name: "Chile", continent: "South America", region: "South America" },
  { code: "chn", name: "China", continent: "Asia", region: "East Asia" },
  { code: "col", name: "Colombia", continent: "South America", region: "South America" },
  { code: "cod", name: "Congo (Democratic Republic)", continent: "Africa", region: "Central Africa" },
  { code: "cog", name: "Congo (Republic)", continent: "Africa", region: "Central Africa" },
  { code: "cri", name: "Costa Rica", continent: "North America", region: "Central America" },
  { code: "hrv", name: "Croatia", continent: "Europe", region: "Southern Europe" },
  { code: "cub", name: "Cuba", continent: "North America", region: "Caribbean" },
  { code: "cyp", name: "Cyprus", continent: "Asia", region: "Western Asia" },
  { code: "cze", name: "Czechia", continent: "Europe", region: "Eastern Europe" },
  { code: "dnk", name: "Denmark", continent: "Europe", region: "Northern Europe" },
  { code: "dji", name: "Djibouti", continent: "Africa", region: "East Africa" },
  { code: "ecu", name: "Ecuador", continent: "South America", region: "South America" },
  { code: "egy", name: "Egypt", continent: "Africa", region: "North Africa" },
  { code: "slv", name: "El Salvador", continent: "North America", region: "Central America" },
  { code: "est", name: "Estonia", continent: "Europe", region: "Northern Europe" },
  { code: "eth", name: "Ethiopia", continent: "Africa", region: "East Africa" },
  { code: "fin", name: "Finland", continent: "Europe", region: "Northern Europe" },
  { code: "fra", name: "France", continent: "Europe", region: "Western Europe" },
  { code: "deu", name: "Germany", continent: "Europe", region: "Western Europe" },
  { code: "gha", name: "Ghana", continent: "Africa", region: "West Africa" },
  { code: "grc", name: "Greece", continent: "Europe", region: "Southern Europe" },
  { code: "gbr", name: "United Kingdom", continent: "Europe", region: "Northern Europe" },
  { code: "hkg", name: "Hong Kong", continent: "Asia", region: "East Asia" },
  { code: "hun", name: "Hungary", continent: "Europe", region: "Eastern Europe" },
  { code: "ind", name: "India", continent: "Asia", region: "South Asia" },
  { code: "idn", name: "Indonesia", continent: "Asia", region: "Southeast Asia" },
  { code: "irl", name: "Ireland", continent: "Europe", region: "Northern Europe" },
  { code: "isr", name: "Israel", continent: "Asia", region: "Western Asia" },
  { code: "ita", name: "Italy", continent: "Europe", region: "Southern Europe" },
  { code: "jpn", name: "Japan", continent: "Asia", region: "East Asia" },
  { code: "ken", name: "Kenya", continent: "Africa", region: "East Africa" },
  { code: "kor", name: "South Korea", continent: "Asia", region: "East Asia" },
  { code: "lka", name: "Sri Lanka", continent: "Asia", region: "South Asia" },
  { code: "lux", name: "Luxembourg", continent: "Europe", region: "Western Europe" },
  { code: "mys", name: "Malaysia", continent: "Asia", region: "Southeast Asia" },
  { code: "mex", name: "Mexico", continent: "North America", region: "North America" },
  { code: "nld", name: "Netherlands", continent: "Europe", region: "Western Europe" },
  { code: "nzl", name: "New Zealand", continent: "Oceania", region: "Australia and New Zealand" },
  { code: "nga", name: "Nigeria", continent: "Africa", region: "West Africa" },
  { code: "nor", name: "Norway", continent: "Europe", region: "Northern Europe" },
  { code: "omn", name: "Oman", continent: "Asia", region: "Western Asia" },
  { code: "pak", name: "Pakistan", continent: "Asia", region: "South Asia" },
  { code: "per", name: "Peru", continent: "South America", region: "South America" },
  { code: "phl", name: "Philippines", continent: "Asia", region: "Southeast Asia" },
  { code: "pol", name: "Poland", continent: "Europe", region: "Eastern Europe" },
  { code: "prt", name: "Portugal", continent: "Europe", region: "Southern Europe" },
  { code: "qat", name: "Qatar", continent: "Asia", region: "Western Asia" },
  { code: "rou", name: "Romania", continent: "Europe", region: "Eastern Europe" },
  { code: "rus", name: "Russia", continent: "Europe", region: "Eastern Europe" },
  { code: "sau", name: "Saudi Arabia", continent: "Asia", region: "Western Asia" },
  { code: "sgp", name: "Singapore", continent: "Asia", region: "Southeast Asia" },
  { code: "zaf", name: "South Africa", continent: "Africa", region: "Southern Africa" },
  { code: "esp", name: "Spain", continent: "Europe", region: "Southern Europe" },
  { code: "swe", name: "Sweden", continent: "Europe", region: "Northern Europe" },
  { code: "che", name: "Switzerland", continent: "Europe", region: "Western Europe" },
  { code: "tha", name: "Thailand", continent: "Asia", region: "Southeast Asia" },
  { code: "tur", name: "Turkey", continent: "Asia", region: "Western Asia" },
  { code: "uga", name: "Uganda", continent: "Africa", region: "East Africa" },
  { code: "ukr", name: "Ukraine", continent: "Europe", region: "Eastern Europe" },
  { code: "are", name: "United Arab Emirates", continent: "Asia", region: "Western Asia" },
  { code: "usa", name: "United States of America", continent: "North America", region: "North America" },
  { code: "ury", name: "Uruguay", continent: "South America", region: "South America" },
  { code: "ven", name: "Venezuela", continent: "South America", region: "South America" },
  { code: "vnm", name: "Vietnam", continent: "Asia", region: "Southeast Asia" },
  { code: "zmb", name: "Zambia", continent: "Africa", region: "East Africa" },
  { code: "zwe", name: "Zimbabwe", continent: "Africa", region: "Southern Africa" },
]

// Get unique continents and regions
const CONTINENTS = [...new Set(ALL_COUNTRIES.map((country) => country.continent))].sort()
const REGIONS = [...new Set(ALL_COUNTRIES.map((country) => country.region))].sort()

// Add helper functions at the top level after ALL_COUNTRIES
const getCountriesByRegion = (region) => {
  return ALL_COUNTRIES.filter(country => country.region === region).map(country => country.code)
}

const getCountriesByContinent = (continent) => {
  return ALL_COUNTRIES.filter(country => country.continent === continent).map(country => country.code)
}

const CreateClusterModal = ({ isOpen, onClose, onSubmit, domainInfo }) => {
  const dispatch = useDispatch()

  const [clusterName, setClusterName] = useState("")
  const [deviceFilter, setDeviceFilter] = useState("All Devices")
  const [showDeviceDropdown, setShowDeviceDropdown] = useState(false)

  // Country filter type selection
  const [countryFilterType, setCountryFilterType] = useState("region")
  const [selectedItems, setSelectedItems] = useState([])
  const [showLocationDropdown, setShowLocationDropdown] = useState(false)

  // Page URLs
  const [pilarPageUrl, setPilarPageUrl] = useState("")
  const [supportingPages, setSupportingPages] = useState([{ url: "" }])

  const [searchTerm, setSearchTerm] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showCreationProgress, setShowCreationProgress] = useState(false)
  const [errors, setErrors] = useState({})

  const deviceOptions = ["All Devices", "Mobile", "Desktop"]
  const modalRef = useRef(null)
  const deviceDropdownRef = useRef(null)
  const locationDropdownRef = useRef(null)

  // Fix the domain display
  const displayDomain = domainInfo?.siteUrl
    ? domainInfo.siteUrl.startsWith("sc-domain:")
      ? domainInfo.siteUrl
      : `sc-domain:${domainInfo.siteUrl}`
    : ""

  // Get options based on filter type
  const getLocationOptions = () => {
    switch (countryFilterType) {
      case "region":
        return REGIONS.map((region) => ({ code: region, name: region }))
      case "continent":
        return CONTINENTS.map((continent) => ({ code: continent, name: continent }))
      case "country":
        return ALL_COUNTRIES
      default:
        return []
    }
  }

  // Filter options based on search term
  const filteredLocationOptions = searchTerm
    ? getLocationOptions().filter((option) => option.name.toLowerCase().includes(searchTerm.toLowerCase()))
    : getLocationOptions()

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (deviceDropdownRef.current && !deviceDropdownRef.current.contains(event.target)) {
        setShowDeviceDropdown(false)
      }
      if (locationDropdownRef.current && !locationDropdownRef.current.contains(event.target)) {
        setShowLocationDropdown(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose])

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setClusterName("")
      setDeviceFilter("All Devices")
      setCountryFilterType("region")
      setSelectedItems([])
      setPilarPageUrl("")
      setSupportingPages([{ url: "" }])
      setErrors({})
      setIsSubmitting(false)
      setShowCreationProgress(false)
    }
  }, [isOpen])

  // Reset selected items when filter type changes
  useEffect(() => {
    setSelectedItems([])
    setSearchTerm("")
  }, [countryFilterType])

  const handleAddSupportingPage = () => {
    setSupportingPages([...supportingPages, { url: "" }])
  }

  const handleRemoveSupportingPage = (index) => {
    const newPages = [...supportingPages]
    newPages.splice(index, 1)
    setSupportingPages(newPages)
  }

  const validateUrl = (url) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const validateDomainMatch = (url, domain) => {
    try {
      const cleanDomain = domain.replace("sc-domain:", "")
      const urlObj = new URL(url)
      const urlDomain = urlObj.hostname
      return urlDomain === cleanDomain || urlDomain.endsWith(`.${cleanDomain}`)
    } catch {
      return false
    }
  }

  const handlePilarPageChange = (value) => {
    setPilarPageUrl(value)

    if (value.trim()) {
      const isDomainValid = validateDomainMatch(value, displayDomain)
      const newErrors = { ...errors }

      if (!validateUrl(value)) {
        newErrors.pilarPage = "Please enter a valid URL"
      } else if (!isDomainValid) {
        newErrors.pilarPage = `URL must be from domain: ${displayDomain.replace("sc-domain:", "")}`
      } else {
        delete newErrors.pilarPage
      }

      setErrors(newErrors)
    } else {
      const newErrors = { ...errors }
      delete newErrors.pilarPage
      setErrors(newErrors)
    }
  }

  const handleSupportingPageChange = (index, value) => {
    const newPages = [...supportingPages]
    newPages[index].url = value
    setSupportingPages(newPages)

    if (value.trim()) {
      const isDomainValid = validateDomainMatch(value, displayDomain)
      const newErrors = { ...errors }

      if (!validateUrl(value)) {
        newErrors[`supporting_${index}`] = "Please enter a valid URL"
      } else if (!isDomainValid) {
        newErrors[`supporting_${index}`] = `URL must be from domain: ${displayDomain.replace("sc-domain:", "")}`
      } else {
        delete newErrors[`supporting_${index}`]
      }

      setErrors(newErrors)
    } else {
      const newErrors = { ...errors }
      delete newErrors[`supporting_${index}`]
      setErrors(newErrors)
    }
  }

  const toggleLocationItem = (code) => {
    if (selectedItems.includes(code)) {
      setSelectedItems(selectedItems.filter((c) => c !== code))
    } else {
      setSelectedItems([...selectedItems, code])
    }
  }

  const getLocationName = (code) => {
    const options = getLocationOptions()
    const item = options.find((option) => option.code === code)
    return item ? item.name : code
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setShowCreationProgress(true)

    try {
      // Validate cluster name
      if (!clusterName.trim()) {
        throw new Error("Cluster name is required")
      }

      // Validate device filter
      if (!deviceFilter) {
        throw new Error("Device filter is required")
      }

      // Validate location selection
      if (selectedItems.length === 0) {
        throw new Error(`At least one ${countryFilterType} must be selected`)
      }

      // Convert selected items to country codes based on filter type
      let countryCodesArray = []
      if (countryFilterType === "region") {
        selectedItems.forEach(region => {
          countryCodesArray = [...countryCodesArray, ...getCountriesByRegion(region)]
        })
      } else if (countryFilterType === "continent") {
        selectedItems.forEach(continent => {
          countryCodesArray = [...countryCodesArray, ...getCountriesByContinent(continent)]
        })
      } else {
        // For country type, we already have country codes
        countryCodesArray = selectedItems
      }

      // Remove duplicates and create the comma-separated string
      const uniqueCountryCodes = [...new Set(countryCodesArray)]
      const locationFilter = uniqueCountryCodes.length === ALL_COUNTRIES.length ? "ALL" : uniqueCountryCodes.join(",")

      // Validate pilar page
      if (!pilarPageUrl.trim()) {
        throw new Error("Pilar page URL is required")
      }

      // Validate and prepare supporting pages
      const validSupportingPages = supportingPages
        .map((page) => page.url.trim())
        .filter((url) => {
          if (!url) return false
          if (!url.startsWith("http://") && !url.startsWith("https://")) {
            throw new Error("All URLs must start with http:// or https://")
          }
          return true
        })

      // Create links array with pillar page and supporting pages
      const allLinks = [
        // Add pillar page as first link with pillar tag
        { 
          url: pilarPageUrl.trim(),
          is_pillar: true // Tag to identify pillar page
        },
        // Add supporting pages
        ...validSupportingPages.map((url) => ({ 
          url,
          is_pillar: false // Tag to identify supporting pages
        }))
      ]

      // Prepare cluster data
      const clusterData = {
        clusterName: clusterName.trim(),
        deviceFilter: deviceFilter === "All Devices" ? "ALL" : deviceFilter,
        countryFilter: locationFilter,
        links: allLinks
      }

      // Submit cluster data
      const success = await onSubmit(clusterData)

      if (success) {
        // Reset form and close modal
        setClusterName("")
        setDeviceFilter("All Devices")
        setCountryFilterType("region")
        setSelectedItems([])
        setPilarPageUrl("")
        setSupportingPages([{ url: "" }])
        setErrors({})
        onClose()
      }
    } catch (error) {
      console.error("Error creating cluster:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Creation Failed",
          message: error.message || "Failed to create cluster. Please try again.",
        }),
      )
    } finally {
      setIsSubmitting(false)
      setShowCreationProgress(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-[8px] shadow-lg w-full max-w-2xl mx-4 max-h-[90vh] flex flex-col">
        {/* Creation Progress Overlay */}
        {showCreationProgress && (
          <div className="absolute inset-0 bg-white bg-opacity-95 rounded-lg flex items-center justify-center z-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#352090] mx-auto"></div>
              <div className="mt-6 px-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Creating Your Cluster</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  Setting up your cluster and initializing Google Search Console data integration.
                  <br />
                  This may take a few moments...
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold">Create Cluster</h2>
          <p className="text-sm text-gray-500 mt-1">Clusters for {displayDomain}</p>
        </div>

        <div className="p-6 overflow-y-auto flex-grow" style={{ maxHeight: "calc(90vh - 140px)" }}>
          <form id="cluster-form" onSubmit={handleSubmit}>
            {/* Cluster Name and Device Filter */}
            <div className="flex gap-4 mb-6">
              <div className="flex-grow">
                <label className="block text-sm font-medium mb-2">Cluster Name</label>
                <input
                  type="text"
                  placeholder="Enter Name"
                  value={clusterName}
                  onChange={(e) => setClusterName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                  required
                />
              </div>

              <div className="w-1/3" ref={deviceDropdownRef}>
                <label className="block text-sm font-medium mb-2">Device Filter</label>
                <div className="relative">
                  <button
                    type="button"
                    className="w-full px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-left flex items-center justify-between focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    onClick={() => setShowDeviceDropdown(!showDeviceDropdown)}
                  >
                    {deviceFilter}
                    <ChevronDown className="w-4 h-4" />
                  </button>

                  {showDeviceDropdown && (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-[8px] shadow-lg">
                      {deviceOptions.map((option) => (
                        <div
                          key={option}
                          className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            setDeviceFilter(option)
                            setShowDeviceDropdown(false)
                          }}
                        >
                          {option}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Country Filter with Radio Options */}
            <div className="mb-6">
              <label className="block text-sm font-medium mb-3">Country Filter (Multi-Select):</label>

              {/* Radio button options */}
              <div className="flex gap-6 mb-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="countryFilterType"
                    value="region"
                    checked={countryFilterType === "region"}
                    onChange={(e) => setCountryFilterType(e.target.value)}
                    className="mr-2 text-[#352090]"
                  />
                  By Region
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="countryFilterType"
                    value="continent"
                    checked={countryFilterType === "continent"}
                    onChange={(e) => setCountryFilterType(e.target.value)}
                    className="mr-2 text-[#352090]"
                  />
                  By Continent
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="countryFilterType"
                    value="country"
                    checked={countryFilterType === "country"}
                    onChange={(e) => setCountryFilterType(e.target.value)}
                    className="mr-2 text-[#352090]"
                  />
                  By Country
                </label>
              </div>

              {/* Location dropdown */}
              <div className="relative" ref={locationDropdownRef}>
                <button
                  type="button"
                  className="w-full px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-left flex items-center justify-between focus:outline-none focus:ring-1 focus:ring-[#352090]"
                  onClick={() => setShowLocationDropdown(!showLocationDropdown)}
                >
                  {selectedItems.length > 0
                    ? `${selectedItems.length} ${countryFilterType}${selectedItems.length > 1 ? "s" : ""} selected`
                    : `Select ${countryFilterType === "region" ? "Region" : countryFilterType === "continent" ? "Continent" : "Countries"}`}
                  <ChevronDown className="w-4 h-4" />
                </button>

                {showLocationDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-[8px] shadow-lg max-h-60 overflow-y-auto">
                    <div className="sticky top-0 bg-white p-2 border-b">
                      <input
                        type="text"
                        placeholder={`Search ${countryFilterType}s...`}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-[8px] focus:outline-none"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>

                    <div className="p-2 border-b">
                      <div className="flex items-center px-2 py-1">
                        <input
                          type="checkbox"
                          id="select-all-locations"
                          checked={selectedItems.length === getLocationOptions().length}
                          onChange={() => {
                            if (selectedItems.length === getLocationOptions().length) {
                              setSelectedItems([])
                            } else {
                              setSelectedItems(getLocationOptions().map((option) => option.code))
                            }
                          }}
                          className="mr-2"
                        />
                        <label htmlFor="select-all-locations" className="cursor-pointer">
                          Select All{" "}
                          {countryFilterType === "region"
                            ? "Regions"
                            : countryFilterType === "continent"
                              ? "Continents"
                              : "Countries"}
                        </label>
                      </div>
                    </div>

                    <div className="p-2">
                      {filteredLocationOptions.map((option) => (
                        <div key={option.code} className="flex items-center px-2 py-1">
                          <input
                            type="checkbox"
                            id={`location-${option.code}`}
                            checked={selectedItems.includes(option.code)}
                            onChange={() => toggleLocationItem(option.code)}
                            className="mr-2"
                          />
                          <label htmlFor={`location-${option.code}`} className="cursor-pointer">
                            {option.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Selected items display */}
              {selectedItems.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-2">
                  {selectedItems.slice(0, 3).map((code) => (
                    <div key={code} className="bg-gray-100 px-2 py-1 rounded-[8px] text-sm flex items-center">
                      {getLocationName(code)}
                      <button
                        type="button"
                        onClick={() => toggleLocationItem(code)}
                        className="ml-1 text-gray-500 hover:text-gray-700"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                  {selectedItems.length > 3 && (
                    <div className="bg-gray-100 px-2 py-1 rounded-[8px] text-sm">+{selectedItems.length - 3} more</div>
                  )}
                </div>
              )}
            </div>

            {/* Pilar Page */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-[#352090] mb-2">Pillar Page</h3>
              <div className="relative">
                <label className="block text-sm mb-1">Link URL</label>
                <input
                  type="url"
                  value={pilarPageUrl}
                  onChange={(e) => handlePilarPageChange(e.target.value)}
                  placeholder="https://example.com"
                  className={`w-full px-3 py-2 border ${
                    errors.pilarPage ? "border-red-500" : "border-gray-300"
                  } rounded-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                  required
                />
                {errors.pilarPage && (
                  <div className="flex items-center gap-1 mt-1">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <p className="text-red-500 text-xs">{errors.pilarPage}</p>
                  </div>
                )}
                <p className="text-gray-500 text-xs mt-1">
                  URL must be from domain: {displayDomain.replace("sc-domain:", "")}
                </p>
              </div>
            </div>

            {/* Supporting Pages */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-[#352090] mb-2">Supporting pages</h3>
              <div className="space-y-3">
                {supportingPages.map((page, index) => (
                  <div key={index} className="relative">
                    <label className="block text-sm mb-1">Link URL</label>
                    <div className="flex items-center gap-2">
                      <div className="relative flex-grow">
                        <input
                          type="url"
                          value={page.url}
                          onChange={(e) => handleSupportingPageChange(index, e.target.value)}
                          placeholder="https://example.com"
                          className={`w-full px-3 py-2 border ${
                            errors[`supporting_${index}`] ? "border-red-500" : "border-gray-300"
                          } rounded-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                        />
                        {errors[`supporting_${index}`] && (
                          <div className="flex items-center gap-1 mt-1">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <p className="text-red-500 text-xs">{errors[`supporting_${index}`]}</p>
                          </div>
                        )}
                        <p className="text-gray-500 text-xs mt-1">
                          URL must be from domain: {displayDomain.replace("sc-domain:", "")}
                        </p>
                      </div>
                      {index > 0 && (
                        <button
                          type="button"
                          onClick={() => handleRemoveSupportingPage(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              <button
                type="button"
                onClick={handleAddSupportingPage}
                className="text-blue-500 text-sm hover:text-blue-700 mt-3 flex items-center justify-end w-full"
              >
                Add More links
              </button>
            </div>
          </form>
        </div>

        {/* Action Buttons */}
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3 bg-white rounded-b-[8px]">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-[8px] hover:bg-gray-50"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            form="cluster-form"
            disabled={Object.keys(errors).length > 0 || !pilarPageUrl.trim() || isSubmitting}
            className="px-4 py-2 bg-[#352090] text-white rounded-[8px] hover:bg-[#2a1a70] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Creating...
              </>
            ) : (
              "Submit"
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default CreateClusterModal
