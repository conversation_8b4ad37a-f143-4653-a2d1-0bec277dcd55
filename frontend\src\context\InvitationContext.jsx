import { createContext, useContext, useState, useEffect, useCallback } from "react"
import { useNavigate } from "react-router-dom"

const TEAM_SERVICE_URL = import.meta.env.VITE_TEAM_SERVICE

// Context to share invitation details
const InvitationContext = createContext()

export const InvitationProvider = ({ children }) => {
  const [invitationData, setInvitationData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isExistingUser, setIsExistingUser] = useState(false)

  const navigate = useNavigate()

  const validateInvitation = useCallback(async () => {
    try {
      // 1. get token from URL
      const params = new URLSearchParams(window.location.search)
      const token = params.get("token")
      if (!token) throw new Error("No invitation token found")

      // 2. verify with backend
      const verifyRes = await fetch(`${TEAM_SERVICE_URL}/invite/verify/${token}`)
      if (!verifyRes.ok) {
        const err = await verifyRes.json()
        throw new Error(err.detail || "Invalid invitation token")
      }
      const { status, invitation } = await verifyRes.json()
      if (status !== "success") throw new Error("Invitation verification failed")

      // 3. save data
      setInvitationData({ ...invitation, token })
      const existing = Boolean(invitation.is_existing_user)
      setIsExistingUser(existing)

      // 4. existing user: auto-accept & redirect to login
      if (existing) {
        const acceptRes = await fetch(
          `${TEAM_SERVICE_URL}/invite/accept/${token}`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({})
          }
        )
        if (!acceptRes.ok) {
          const err = await acceptRes.json()
          throw new Error(err.detail || "Failed to accept invitation")
        }
        // now send them to login with a message
        navigate("/login", {
          state: {
            message: "You’ve been added to a workspace—please log in to continue.",
            invitationToken: token
          }
        })
        return
      }

      // 5. new user → render children/form
    }
    catch (err) {
      console.error("Invitation flow error:", err)
      setError(err.message)
    }
    finally {
      setLoading(false)
    }
  }, [navigate])

  useEffect(() => {
    validateInvitation()
  }, [validateInvitation])

  // while verifying...
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#352090] mx-auto mb-4" />
          <p className="text-gray-600">Processing invitation...</p>
        </div>
      </div>
    )
  }

  // on error
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 max-w-md">
          <div className="text-red-600 mb-2">
            <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">Invitation Error</h3>
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    )
  }

  // new users: expose invitationData & render form children
  return (
    <InvitationContext.Provider value={{ invitationData, isExistingUser }}>
      {children}
    </InvitationContext.Provider>
  )
}

// Hook for consuming the invitation context
export const useInvitation = () => {
  const ctx = useContext(InvitationContext)
  if (!ctx) throw new Error("useInvitation must be used within InvitationProvider")
  return ctx
}
