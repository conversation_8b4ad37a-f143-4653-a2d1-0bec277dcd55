import { apiSlice } from "./apiSlice"
import {
  setDeletedClusters,
  setDeletedLinks,
  clusterRestoredFromTrash,
  linkRestoredFromTrash,
  clusterPermanentlyDeleted,
  linkPermanentlyDeleted,
  addRestoringItem,
  removeRestoringItem,
  addDeletingItem,
  removeDeletingItem,
} from "../slices/trashSlice"
import { addNotification } from "../slices/uiSlice"

const CLUSTERGAZER_SERVICE_URL = import.meta.env.VITE_CLUSTERGAZER_SERVICE || "http://localhost:8001"

export const trashApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get deleted clusters
    getDeletedClusters: builder.query({
      query: (workspaceId) => ({
        url: `/clusters/workspace/${workspaceId}/deleted-clusters`,
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      extraOptions:{
        keepUnusedDataFor: 0, // Keep cached for 5 minutes
      },
      providesTags: [{ type: "DeletedCluster", id: "LIST" }],
      async onQueryStarted(workspaceId, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          const formattedClusters = data.map((cluster) => {
            const deletedAtDate = new Date(cluster.deleted_at)
            const permanentDeletionDate = new Date(deletedAtDate)
            permanentDeletionDate.setDate(permanentDeletionDate.getDate() + 30)

            return {
              id: cluster._id,
              name: cluster.clusterName,
              deviceFilter: cluster.deviceFilter,
              countryFilter: cluster.countryFilter,
              dateDeleted: deletedAtDate.toLocaleDateString(),
              permanentDeletionDate: permanentDeletionDate.toLocaleDateString(),
              daysLeft: Math.ceil((permanentDeletionDate - new Date()) / (1000 * 60 * 60 * 24)),
            }
          })
          dispatch(setDeletedClusters(formattedClusters))
        } catch (error) {
          console.error("Error fetching deleted clusters:", error)
        }
      },
    }),

    // Get deleted links
    getDeletedLinks: builder.query({
      query: (workspaceId) => ({
        url: `/clusters/workspace/${workspaceId}/deleted-links`,
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      providesTags: [{ type: "DeletedLink", id: "LIST" }],
      extraOptions:{

         refetchOnMountOrArgChange: true ,
      },
      async onQueryStarted(workspaceId, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          const formattedLinks = data.map((link) => {
            const deletedAtDate = new Date(link.deleted_at)
            const permanentDeletionDate = new Date(deletedAtDate)
            permanentDeletionDate.setDate(permanentDeletionDate.getDate() + 30)

            return {
              id: link._id,
              url: link.url,
              clusterId: link.cluster_id,
              clusterName: link.cluster_name,
              is_pillar: link.is_pillar,
              status: link.status || "inactive",
              dateDeleted: deletedAtDate.toLocaleDateString(),
              permanentDeletionDate: permanentDeletionDate.toLocaleDateString(),
              daysLeft: Math.ceil((permanentDeletionDate - new Date()) / (1000 * 60 * 60 * 24)),
            }
          })
          dispatch(setDeletedLinks(formattedLinks))
        } catch (error) {
          console.error("Error fetching deleted links:", error)
        }
      },
    }),

    // Restore cluster - SIMPLIFIED APPROACH
    restoreCluster: builder.mutation({
      query: ({ clusterId, workspaceId }) => ({
        url: `/clusters/${clusterId}/recover`,
        method: "POST",
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      // Only invalidate trash data, NOT clusters data
      invalidatesTags: [{ type: "DeletedCluster", id: "LIST" }],
      async onQueryStarted({ clusterId, workspaceId }, { dispatch, queryFulfilled }) {
        dispatch(addRestoringItem(clusterId))

        try {
          await queryFulfilled

          console.log(`Cluster ${clusterId} restore API call successful`)

          // ONLY update trash page immediately (remove from trash)
          dispatch(clusterRestoredFromTrash(clusterId))

          // Show success notification
          dispatch(
            addNotification({
              type: "success",
              title: "Cluster Restored",
              message: "Cluster has been restored successfully! Navigate to Overview to see it.",
            }),
          )

          console.log(
            `Cluster ${clusterId} removed from trash. Will appear in clusters when user navigates to Overview.`,
          )
        } catch (error) {
          console.error("Error restoring cluster:", error)
          dispatch(
            addNotification({
              type: "error",
              title: "Restore Failed",
              message: error.data?.detail || "Failed to restore cluster. Please try again.",
            }),
          )
        } finally {
          dispatch(removeRestoringItem(clusterId))
        }
      },
    }),

    // Restore link - SIMPLIFIED APPROACH
    restoreLink: builder.mutation({
      query: ({ clusterId, linkId, workspaceId }) => ({
        url: `/clusters/${clusterId}/links/${linkId}/recover`,
        method: "POST",
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      // Only invalidate trash data, NOT links data
      invalidatesTags: [{ type: "DeletedLink", id: "LIST" }],
      async onQueryStarted({ clusterId, linkId, workspaceId }, { dispatch, queryFulfilled }) {
        dispatch(addRestoringItem(linkId))

        try {
          await queryFulfilled

          // ONLY update trash page immediately (remove from trash)
          dispatch(linkRestoredFromTrash(linkId))

          dispatch(
            addNotification({
              type: "success",
              title: "Link Restored",
              message: "Link has been restored successfully! Navigate to cluster links to see it.",
            }),
          )

          console.log(`Link ${linkId} removed from trash. Will appear in links when user navigates to cluster.`)
        } catch (error) {
          console.error("Error restoring link:", error)
          dispatch(
            addNotification({
              type: "error",
              title: "Restore Failed",
              message: error.data?.detail || "Failed to restore link. Please try again.",
            }),
          )
        } finally {
          dispatch(removeRestoringItem(linkId))
        }
      },
    }),

    // Permanently delete cluster
    permanentlyDeleteCluster: builder.mutation({
      query: ({ clusterId, workspaceId }) => ({
        url: `/clusters/${clusterId}/permanent`,
        method: "DELETE",
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      invalidatesTags: [{ type: "DeletedCluster", id: "LIST" }],
      async onQueryStarted({ clusterId }, { dispatch, queryFulfilled }) {
        dispatch(addDeletingItem(clusterId))

        try {
          await queryFulfilled
          dispatch(clusterPermanentlyDeleted(clusterId))

          dispatch(
            addNotification({
              type: "success",
              title: "Cluster Deleted",
              message: "Cluster has been permanently deleted.",
            }),
          )

          console.log(`Cluster ${clusterId} permanently deleted`)
        } catch (error) {
          console.error("Error permanently deleting cluster:", error)
          dispatch(
            addNotification({
              type: "error",
              title: "Deletion Failed",
              message: error.data?.detail || "Failed to permanently delete cluster. Please try again.",
            }),
          )
        } finally {
          dispatch(removeDeletingItem(clusterId))
        }
      },
    }),

    // Permanently delete link
    permanentlyDeleteLink: builder.mutation({
      query: ({ clusterId, linkId, workspaceId }) => ({
        url: `/clusters/${clusterId}/links/${linkId}/permanent`,
        method: "DELETE",
        baseUrl: CLUSTERGAZER_SERVICE_URL,
      }),
      invalidatesTags: [{ type: "DeletedLink", id: "LIST" }],
      async onQueryStarted({ linkId }, { dispatch, queryFulfilled }) {
        dispatch(addDeletingItem(linkId))

        try {
          await queryFulfilled
          dispatch(linkPermanentlyDeleted(linkId))

          dispatch(
            addNotification({
              type: "success",
              title: "Link Deleted",
              message: "Link has been permanently deleted.",
            }),
          )

          console.log(`Link ${linkId} permanently deleted`)
        } catch (error) {
          console.error("Error permanently deleting link:", error)
          dispatch(
            addNotification({
              type: "error",
              title: "Deletion Failed",
              message: error.data?.detail || "Failed to permanently delete link. Please try again.",
            }),
          )
        } finally {
          dispatch(removeDeletingItem(linkId))
        }
      },
    }),
  }),
})

export const {
  useGetDeletedClustersQuery,
  useGetDeletedLinksQuery,
  useRestoreClusterMutation,
  useRestoreLinkMutation,
  usePermanentlyDeleteClusterMutation,
  usePermanentlyDeleteLinkMutation,
} = trashApi
