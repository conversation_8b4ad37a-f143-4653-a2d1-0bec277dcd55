import { createSlice } from "@reduxjs/toolkit"

const initialState = {
  searchQuery: "",
  selectedCluster: null,
  filters: {
    device: "all",
    country: "all",
  },
  ui: {
    isCreateModalOpen: false,
    isDeleteModalOpen: false,
    clusterToDelete: null,
  },
  // 🔑 KEY FIX: Workspace-specific state management
  // Store clusters by workspace ID for proper isolation
  clustersByWorkspace: {},
  // Global processing clusters (workspace-agnostic)
  processingClusters: [],
}

const clusterSlice = createSlice({
  name: "cluster",
  initialState,
  reducers: {
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload
    },
    setSelectedCluster: (state, action) => {
      state.selectedCluster = action.payload
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    openCreateModal: (state) => {
      state.ui.isCreateModalOpen = true
    },
    closeCreateModal: (state) => {
      state.ui.isCreateModalOpen = false
    },
    openDeleteModal: (state, action) => {
      state.ui.isDeleteModalOpen = true
      state.ui.clusterToDelete = action.payload
    },
    closeDeleteModal: (state) => {
      state.ui.isDeleteModalOpen = false
      state.ui.clusterToDelete = null
    },
    clearFilters: (state) => {
      state.filters = initialState.filters
      state.searchQuery = ""
    },
    // 🔑 KEY FIX: Workspace-specific cluster management
    setWorkspaceClusters: (state, action) => {
      const { workspaceId, clusters } = action.payload
      state.clustersByWorkspace[workspaceId] = clusters
    },
    addWorkspaceCluster: (state, action) => {
      const { workspaceId, cluster } = action.payload
      if (!state.clustersByWorkspace[workspaceId]) {
        state.clustersByWorkspace[workspaceId] = []
      }
      state.clustersByWorkspace[workspaceId].push(cluster)
    },
    removeWorkspaceCluster: (state, action) => {
      const { workspaceId, clusterId } = action.payload
      if (state.clustersByWorkspace[workspaceId]) {
        state.clustersByWorkspace[workspaceId] = state.clustersByWorkspace[workspaceId].filter(
          (cluster) => cluster._id !== clusterId
        )
      }
    },
    updateWorkspaceCluster: (state, action) => {
      const { workspaceId, clusterId, updates } = action.payload
      if (state.clustersByWorkspace[workspaceId]) {
        const index = state.clustersByWorkspace[workspaceId].findIndex(
          (cluster) => cluster._id === clusterId
        )
        if (index !== -1) {
          state.clustersByWorkspace[workspaceId][index] = {
            ...state.clustersByWorkspace[workspaceId][index],
            ...updates,
          }
        }
      }
    },
    // Clear workspace clusters (for workspace switching)
    clearWorkspaceClusters: (state, action) => {
      const workspaceId = action.payload
      delete state.clustersByWorkspace[workspaceId]
    },
    // Processing clusters management (workspace-agnostic)
    addProcessingCluster: (state, action) => {
      const clusterId = action.payload
      if (!state.processingClusters.includes(clusterId)) {
        state.processingClusters.push(clusterId)
      }
    },
    removeProcessingCluster: (state, action) => {
      const clusterId = action.payload
      state.processingClusters = state.processingClusters.filter((id) => id !== clusterId)
    },
    clearAllProcessingClusters: (state) => {
      state.processingClusters = []
    },
    // Legacy support (for backward compatibility)
    setActiveClusters: (state, action) => {
      // This is now deprecated - use setWorkspaceClusters instead
      console.warn("setActiveClusters is deprecated. Use setWorkspaceClusters instead.")
    },
    clusterCreated: (state, action) => {
      // This is now deprecated - use addWorkspaceCluster instead
      console.warn("clusterCreated is deprecated. Use addWorkspaceCluster instead.")
    },
    clusterDeleted: (state, action) => {
      // This is now deprecated - use removeWorkspaceCluster instead
      console.warn("clusterDeleted is deprecated. Use removeWorkspaceCluster instead.")
    },
    clusterMovedToTrash: (state, action) => {
      const cluster = action.payload
      // Remove from all workspaces if it exists
      Object.keys(state.clustersByWorkspace).forEach((workspaceId) => {
        state.clustersByWorkspace[workspaceId] = state.clustersByWorkspace[workspaceId].filter(
          (c) => c._id !== cluster._id
        )
      })
      // Remove from processing if it was there
      state.processingClusters = state.processingClusters.filter((id) => id !== cluster._id)
    },
    setProcessingClusters: (state, action) => {
      state.processingClusters = action.payload
    },
  },
})

export const {
  setSearchQuery,
  setSelectedCluster,
  setFilters,
  openCreateModal,
  closeCreateModal,
  openDeleteModal,
  closeDeleteModal,
  clearFilters,
  // New workspace-specific actions
  setWorkspaceClusters,
  addWorkspaceCluster,
  removeWorkspaceCluster,
  updateWorkspaceCluster,
  clearWorkspaceClusters,
  // Processing clusters actions
  addProcessingCluster,
  removeProcessingCluster,
  clearAllProcessingClusters,
  // Legacy actions (for backward compatibility)
  setActiveClusters,
  clusterCreated,
  clusterDeleted,
  clusterMovedToTrash,
  setProcessingClusters,
} = clusterSlice.actions

// 🔑 KEY FIX: Selectors for workspace-specific data
export const selectClustersByWorkspace = (state) => 
  state.cluster.clustersByWorkspace

export const selectProcessingClusters = (state) => 
  state.cluster.processingClusters

export const selectClusterFilters = (state) => 
  state.cluster.filters

export const selectClusterSearchQuery = (state) => 
  state.cluster.searchQuery

export const selectClusterUI = (state) => 
  state.cluster.ui

export default clusterSlice.reducer
