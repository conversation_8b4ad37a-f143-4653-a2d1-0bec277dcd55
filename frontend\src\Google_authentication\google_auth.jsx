import { useState } from "react"
import { useParams } from "react-router-dom"
import { useDispatch } from "react-redux"
import { useWorkspace } from "../hooks/useWorkspace"
import { addNotification } from "../store/slices/uiSlice"
import Navbar from "../components/navbar"
import ConnectionSVG from "../components/connection-svg"

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE || "http://localhost:8000"

const GoogleAuth = () => {
  const { workspaceId } = useParams()
  const dispatch = useDispatch()
  const { currentWorkspace } = useWorkspace()
  const [errorMessage, setErrorMessage] = useState(null)

  // Use workspaceId from params or current workspace
  const activeWorkspaceId = workspaceId || currentWorkspace

  // Handle OAuth authentication redirection
  const handleGoogleAuth = () => {
    if (!activeWorkspaceId) {
      setErrorMessage("Workspace ID is missing")
      dispatch(
        addNotification({
          type: "error",
          title: "Workspace Error",
          message: "Workspace ID is missing. Please try again.",
        }),
      )
      return
    }

    try {
      // Redirect to backend Google OAuth authorize route with workspace ID
      window.location.href = `${USER_SERVICE_URL}/google_auth/authorize/${activeWorkspaceId}`
    } catch (error) {
      console.error("Error initiating Google Auth:", error)
      setErrorMessage("Failed to initiate Google authentication")
      dispatch(
        addNotification({
          type: "error",
          title: "Authentication Error",
          message: "Failed to initiate Google authentication. Please try again.",
        }),
      )
    }
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Navbar */}
      <Navbar />

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-6 py-12 text-center mt-20">
        {/* Welcome Text */}
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome to <span className="text-purple-600">Google Search Console Tracker</span>
        </h1>
        <p className="text-gray-500 text-[14px] font-normal leading-5 mt-2">
          Track your website's performance with ease!
        </p>

        {/* Connection Image */}
        <div className="flex justify-center my-12">
          <ConnectionSVG />
        </div>

        {/* Call to Action */}
        <p className="text-[30px] font-semibold leading-[45px] text-gray-700 max-w-2xl mx-auto">
          Welcome to your SEO insights hub! Let's get started by connecting your Google Search Console for real-time
          data tracking.
        </p>
        <button
          onClick={handleGoogleAuth}
          disabled={!activeWorkspaceId}
          className="mt-6 bg-[#352090] text-white px-6 py-3 rounded-[12px] hover:bg-[#2a1a70] transition disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Connect now
        </button>

        {/* Error Message */}
        {errorMessage && <div className="mt-4 p-4 bg-red-50 rounded-lg text-red-600">{errorMessage}</div>}

        {/* Features Section */}
        <div className="mt-12 text-left max-w-2xl mx-auto">
          <div className="flex items-start space-x-3 mb-4">
            <span className="text-purple-600">✔️</span>
            <div>
              <h3 className="font-bold text-gray-900">Permanent Connection</h3>
              <p className="text-gray-600 text-sm">
                Your Google Search Console connection will remain active indefinitely, allowing continuous monitoring of
                your website's performance.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-purple-600">✔️</span>
            <div>
              <h3 className="font-bold text-gray-900">Workspace-Specific</h3>
              <p className="text-gray-600 text-sm">
                Each workspace can have its own Google Search Console connection, allowing you to manage multiple
                websites independently.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default GoogleAuth
