const ConnectionComponent = ({ width = 245, height = 53 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 245 53"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="transition-transform hover:scale-105"
    >
      {/* Bidirectional Arrow */}
      <g className="connection-arrow">
        <path
          d="M92.5 26L102.5 31.7735V20.2265L92.5 26ZM153.5 26L143.5 20.2265V31.7735L153.5 26ZM101.5 27H144.5V25H101.5V27Z"
          fill="black"
        />
      </g>

      {/* App Logo (Left Side) */}
      <g clipPath="url(#clip0_app_logo)">
        {/* Cyan/Teal Component */}
        <path
          d="M26.2066 51.9377V51.2515C26.2066 41.8532 26.2066 32.4601 26.2066 23.0722C26.1708 22.8771 26.1984 22.6757 26.2854 22.4975C26.3724 22.3193 26.5142 22.1737 26.69 22.0819C30.5887 19.8597 34.4873 17.6063 38.3859 15.3685L38.8148 15.158C38.8589 15.3371 38.8875 15.5197 38.9006 15.7038C38.9006 25.1852 38.9006 34.6615 38.9006 44.1326C38.8974 44.2939 38.8596 44.4525 38.7896 44.5979C38.7197 44.7432 38.6192 44.8717 38.4951 44.9747C34.5185 47.3139 30.5263 49.5829 26.5419 51.8753C26.4324 51.907 26.3201 51.9279 26.2066 51.9377Z"
          fill="#00CEFF"
          className="transition-all duration-300 hover:fill-[#00d8ff]"
        />

        {/* Purple Component */}
        <path
          d="M51.7505 7.15011L35.2904 16.6316C32.382 18.308 29.4737 19.9922 26.5497 21.6374C26.3973 21.718 26.2294 21.7652 26.0574 21.7759C25.8853 21.7867 25.7129 21.7607 25.5516 21.6998C21.653 19.4932 17.7544 17.2398 13.8557 14.9942C13.7232 14.9162 13.5906 14.8226 13.3645 14.6667L15.6569 13.3333C23.241 8.96688 30.8174 4.6004 38.3859 0.233933C38.5852 0.0874784 38.825 0.0065593 39.0723 0.00236867C39.3195 -0.00182197 39.562 0.0709229 39.7661 0.210541C43.4854 2.38598 47.2203 4.52243 50.9551 6.66668C51.1891 6.79923 51.4074 6.94738 51.7505 7.15011Z"
          fill="#AD66FF"
          className="transition-all duration-300 hover:fill-[#b975ff]"
        />

        {/* Dark Purple Components */}
        <g className="transition-all duration-300 hover:opacity-90">
          <path
            d="M25.6842 36.8968L20.1247 33.6921C13.6478 29.9442 7.17086 26.2197 0.693927 22.5186C0.46754 22.4157 0.278954 22.2447 0.154532 22.0294C0.0301099 21.8141 -0.0239291 21.5653 -3.05027e-05 21.3178C-3.05027e-05 17.0137 -3.05027e-05 12.7408 -3.05027e-05 8.39774V7.54004L2.89276 9.18526L25.1384 21.9884C25.497 22.1989 25.7777 22.3704 25.7777 22.8929C25.7777 27.4387 25.7777 31.9923 25.7777 36.5381C25.7568 36.6601 25.7255 36.7801 25.6842 36.8968Z"
            fill="#352090"
          />
          <path
            d="M51.9454 7.57886C51.9454 7.94533 51.9922 8.20264 51.9922 8.45995C51.9922 12.764 51.9922 17.0369 51.9922 21.3644C51.9906 21.5509 51.9495 21.7348 51.8715 21.9042C51.7936 22.0736 51.6805 22.2244 51.54 22.3469C47.6413 24.6315 43.7427 26.8693 39.8441 29.1227C39.7349 29.1851 39.6101 29.2241 39.3762 29.3254V28.3742C39.3762 24.1013 39.3762 19.7972 39.3762 15.5477C39.3562 15.3128 39.4101 15.0776 39.5303 14.8749C39.6505 14.6722 39.8311 14.5121 40.0468 14.4171C43.7817 12.2962 47.501 10.1364 51.2281 7.99211L51.9454 7.57886Z"
            fill="#352090"
          />
          <path
            d="M13.0994 30.1675L15.6881 31.6412C18.807 33.4501 21.9259 35.2747 25.115 37.0993C25.3246 37.1951 25.5001 37.3522 25.6184 37.5499C25.7368 37.7476 25.7923 37.9765 25.7778 38.2065C25.7778 42.5573 25.7778 46.916 25.7778 51.2747C25.7778 51.4774 25.7778 51.6879 25.731 52.0076C24.2183 51.1421 22.7914 50.339 21.3723 49.5203C18.8148 48.0466 16.2573 46.5807 13.7232 45.0837C13.5757 45.003 13.4472 44.8916 13.3463 44.7572C13.2455 44.6227 13.1746 44.4681 13.1384 44.3039C13.0916 39.6724 13.0994 35.0174 13.0994 30.1675Z"
            fill="#352090"
          />
          <path
            d="M25.4815 7.17361L19.54 10.6044C17.5049 11.7818 15.4698 12.9436 13.4113 14.1054C13.2703 14.182 13.1143 14.2272 12.9542 14.2379C12.7941 14.2487 12.6335 14.2248 12.4834 14.1678C8.48344 11.9066 4.49904 9.60636 0.514634 7.31396C0.439543 7.245 0.373987 7.16634 0.319702 7.08004L6.93179 3.26718C8.73296 2.22234 10.5341 1.17751 12.3899 0.148269C12.6601 0.0174257 12.9709 -0.00217223 13.2554 0.0936877C17.2632 2.3393 21.2164 4.67848 25.1774 6.9241C25.2854 6.99884 25.3871 7.08229 25.4815 7.17361Z"
            fill="#352090"
          />
        </g>
      </g>

      {/* Google Logo (Right Side) */}
      <g className="google-logo transition-all duration-300 hover:opacity-90">
        {/* Google Logo - Yellow Part */}
        <g>
          <mask
            id="mask0_google"
            style={{ maskType: "luminance" }}
            maskUnits="userSpaceOnUse"
            x="194"
            y="0"
            width="51"
            height="53"
          >
            <path
              d="M244.407 21.3164H220.093V31.3825H234.088C232.784 37.7774 227.328 41.4485 220.093 41.4485C211.553 41.4485 204.674 34.5799 204.674 26.0534C204.674 17.5268 211.553 10.6582 220.093 10.6582C223.77 10.6582 227.091 11.9609 229.7 14.0925L237.291 6.51334C232.665 2.48691 226.735 0 220.093 0C205.623 0 194 11.6056 194 26.0534C194 40.5011 205.623 52.1067 220.093 52.1067C233.14 52.1067 245 42.6328 245 26.0534C245 24.5138 244.763 22.8559 244.407 21.3164Z"
              fill="white"
            />
          </mask>
          <g mask="url(#mask0_google)">
            <path d="M191.628 41.4485V10.6582L211.791 26.0534L191.628 41.4485Z" fill="#FBBC05" />
          </g>
        </g>

        {/* Google Logo - Red Part */}
        <g>
          <mask
            id="mask1_google"
            style={{ maskType: "luminance" }}
            maskUnits="userSpaceOnUse"
            x="194"
            y="0"
            width="51"
            height="53"
          >
            <path
              d="M244.407 21.3164H220.093V31.3825H234.088C232.784 37.7774 227.328 41.4485 220.093 41.4485C211.553 41.4485 204.674 34.5799 204.674 26.0534C204.674 17.5268 211.553 10.6582 220.093 10.6582C223.77 10.6582 227.091 11.9609 229.7 14.0925L237.291 6.51334C232.665 2.48691 226.735 0 220.093 0C205.623 0 194 11.6056 194 26.0534C194 40.5011 205.623 52.1067 220.093 52.1067C233.14 52.1067 245 42.6328 245 26.0534C245 24.5138 244.763 22.8559 244.407 21.3164Z"
              fill="white"
            />
          </mask>
          <g mask="url(#mask1_google)">
            <path
              d="M191.628 10.658L211.791 26.0532L220.093 18.8293L248.558 14.2108V-2.36865H191.628V10.658Z"
              fill="#EA4335"
            />
          </g>
        </g>

        {/* Google Logo - Green Part */}
        <g>
          <mask
            id="mask2_google"
            style={{ maskType: "luminance" }}
            maskUnits="userSpaceOnUse"
            x="194"
            y="0"
            width="51"
            height="53"
          >
            <path
              d="M244.407 21.3164H220.093V31.3825H234.088C232.784 37.7774 227.328 41.4485 220.093 41.4485C211.553 41.4485 204.674 34.5799 204.674 26.0534C204.674 17.5268 211.553 10.6582 220.093 10.6582C223.77 10.6582 227.091 11.9609 229.7 14.0925L237.291 6.51334C232.665 2.48691 226.735 0 220.093 0C205.623 0 194 11.6056 194 26.0534C194 40.5011 205.623 52.1067 220.093 52.1067C233.14 52.1067 245 42.6328 245 26.0534C245 24.5138 244.763 22.8559 244.407 21.3164Z"
              fill="white"
            />
          </mask>
          <g mask="url(#mask2_google)">
            <path
              d="M191.628 41.4484L227.209 14.2108L236.579 15.395L248.558 -2.36865V54.4751H191.628V41.4484Z"
              fill="#34A853"
            />
          </g>
        </g>

        {/* Google Logo - Blue Part */}
        <g>
          <mask
            id="mask3_google"
            style={{ maskType: "luminance" }}
            maskUnits="userSpaceOnUse"
            x="194"
            y="0"
            width="51"
            height="53"
          >
            <path
              d="M244.407 21.3164H220.093V31.3825H234.088C232.784 37.7774 227.328 41.4485 220.093 41.4485C211.553 41.4485 204.674 34.5799 204.674 26.0534C204.674 17.5268 211.553 10.6582 220.093 10.6582C223.77 10.6582 227.091 11.9609 229.7 14.0925L237.291 6.51334C232.665 2.48691 226.735 0 220.093 0C205.623 0 194 11.6056 194 26.0534C194 40.5011 205.623 52.1067 220.093 52.1067C233.14 52.1067 245 42.6328 245 26.0534C245 24.5138 244.763 22.8559 244.407 21.3164Z"
              fill="white"
            />
          </mask>
          <g mask="url(#mask3_google)">
            <path d="M248.558 54.4752L211.791 26.0534L207.047 22.5006L248.558 10.6582V54.4752Z" fill="#4285F4" />
          </g>
        </g>
      </g>

      {/* Clip Paths */}
      <defs>
        <clipPath id="clip0_app_logo">
          <rect width="52" height="52" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default ConnectionComponent;

