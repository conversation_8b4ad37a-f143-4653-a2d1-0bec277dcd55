import { useEffect, useState } from "react"
import { useNavigation } from "../hooks/useNavigation"
import { useWorkspace } from "../hooks/useWorkspace"
import { useLocation } from "react-router-dom"

/**
 * NavigationTest component for validating navigation improvements
 * This component helps test and debug navigation behavior
 */
const NavigationTest = () => {
  const location = useLocation()
  const { currentWorkspace, isSwitching } = useWorkspace()
  const { 
    currentPage, 
    isNavigating, 
    navigationType,
    navigateToOverview,
    navigateToClusters,
    navigateToTrash,
    navigateToSettings
  } = useNavigation()
  
  const [navigationLog, setNavigationLog] = useState([])
  const [renderCount, setRenderCount] = useState(0)
  
  // Track component renders
  useEffect(() => {
    setRenderCount(prev => prev + 1)
  })
  
  // Log navigation changes
  useEffect(() => {
    const logEntry = {
      timestamp: new Date().toISOString(),
      currentPage,
      navigationType,
      isNavigating,
      isSwitching,
      pathname: location.pathname,
      workspaceId: currentWorkspace?.id || 'none',
      renderCount
    }
    
    setNavigationLog(prev => [...prev.slice(-9), logEntry]) // Keep last 10 entries
    
    console.log("🧪 Navigation Test Log:", logEntry)
  }, [currentPage, navigationType, isNavigating, isSwitching, location.pathname, currentWorkspace?.id, renderCount])
  
  const testIntraWorkspaceNavigation = () => {
    console.log("🧪 Testing intra-workspace navigation...")
    
    // Test sequence: Overview -> Clusters -> Trash -> Settings -> Overview
    setTimeout(() => {
      console.log("🧪 Step 1: Navigate to Overview")
      navigateToOverview()
    }, 100)
    
    setTimeout(() => {
      console.log("🧪 Step 2: Navigate to Clusters")
      navigateToClusters()
    }, 1000)
    
    setTimeout(() => {
      console.log("🧪 Step 3: Navigate to Trash")
      navigateToTrash()
    }, 2000)
    
    setTimeout(() => {
      console.log("🧪 Step 4: Navigate to Settings")
      navigateToSettings()
    }, 3000)
    
    setTimeout(() => {
      console.log("🧪 Step 5: Navigate back to Overview")
      navigateToOverview()
    }, 4000)
  }
  
  const clearLog = () => {
    setNavigationLog([])
    setRenderCount(0)
  }
  
  if (!currentWorkspace) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">
        <h3 className="font-bold text-yellow-800">Navigation Test</h3>
        <p className="text-yellow-700">No workspace available for testing</p>
      </div>
    )
  }
  
  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-gray-800">Navigation Test Panel</h3>
        <button 
          onClick={clearLog}
          className="text-xs bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded"
        >
          Clear
        </button>
      </div>
      
      {/* Current State */}
      <div className="mb-3 p-2 bg-gray-50 rounded text-xs">
        <div><strong>Workspace:</strong> {currentWorkspace.id}</div>
        <div><strong>Current Page:</strong> {currentPage}</div>
        <div><strong>Navigation Type:</strong> {navigationType || 'none'}</div>
        <div><strong>Is Navigating:</strong> {isNavigating ? 'Yes' : 'No'}</div>
        <div><strong>Is Switching:</strong> {isSwitching ? 'Yes' : 'No'}</div>
        <div><strong>Render Count:</strong> {renderCount}</div>
        <div><strong>Current Path:</strong> {location.pathname}</div>
      </div>
      
      {/* Test Controls */}
      <div className="mb-3">
        <button
          onClick={testIntraWorkspaceNavigation}
          disabled={isNavigating || isSwitching}
          className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white text-xs py-2 px-3 rounded mb-2"
        >
          Test Intra-Workspace Navigation
        </button>
        
        <div className="grid grid-cols-2 gap-1">
          <button
            onClick={navigateToOverview}
            disabled={isNavigating || isSwitching}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white text-xs py-1 px-2 rounded"
          >
            Overview
          </button>
          <button
            onClick={navigateToClusters}
            disabled={isNavigating || isSwitching}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white text-xs py-1 px-2 rounded"
          >
            Clusters
          </button>
          <button
            onClick={navigateToTrash}
            disabled={isNavigating || isSwitching}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white text-xs py-1 px-2 rounded"
          >
            Trash
          </button>
          <button
            onClick={navigateToSettings}
            disabled={isNavigating || isSwitching}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white text-xs py-1 px-2 rounded"
          >
            Settings
          </button>
        </div>
      </div>
      
      {/* Navigation Log */}
      <div>
        <h4 className="font-semibold text-xs mb-2">Navigation Log:</h4>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {navigationLog.map((entry, index) => (
            <div key={index} className="text-xs p-1 bg-gray-100 rounded">
              <div className="font-mono text-gray-600">
                {new Date(entry.timestamp).toLocaleTimeString()}
              </div>
              <div>
                <span className="font-semibold">{entry.currentPage}</span>
                {entry.navigationType && (
                  <span className="ml-1 text-blue-600">({entry.navigationType})</span>
                )}
                <span className="ml-1 text-gray-500">R:{entry.renderCount}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default NavigationTest
