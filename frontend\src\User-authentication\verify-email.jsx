import { useState, useEffect } from "react"
import { useNavigate, useSearchParams } from "react-router-dom"
import { useDispatch } from "react-redux"
import { useVerifyEmailMutation } from "../store/api/authApi"
import { setOnboardingState, setWorkspaceId, setCurrentStep } from "../store/slices/onboardingSlice"
import { addNotification } from "../store/slices/uiSlice"
import LogoComponent from "../components/logo-component"

const VerifyEmail = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [searchParams] = useSearchParams()
  const [verificationStatus, setVerificationStatus] = useState("verifying")
  const [verifyEmail] = useVerifyEmailMutation()

  useEffect(() => {
    const handleVerification = async () => {
      const token = searchParams.get("token")
      if (!token) {
        setVerificationStatus("error")
        dispatch(
          addNotification({
            type: "error",
            title: "Verification Error",
            message: "No verification token provided",
          }),
        )
        return
      }

      try {
        // Send token directly as string parameter
        const result = await verifyEmail(token).unwrap()
        console.log("Verification result:", result)

        // Store workspace ID and onboarding state
        if (result.workspace_id) {
          dispatch(setWorkspaceId(result.workspace_id))
        }

        if (result.onboarding_state) {
          dispatch(setOnboardingState(result.onboarding_state))
        }

        // Update onboarding step
        dispatch(setCurrentStep("team_invitation"))

        setVerificationStatus("success")

        dispatch(
          addNotification({
            type: "success",
            title: "Email Verified!",
            message: "Email verified and workspace created successfully!",
          }),
        )

        // Redirect to team invitation after a short delay
        setTimeout(() => {
          navigate(`/workspace/${result.workspace_id}/invite`, {
            state: {
              onboarding_state: result.onboarding_state,
              workspace_id: result.workspace_id,
            },
          })
        }, 2000)
      } catch (error) {
        console.error("Email verification failed:", error)
        setVerificationStatus("error")
        dispatch(
          addNotification({
            type: "error",
            title: "Verification Failed",
            message: error.data?.detail || "Email verification failed. Please try again.",
          }),
        )
      }
    }

    handleVerification()
  }, [searchParams, navigate, dispatch, verifyEmail])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <LogoComponent className="mx-auto h-12 w-auto" />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">Email Verification</h2>
        </div>

        <div className="mt-8 space-y-6">
          {verificationStatus === "verifying" && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-4 text-gray-600">Verifying your email and setting up your workspace...</p>
            </div>
          )}

          {verificationStatus === "success" && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="mt-4 text-gray-600">
                Email verified and workspace created successfully! Redirecting to team setup...
              </p>
            </div>
          )}

          {verificationStatus === "error" && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <p className="mt-4 text-red-600">
                Verification failed. Please check your email for a new verification link or contact support.
              </p>
              <button onClick={() => navigate("/login")} className="mt-4 text-[#352090] hover:underline">
                Return to Login
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default VerifyEmail
