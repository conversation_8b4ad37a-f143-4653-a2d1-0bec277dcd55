import { useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useDispatch } from "react-redux"
import { Home, Trash2, Settings, LogOut, Users } from "lucide-react"
import { useWorkspace } from "../hooks/useWorkspace"
import { useNavigation } from "../hooks/useNavigation"
import { addNotification } from "../store/slices/uiSlice"
import { clusterApi } from "../store/api/clusterApi"
import { useLogoutMutation } from "../store/api/authApi"
import { logout as logoutAction } from "../store/slices/authSlice"
import { authApi } from "../store/api/authApi"
import { resetNavigationState } from "../store/slices/navigationSlice"
import InviteUserModal from "../team_invitation/invite-user"

const Sidebar = ({ activePage = "overview" }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { currentWorkspace } = useWorkspace()
  const {
    currentPage,
    navigateToOverview,
    navigateToTrash,
    navigateToSettings,
    syncWithCurrentUrl,
    isPageActive,
    PAGES
  } = useNavigation()
  const [isInviteModalOpen, setInviteModalOpen] = useState(false)

  // Logout mutation
  const [logoutMutation] = useLogoutMutation()

  // Sync navigation state with current URL on mount and location changes
  useEffect(() => {
    syncWithCurrentUrl()
  }, [location.pathname, syncWithCurrentUrl])

  const openInviteModal = () => setInviteModalOpen(true)
  const closeInviteModal = () => setInviteModalOpen(false)

  const handleLogout = async () => {
    try {
      console.log("🔄 Starting logout process...")
      
      // Call the logout API
      await logoutMutation().unwrap()
      console.log("✅ Logout API call successful")
      
      // Clear Redux state
      dispatch(logoutAction())
      dispatch(resetNavigationState())
      console.log("✅ Redux state cleared")

      // Clear all RTK Query cache
      dispatch(clusterApi.util.resetApiState())
      dispatch(authApi.util.resetApiState())
      console.log("✅ API cache cleared")
      
      // Clear localStorage
      localStorage.clear()
      console.log("✅ LocalStorage cleared")
      
      // Show success notification
      dispatch(
        addNotification({
          type: "success",
          title: "Logged Out",
          message: "You have been logged out successfully",
        }),
      )
      
      // Navigate to login
      navigate("/login", { replace: true })
      console.log("✅ Navigated to login page")
      
    } catch (error) {
      console.error("❌ Logout failed:", error)
      
      // Even if API call fails, clear local state and redirect
      dispatch(logoutAction())
      dispatch(resetNavigationState())
      dispatch(clusterApi.util.resetApiState())
      dispatch(authApi.util.resetApiState())
      localStorage.clear()
      
      dispatch(
        addNotification({
          type: "error",
          title: "Logout Failed",
          message: "Failed to logout from server, but local session cleared.",
        }),
      )
      
      navigate("/login", { replace: true })
    }
  }

  // Optimized navigation handler using Redux navigation state
  const handleNavigation = (id) => {
    console.log("🚀 Sidebar Navigation Debug:", {
      id,
      currentWorkspace: currentWorkspace?.id || 'undefined',
      currentPath: location.pathname,
      navigationHookState: {
        currentPage,
        isNavigating,
        navigationType
      }
    })

    if (!currentWorkspace?.id) {
      console.error("❌ Sidebar Navigation Error: No workspace ID available", {
        currentWorkspace,
        location: location.pathname
      })
      // Don't return early - let the navigation hook handle the fallback
    }

    // Use optimized navigation functions for intra-workspace navigation
    switch (id) {
      case "overview":
        console.log("🔄 Sidebar: Calling navigateToOverview()")
        navigateToOverview()
        break
      case "trash":
        console.log("🔄 Sidebar: Calling navigateToTrash()")
        navigateToTrash()
        break
      case "settings":
        console.log("🔄 Sidebar: Calling navigateToSettings()")
        navigateToSettings()
        break
      default:
        console.warn("Unknown navigation id:", id)
    }
  }

  const menuItems = [
    { id: "overview", label: "Overview", icon: "/Overview.svg" },
    { id: "trash", label: "Trash", icon: "/Trash2.svg" },
    { id: "settings", label: "Settings", icon: "/setting.svg" },
    { id: "logout", label: "Logout", icon: "/Logout.svg", onClick: handleLogout },
  ]

  // Helper function to determine if a menu item is active
  const isMenuItemActive = (itemId) => {
    // Use Redux navigation state for more accurate active state detection
    return isPageActive(PAGES[itemId.toUpperCase()]) || activePage === itemId
  }

  return (
    <>
      <aside className="w-[200px] border-r border-gray-200 h-[calc(100vh-100px)] bg-white fixed top-[100px] left-0 z-20">
        <nav className="py-0">
          <ul className="space-y-1">
            {menuItems.map((item) => {
              const isActive = isMenuItemActive(item.id)
              return (
                <li key={item.id}>
                  {item.onClick ? (
                    <button
                      onClick={item.onClick}
                      className={`flex items-center px-6 py-3 text-sm w-full text-left ${
                        isActive
                          ? "text-[#352090] bg-[#F8F7FC] border-l-4 border-[#DCDCDC] pl-5"
                          : "text-gray-600 hover:bg-gray-50"
                      } font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal`}
                    >
                      <img src={item.icon} alt={item.label} className="w-5 h-5 mr-3" />
                      {item.label}
                    </button>
                  ) : (
                    <button
                      onClick={() => handleNavigation(item.id)}
                      className={`flex items-center px-6 py-3 text-sm w-full text-left ${
                        isActive
                          ? "text-[#352090] bg-[#F8F7FC] border-l-4 border-[#DCDCDC] pl-5"
                          : "text-gray-600 hover:bg-gray-50"
                      } font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal`}
                    >
                      <img src={item.icon} alt={item.label} className="w-5 h-5 mr-3" />
                      {item.label}
                    </button>
                  )}
                </li>
              )
            })}
          </ul>
        </nav>

        {/* Invite Team button at the bottom */}
        {currentWorkspace && (
          <div className="mt-auto mb-6 px-4">
            <button
              onClick={openInviteModal}
              className="w-full flex items-center justify-center px-4 py-2 text-sm text-[#352090] border border-[#352090] rounded-[8px] hover:bg-[#F8F7FC] transition-colors"
            >
              <img src="/Team_member.svg" alt="Invite Team" className="w-4 h-4 mr-2" />
              Invite Team
            </button>
          </div>
        )}
      </aside>

      {/* Invite User Modal */}
      <InviteUserModal isOpen={isInviteModalOpen} onClose={closeInviteModal} workspaceId={currentWorkspace} />
    </>
  )
}

export default Sidebar
