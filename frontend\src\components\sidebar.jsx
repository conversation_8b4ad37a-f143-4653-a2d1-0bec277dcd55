import { useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useDispatch } from "react-redux"
import { X } from "lucide-react"
import { useWorkspace } from "../hooks/useWorkspace"
import { useNavigation } from "../hooks/useNavigation"
import { addNotification } from "../store/slices/uiSlice"
import { clusterApi } from "../store/api/clusterApi"
import { useLogoutMutation } from "../store/api/authApi"
import { logout as logoutAction } from "../store/slices/authSlice"
import { authApi } from "../store/api/authApi"
import { resetNavigationState } from "../store/slices/navigationSlice"
import ModalPortal from "./ModalPortal";
import InviteUserModal from "../team_invitation/invite-user"

const Sidebar = ({ activePage = "overview" }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { currentWorkspace } = useWorkspace()
  const {
    currentPage,
    isNavigating,
    navigationType,
    navigateToClusters,
    navigateToTrash,
    navigateToSettings,
    syncWithCurrentUrl,
    isPageActive,
    PAGES
  } = useNavigation()
  const [isInviteModalOpen, setInviteModalOpen] = useState(false)
  const [isLogoutModalOpen, setLogoutModalOpen] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  // Logout mutation
  const [logoutMutation] = useLogoutMutation()

  // Sync navigation state with current URL on mount and location changes
  useEffect(() => {
    syncWithCurrentUrl()
  }, [location.pathname, syncWithCurrentUrl])

  const openInviteModal = () => setInviteModalOpen(true)
  const closeInviteModal = () => setInviteModalOpen(false)
  
  const openLogoutModal = () => setLogoutModalOpen(true)
  const closeLogoutModal = () => setLogoutModalOpen(false)

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true)
      console.log("🔄 Starting logout process...")
      
      // Call the logout API
      await logoutMutation().unwrap()
      console.log("✅ Logout API call successful")
      
      // Clear Redux state
      dispatch(logoutAction())
      dispatch(resetNavigationState())
      console.log("✅ Redux state cleared")

      // Clear all RTK Query cache
      dispatch(clusterApi.util.resetApiState())
      dispatch(authApi.util.resetApiState())
      console.log("✅ API cache cleared")
      
      // Clear localStorage
      localStorage.clear()
      console.log("✅ LocalStorage cleared")
      
      // Show success notification
      dispatch(
        addNotification({
          type: "success",
          title: "Logged Out",
          message: "You have been logged out successfully",
        }),
      )
      
      // Navigate to login
      navigate("/login", { replace: true })
      console.log("✅ Navigated to login page")
      
    } catch (error) {
      console.error("❌ Logout failed:", error)
      
      // Even if API call fails, clear local state and redirect
      dispatch(logoutAction())
      dispatch(resetNavigationState())
      dispatch(clusterApi.util.resetApiState())
      dispatch(authApi.util.resetApiState())
      localStorage.clear()
      
      dispatch(
        addNotification({
          type: "error",
          title: "Logout Failed",
          message: "Failed to logout from server, but local session cleared.",
        }),
      )
      
      navigate("/login", { replace: true })
    } finally {
      setIsLoggingOut(false)
      closeLogoutModal()
    }
  }

  // Optimized navigation handler using Redux navigation state
  const handleNavigation = (id) => {
    console.log("🚀 Sidebar Navigation Debug:", {
      id,
      currentWorkspace: currentWorkspace?.id || 'undefined',
      currentPath: location.pathname,
      navigationHookState: {
        currentPage,
        isNavigating,
        navigationType
      }
    })

    if (!currentWorkspace?.id) {
      console.error("❌ Sidebar Navigation Error: No workspace ID available", {
        currentWorkspace,
        location: location.pathname
      })
      // Don't return early - let the navigation hook handle the fallback
    }

    // Use optimized navigation functions for intra-workspace navigation
    switch (id) {
      case "overview":
        console.log("🔄 Sidebar: Calling navigateToOverview()")
        navigateToClusters()
        break
      case "trash":
        console.log("🔄 Sidebar: Calling navigateToTrash()", {
          currentWorkspace: currentWorkspace?.id,
          currentPath: location.pathname,
          navigateToTrash: typeof navigateToTrash
        })
        navigateToTrash()
        break
      case "settings":
        console.log("🔄 Sidebar: Calling navigateToSettings()")
        navigateToSettings()
        break
      default:
        console.warn("Unknown navigation id:", id)
    }
  }

  const menuItems = [
    { id: "overview", label: "Overview", icon: "/Overview.svg" },
    { id: "trash", label: "Trash", icon: "/Trash2.svg" },
    { id: "settings", label: "Settings", icon: "/setting.svg" },
    { id: "logout", label: "Logout", icon: "/Logout.svg", onClick: openLogoutModal },
  ]

  // Helper function to determine if a menu item is active
  const isMenuItemActive = (itemId) => {
    // Use Redux navigation state for more accurate active state detection
    return isPageActive(PAGES[itemId.toUpperCase()]) || activePage === itemId
  }

  return (
    <>
      <aside className="w-[200px] border-r border-gray-200 h-[calc(100vh-100px)] bg-white fixed top-[100px] left-0 z-20 flex flex-col">
        <nav className="flex-1">
          <ul className="space-y-1">
            {menuItems.map((item) => {
              const isActive = isMenuItemActive(item.id)
              return (
                <li key={item.id}>
                  <button
                    onClick={item.onClick ? item.onClick : () => handleNavigation(item.id)}
                    className={`flex items-center px-6 py-3 text-sm w-full text-left transition-colors duration-200 ${
                      isActive
                        ? "text-[#352090] bg-[#F8F7FC] border-l-4 border-[#DCDCDC] pl-5"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    } font-['Noto Sans'] font-normal text-[14px] leading-[20px] tracking-normal`}
                    aria-current={isActive ? "page" : undefined}
                    aria-label={`Navigate to ${item.label}`}
                    type="button"
                  >
                    <img
                      src={item.icon}
                      alt=""
                      className={`w-5 h-5 mr-3 transition-all ${isActive ? 'filter-purple' : ''}`}
                      style={{
                        filter: isActive ? 'invert(47%) sepia(92%) saturate(1466%) hue-rotate(233deg) brightness(99%) contrast(101%)' : 'none'
                      }}
                      aria-hidden="true"
                    />
                    {item.label}
                  </button>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* Invite Team button at the bottom */}
        {currentWorkspace && (
          <div className="p-4">
            <button
              onClick={openInviteModal}
              className="w-full h-[44px] flex items-center justify-center text-sm text-[#352090] border border-[#ECE8FF] rounded-[8px] hover:bg-[#F8F7FC] transition-colors"
              style={{
                padding: "12px 30px",
                gap: "10px",
                opacity: 1,
                backgroundColor: "#ECE8FF",
              }}
            >
              <img src="/Team_member.svg" alt="Invite Team" className="w-4 h-4" />
              <span
                style={{
                  font: "Noto Sans",
                  fontWeight: "400",
                  fontSize: "14px",
                  lineHeight: "20px",
                  letterSpacing: "0%",
                  textAlign: "center",
                  color: "#352090",
                }}
              >
                Invite Team
              </span>
            </button>
          </div>
        )}
      </aside>

      {/* Invite User Modal */}
      {isInviteModalOpen && (
        <ModalPortal>
          <InviteUserModal
            isOpen={isInviteModalOpen}
            onClose={closeInviteModal}
            workspaceId={currentWorkspace}
          />
        </ModalPortal>
        )}

      {/* Logout Confirmation Modal */}
      {isLogoutModalOpen && (
        <ModalPortal>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Confirm Logout</h3>
                <button
                  onClick={closeLogoutModal}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <p className="text-gray-600 mb-6">Are you sure you want to logout? You will need to login again to access your account.</p>
              
              <div className="flex justify-end gap-3">
                <button
                  onClick={closeLogoutModal}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900 font-medium"
                  disabled={isLoggingOut}
                >
                  Cancel
                </button>
                <button
                  onClick={handleLogout}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isLoggingOut}
                >
                  {isLoggingOut ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Logging out...
                    </div>
                    
                  ) : (
                    'Logout'
                  )}
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </>
  )
}

export default Sidebar
