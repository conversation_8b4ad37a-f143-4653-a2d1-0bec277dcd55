import { useState } from "react"
import { Menu } from "lucide-react"
import { Link, useNavigate } from "react-router-dom"
import { useDispatch } from "react-redux"
import { useGetUserProfileQuery } from "../store/api/authApi"
import { useWorkspace } from "../hooks/useWorkspace"
import { addNotification } from "../store/slices/uiSlice"
import LogoComponent from "./logo-component"
import WorkspaceDropdown from "./workspace-dropdown"

const Navbar = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { currentWorkspace } = useWorkspace()

  // Fetch user profile
  const { data: userProfile, isLoading: profileLoading, error: profileError } = useGetUserProfileQuery()

  // Extract user info with fallbacks
  const adminName = userProfile?.full_name || userProfile?.email || "User"
  const adminEmail = userProfile?.email || ""
  const adminInitials = adminName
    ? adminName
        .split(" ")
        .map((part) => part[0])
        .join("")
        .substring(0, 2)
        .toUpperCase()
    : "U"

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  // Get the correct path for each menu item
  const getItemPath = (id) => {
    if (id === "overview") {
      // 🔧 FIX: Navigate to onboarding check route instead of directly to clusters
      // This ensures proper onboarding validation before accessing clusters
      return currentWorkspace ? `/workspace/${currentWorkspace}` : "/"
    }
    if (id === "trash") {
      if (!currentWorkspace) {
        console.error("No workspace ID available for trash navigation")
        return "/"
      }
      return `/workspace/${currentWorkspace}/trash`
    }
    return `/${id}`
  }

  const handleLogout = async () => {
    try {
      // Clear any stored data
      localStorage.clear()

      dispatch(
        addNotification({
          type: "success",
          title: "Logged Out",
          message: "You have been logged out successfully",
        }),
      )

      navigate("/login")
    } catch (error) {
      console.error("Logout failed:", error)
      dispatch(
        addNotification({
          type: "error",
          title: "Logout Failed",
          message: "Failed to logout. Please try again.",
        }),
      )
    }
  }

  if (profileLoading) {
    return (
      <header className="w-full h-[100px] border-b border-gray-200 bg-white z-10 flex-shrink-0">
        <div className="h-full flex items-center justify-between px-4 md:pl-0">
          <div className="flex items-center py-6">
            <div className="md:pl-[69px] pl-6">
              <LogoComponent width={40} height={40} />
            </div>
            <h1 className="text-[#352090] text-xl font-semibold ml-3">Tracker</h1>
          </div>
          <div className="pr-4">
            <div className="animate-pulse bg-gray-200 h-10 w-32 rounded"></div>
          </div>
        </div>
      </header>
    )
  }

  return (
    <header className="w-full h-[100px] border-b border-gray-200 bg-white z-10 flex-shrink-0">
      <div className="h-full flex items-center justify-between px-4 md:pl-0">
        {/* Left side - Logo and Tracker text */}
        <div className="flex items-center py-6">
          {/* Mobile menu button - only visible on small screens */}
          <button className="md:hidden mr-2" onClick={toggleMobileMenu}>
            <Menu className="w-6 h-6 text-gray-600" />
          </button>

          <div className="md:pl-[69px] pl-6">
            <LogoComponent width={40} height={40} />
          </div>
          <h1 className="text-[#352090] text-xl font-semibold ml-3">Tracker</h1>
        </div>

        {/* Right side - Workspace Dropdown */}
        <div className="pr-4">
          <WorkspaceDropdown adminName={adminName} adminEmail={adminEmail} adminInitials={adminInitials} />
        </div>
      </div>

      {/* Mobile sidebar - only visible when menu is open on small screens */}
      {mobileMenuOpen && (
        <div className="md:hidden fixed inset-0 z-50 bg-white">
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <LogoComponent width={40} height={40} />
              <h1 className="text-[#352090] text-xl font-semibold">Tracker</h1>
            </div>
            <button onClick={toggleMobileMenu} className="text-gray-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <nav className="p-4">
            <ul className="space-y-4">
              <li>
                <Link to={getItemPath("overview")} className="flex items-center py-2 text-[#352090]">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                    />
                  </svg>
                  Overview
                </Link>
              </li>
              <li>
                <Link to={getItemPath("trash")} className="flex items-center py-2 text-gray-600">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                  Trash
                </Link>
              </li>
              <li>
                <Link to={getItemPath("settings")} className="flex items-center py-2 text-gray-600">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  Settings
                </Link>
              </li>
              <li>
                <button onClick={handleLogout} className="flex items-center py-2 text-gray-600 w-full text-left">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  Logout
                </button>
              </li>
            </ul>
          </nav>

          {/* Mobile Workspace Info */}
          <div className="mt-auto p-4 border-t border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-[#352090] flex items-center justify-center text-white text-sm font-medium">
                {adminInitials}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{adminName}</p>
                <p className="text-xs text-gray-500 truncate">{adminEmail}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}

export default Navbar
