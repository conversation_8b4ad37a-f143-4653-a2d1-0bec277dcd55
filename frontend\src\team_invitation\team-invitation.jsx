import { useState, useEffect } from "react"
import { useNavigate, useParams, useLocation } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useSendTeamInvitationMutation } from "../store/api/teamApi"
import { setCurrentStep } from "../store/slices/onboardingSlice"
import { addNotification } from "../store/slices/uiSlice"
import LogoComponent from "../components/logo-component"
import TeamInvitationImage from "../assets/team_invitation.png"

const TeamInvitation = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { workspace_id } = useParams()
  const location = useLocation()

  // Redux state
  const { onboardingState, userData } = useSelector((state) => state.onboarding)
  const [sendTeamInvitation, { isLoading }] = useSendTeamInvitationMutation()

  // Local state
  const [invites, setInvites] = useState([{ email: "", role: "Admin" }])
  const [validationErrors, setValidationErrors] = useState({})

  // Get the passed state data (fallback to Redux state)
  const stateOnboardingState = location.state?.onboarding_state || onboardingState
  const stateUserData = location.state?.user_data || userData

  useEffect(() => {
    // Update onboarding step
    dispatch(setCurrentStep("team_invitation"))
  }, [dispatch])

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleEmailChange = (index, value) => {
    const newInvites = [...invites]
    newInvites[index].email = value
    setInvites(newInvites)

    // Clear error for this field when user starts typing
    if (validationErrors[`email-${index}`]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[`email-${index}`]
        return newErrors
      })
    }
  }

  const handleRoleChange = (index, value) => {
    const newInvites = [...invites]
    newInvites[index].role = value
    setInvites(newInvites)
  }

  const addNewInviteField = () => {
    setInvites([...invites, { email: "", role: "Admin" }])
  }

  const handleSkip = () => {
    dispatch(setCurrentStep("google_auth"))
    navigate(`/google_auth/${workspace_id}`, {
      state: {
        onboarding_state: stateOnboardingState,
        workspace_id: workspace_id,
        user_data: stateUserData,
      },
    })
  }

  const validateForm = () => {
    const newErrors = {}
    const validInvites = []

    invites.forEach((invite, index) => {
      if (invite.email.trim() === "") return // Skip empty emails

      if (!validateEmail(invite.email)) {
        newErrors[`email-${index}`] = "Please enter a valid email address"
      } else {
        validInvites.push(invite)
      }
    })

    setValidationErrors(newErrors)
    return { isValid: Object.keys(newErrors).length === 0, validInvites }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    const { isValid, validInvites } = validateForm()

    if (!isValid) {
      return
    }

    if (validInvites.length === 0) {
      dispatch(
        addNotification({
          type: "warning",
          title: "No Invitations",
          message: "Please enter at least one valid email address",
        }),
      )
      return
    }

    try {
      // Extract emails and roles
      const emails = validInvites.map((invite) => invite.email)
      const role = validInvites[0].role // Assuming all invites have the same role, or use the first one

      // Send invitation(s) - backend handles both single and multiple
      const result = await sendTeamInvitation({
        workspaceId: workspace_id,
        email: emails.length === 1 ? emails[0] : emails, // Send as string if single, array if multiple
        role: role,
      }).unwrap()

      // Handle the response
      if (result.status === "success" && result.results) {
        const successCount = result.results.filter((r) => r.status === "success").length
        const errorCount = result.results.filter((r) => r.status === "error").length

        if (successCount > 0) {
          dispatch(
            addNotification({
              type: "success",
              title: "Invitations Sent!",
              message: `Successfully sent ${successCount} invitation(s)`,
            }),
          )
        }

        if (errorCount > 0) {
          const errorMessages = result.results
            .filter((r) => r.status === "error")
            .map((r) => `${r.email}: ${r.message}`)
            .join(", ")

          dispatch(
            addNotification({
              type: "warning",
              title: "Some Invitations Failed",
              message: errorMessages,
              persistent: true,
            }),
          )
        }

        // If at least one invitation was successful, proceed to next step
        if (successCount > 0) {
          dispatch(setCurrentStep("google_auth"))

          setTimeout(() => {
            navigate(`/google_auth/${workspace_id}`, {
              state: {
                onboarding_state: stateOnboardingState,
                workspace_id: workspace_id,
                user_data: stateUserData,
              },
            })
          }, 1500)
        }
      } else {
        throw new Error("Unexpected response format")
      }
    } catch (error) {
      console.error("Failed to send invitations:", error)

      dispatch(
        addNotification({
          type: "error",
          title: "Invitation Failed",
          message: error.data?.detail || error.message || "Failed to send invitations. Please try again.",
        }),
      )
    }
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen w-full font-noto overflow-hidden">
      {/* Form Section */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10 overflow-auto">
        <div className="w-full max-w-[500px]">
          {/* Logo */}
          <div className="mb-6">
            <LogoComponent />
          </div>

          {/* Heading */}
          <h1 className="text-[30px] font-bold mb-2">Invite Team Member</h1>

          <p className="text-[14px] text-black mb-8">
            Enhance your team's collaboration and efficiency by inviting new members to your workspace. You can invite
            multiple team members at once and assign them different roles.
          </p>

          {/* Form */}
          <form onSubmit={handleSubmit} className="w-full">
            <p className="text-[16px] font-medium mb-4">Invite people to collaborate</p>

            {invites.map((invite, index) => (
              <div key={index} className="mb-4">
                <div className="flex items-center gap-2">
                  <div className="flex-grow">
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      value={invite.email}
                      onChange={(e) => handleEmailChange(index, e.target.value)}
                      className={`w-full h-[48px] px-3 py-2 text-sm bg-white border ${
                        validationErrors[`email-${index}`] ? "border-red-500" : "border-[#D9D9D9]"
                      } rounded-l-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090]`}
                    />
                    {validationErrors[`email-${index}`] && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors[`email-${index}`]}</p>
                    )}
                  </div>
                  <select
                    value={invite.role}
                    onChange={(e) => handleRoleChange(index, e.target.value)}
                    className="h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] border-l-0 rounded-r-[8px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                  >
                    <option value="Admin">Admin</option>
                    <option value="Member">Member</option>
                    <option value="Viewer">Viewer</option>
                  </select>
                </div>
              </div>
            ))}

            <button
              type="button"
              onClick={addNewInviteField}
              className="text-[14px] text-[#352090] hover:text-[#2a1a70] mb-6 flex items-center gap-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
              Add another email
            </button>

            <div className="flex justify-between items-center mt-auto pt-8">
              <button type="button" className="text-[14px] text-gray-500 hover:text-gray-700" onClick={handleSkip}>
                Skip for now
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-6 h-[48px] bg-[#352090] text-white text-sm rounded-[8px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Sending...
                  </>
                ) : (
                  "Send Invitations"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Image Section */}
      <div className="hidden md:block md:w-1/2 bg-black">
        <img
          src={TeamInvitationImage || "/placeholder.svg"}
          alt="Team invitation illustration"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  )
}

export default TeamInvitation
