import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useGetUserProfileQuery } from "../store/api/authApi"

const SessionRestoration = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false)
  const { isAuthenticated } = useSelector((state) => state.auth)

  // Try to get user profile to check if session is valid
  // This endpoint is already working correctly in the current authentication flow
  const {
    data: userProfile,
    isLoading: profileLoading,
    error: profileError,
  } = useGetUserProfileQuery(undefined, {
    // Don't skip - always try to get user profile to validate session
    skip: false,
    retry: 1,
  })

  useEffect(() => {
    console.log("🔍 SessionRestoration effect:", {
      profileLoading,
      profileError: profileError?.status,
      userProfile: !!userProfile,
      isAuthenticated,
      isInitialized
    })

    if (profileLoading) {
      // Still loading, don't initialize yet
      return
    }

    // Profile fetch completed (either success or failure)
    // The auth slice will automatically handle the state updates through RTK Query matchers
    if (!isInitialized) {
      if (userProfile) {
        console.log("✅ Valid session restored via user profile:", userProfile.email)
      } else if (profileError) {
        console.log("❌ Session restoration failed:", profileError.status)
      }
      setIsInitialized(true)
    }
  }, [profileLoading, userProfile, profileError, isAuthenticated, isInitialized])

  // Show loading screen while checking session
  if (!isInitialized || profileLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090] mx-auto"></div>
          <p className="mt-4 text-gray-600">Restoring your session...</p>
        </div>
      </div>
    )
  }

  return children
}

export default SessionRestoration
